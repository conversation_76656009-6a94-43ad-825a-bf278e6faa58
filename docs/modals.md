## Universal Modal

Použitie

```bladehtml

<universal-modal
        modal-window-classes="w-1/2"
>
    <template #button>
        Otvor modal
    </template>
    <template #header>
        <h1 class="text-2xl">Toto je moj testovaci header</h1>
    </template>
    <template #body>
        <div class="flex flex-col w-full h-1/2 items-center">
            Toto je moj text na stred modalu

        </div>
        <div class="flex flex-col items-end">
            <button aria-label="close" class="button w-40">toto zavrie modal tiez</button>
        </div>
    </template>
</universal-modal>
```

#### Atribúty universal-modal ####

* **modal-window-classes**: doplňujúce classy pre modalové okno, vhodné najmä na definovanie šírky a výšky
* **modal-position-classes**: používať na umiestnenie modalového okna využitím flexu, default je
  `items-center justify-center`
* **buttonClasses**: sl<PERSON><PERSON>i na naformátovanie buttonu, ktorým sa otvára modal, default je `button-blue-outline`
* **name**: pridáva prefix idčkam v modale

#### Využitie slotov ####

Používajú sa porostredníctvom element template s hashtagom názvu slotu. Universal modal má tri sloty: `header`, `body`
alebo `button`.
Button slot vkladá iba text do buttonu, v rámci ostatných si môžeme naštýlovať už všetko ako potrebujeme (pozri príklad
vyššie)

#### Custom close button ####

okrem close buttonu v pravo hore si vieme vytvoriť aj vlastný close button prakticky kdekoľvek. jediné čo treba, tak
pridať elementu `aria-label="close"` (pozri príklad vyššie)