## Universal filter

Základn<PERSON> použitie filtra v blade

 ```bladehtml

<universal-filter
        :filter-inputs="{{json_encode($filterInputs)}}"
        :filter-data="{{json_encode($filterData)}}"
></universal-filter>
```

<PERSON><PERSON> je vidie<PERSON>, sú potrebné minimálne 2 premenné a to sú inputy a dáta.

### Filter Inputs

Vytvárame v kontrolleri ako array, napríklad

```php 
 $filterInputs = [
            [
                'attribute' => 'first_name',
                'label' => __('it.persons.user.list.full_name'),
                'icon' => '',
                'placeholder' => 'Jan<PERSON>'
            ];
```

**Atribúty pre filterInputs**

* **attribute**: povinný, je názov atribútu v html tabulke, keď chceme vyhľadávať vo vzťahoch, používame názov vzťahu s
  tým že za poslednou bodkou je názov atribútu z tabuľky, napríklad `user.positions.name`
* **label**: pov<PERSON><PERSON>, je názov nad inputom
* **icon**: nepovinný, je font ikonka, ktorú zobrazí pred názvom inputu ale aj v labeli nad filtrom, pokiaľ je daný
  input použitý na filtrovanie
* **options**: je povinný v prípade, že chceme vyhľadávať z predom zadefinovaných hodnôt cez select2 input, môže byť
  array alebo kolekcia
* **searchProperty**: je povinný v prípade použitia atribútu `options`, keď nie je options jednoduchý array/kolekcia (tj
  má ďašie svoje atribúty ako napr id, name a pod...), určuje podľa čoho má v kolekcii/arrayi select2 vyhľadávať
* **inputProperty**: je povinný v prípade použitia atribútu `options`,keď nie je options jednoduchý array/kolekcia (tj
  má ďašie svoje atribúty ako napr id, name a pod...), určuje čo má inputu priradiť ako value pri odoslaní requestu
* **customAction**: nepovinný, vyradí input z bežného filtrovacieho procesu čiže z príkladu $filterInputs vyššie by
  filer request vypadal `$request['filter']['akcia zvolena v inpute napr like'][first_name]` ale pokiaľ by sme nastavili
  vo filterInputs `'customAction' => 'custom'`, v requeste by to potom vypadalo
  `$request[filter]['akcia zvolena v inpute napr like'][custom][first_name]`. Je to preto, aby sme vedeli pracovať s
  neštandartnými filtrovacími hodnotami ktoré bežný filter nezvláda.
* **disableLogic**: nepovinný, boolean, `true` hodnota vypne výber logickej operácie používateľom, pokiaľ je vypnutá
  logika, input vždy vracia 'equal' akciu
* **disableAllOption**: nepovinný, boolean a default je `false`, vypina moznost `All/Všetko` v select inputoch
* **customActions**: nepovinný, vieme tak obmedziť možnosti akcií použiteľneých pre daný input... treba vždy napísať
  názov logiky ako array key a zmanienko ktoré má byť použité ako hodnota. Dostupné akcie sú:

```php 
'allowedActions' => [
                    'like' => "~",
                    'notLike' => "!~",
                    'equal' => "=",
                    'notEqual' => "!=",
                    'lessThan' => "<",
                    'greaterThan' => ">",
                    'isNull' => "x",
                    'isNotNull' => "!x",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
```

### Filter Data

Na správne vytvorenie dát pre filter potrebujeme využiť `FilterService`. V základe a v drvivej väčšine bude stačiť, keď
sa do jej metódy `processAllFilterDataForUniversalFilterComponent` vloží iba input filter z requestu.
Všetko ostatné si filter service dokáže spracovať a pripraviť sama.

Príklad:

```php 
$filterService = new FilterService();
        $filterData = $filterService->processAllFilterDataForUniversalFilterComponent(
            $request->input('filter')
        );
```

Ako druhý atribút metódy si vieme zadefinovať custom meno filtra (default je cesta k URL bez base path a get atribútov s
nahradenými lomítkami za podtržítka).

Tretí atribút metódy mení názov pre ukladanie záložiek v databáze, default je filterName.

Štvrtý je posledný možný atribút je boolean, tu sa nastavuje, či chceme alebo nechceme povoliť možnosť používať záložky
pre daný filter, default je zatiaľ nastavený na true

### Filtrovanie modelov

Pokiaľ chceme využívať filtrovanie modelu, potrebujeme mu do use pridať `IsFilterableTrait`. Ten zabezpečí, že vieme pri
Query builderi volať `->filter()`, do ktorého vložíme dáta pripravené z `FilterService`

Napríklad:

```php
$filterService = new FilterService();
$filterData = $filterService->processAllFilterDataForUniversalFilterComponent(
    $request->input('filter')
);
$users = User::query()->filter($filterData['filter'])
    ->orderBy('last_name')
    ->orderBy('first_name')
    ->paginate(20);
```
