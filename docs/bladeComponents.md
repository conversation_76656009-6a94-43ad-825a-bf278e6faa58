##  Simple data table
Na zobrazenie jednoduchých číselníkových tabuliek.

Príklad použitia v blade:
```bladehtml
<x-simple-data-table :data-for-table="$sections->getCollection()" :col-options="$colOptions" :pagination-links="$sections->links()"></x-simple-data-table>
```
### Komponent má atribúty:
* **data-for-table**: kolekcia dát ktorú m<PERSON> zobraziť
* **col-options**: array nastavení (popísané nižšie)
* **pagination-links**: linky na pagináciu (voliteľný parameter, len ak je paginácia na query použitá)

### col-options:
Tento array slúži na nastavenie stĺpcov a ciest k dátam/actions buttonom 

Príklad:
```php
$colOptions = [
            __('hr.organisation.section.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.section.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.section.name_cs') => ['type' => 'value', 'path_in_collection' => 'name_cs'],
            __('hr.organisation.section.name_sk') => ['type' => 'value', 'path_in_collection' => 'name_sk'],
            __('hr.organisation.section.name_hu') => ['type' => 'value', 'path_in_collection' => 'name_hu'],
            __('hr.organisation.section.action') => ['type' => 'actions', 'base_path' => route('hr.organisation.sections.index'), 'available_actions' => [ 'show' => true,'edit' => true]],
        ];
```
#### Atribúty pre col-options:
* **array key** je vždy názov stĺpca tabuľky
* **type**: `value`, pokiaľ majú byť bunky stĺpca konkrétnou hodnotou z kolekcie,`many_values` pokiaľ chceme vypísať atribút z toMany vzťahu,  `yesno` pokiaľ chceme zobraziť farebné rozlíšenie áno/nie, alebo `actions`, pokiaľ sú v stĺpci akčné tlačítka
* **path_in_collection**: povinný pri `type => value`,`type => many_values` a `type => yesno`, zadáva sa tam cesta k atribútu v kolekcii, ktorý má v príslušnom stĺpci vypisovať. Cesta do relations sa oddeľuje bodkou s tým, že posledný je názov atribútu (prípadne toMany vzťahu v prípade vzťahu many_values). Napr `organisation.owner.name`
* **base_path**: povinný pri `type => actions`, zadáva sa tam základná cesta pre akčné tlačítka (tj odkiaľ bude vystkadávať tlačítko napr k show alebo delete ceste), zvyčajne rovnaká stránka na akej sa nachádza daný komponent
* **available_actions**:  povinný pri `type => actions`, zadávajú sa tam metódy (ako názov atribútu), ktoré sa majú nachádzať v danom stĺpci ako tlačítka s bool hodnotou, či ho daný user môže vidieť alebo nie (v controlleri môže byť zložitejšia logika napr na kontrolu roles/permissions a pod)
* **get_attribute** je povinný pri type `type => many_values` určuje, ktorý atribút z toMany kolekcie má vypísať, napríklad pri `path_in_collection => 'position.sections'` bude `get_attribute => 'name'` v prípade, že chcem aby sa v stĺpci vypisoval názov sekcií
* **custom_classes** nepovinné, môžnosť priradiť ďalšie classy pre konkrétnu celú bunku (<td>) a zmeniť tak napríklad hrúbku textu v ňom vypísanú
