##  Universal select 2 
```bladehtml
<universal-select2
search-property="full_name"
input-property="id"
name="full_name"
:options="{{json_encode($users->getCollection())}}"
>
</universal-select2>
```
použiteľné atribúty: 
* **name**: názov inputu ktorý bude prenášaný v requeste
* **selected**: input-property, ktorý má byť "pred selektnutý"
* **search-property**: názov atribútu, podľa ktorého sa má vyhľadávať z options (napríklad full_name pri users)
* **input-property**: názov atribútu, ktorý má byť ako input value (napríklad id)
* **options**: Kolekcia/Array možností pre input, v prípade použitia z blade súboru musí byť s dvojbodkou (tj :options) a hodnoty musia byť json_encode (takže napríklad :options="json_encode($users)")
* **all-option**: boolean, sl<PERSON><PERSON>i na to aby buď pridalo alebo nepridalo 'All' možnosť na začiatok inputu, takisto ako options, pri použití v blade musí byť s dvojbodkou a json encodnute
* **disable-logic**: boolean, slúži iba na zmenu zaoblednia inputu, využíva sa v UniversalFilterInput-e kde môžu byť pred inputom logické operácie a teda zmení zaoblenie ľavých rohov
* **custom-classes**: string, slúži na pridanie custom classov, smeruje to na <select ...> input takže vhodné napr na border-red pri errore

##  Universal multiple select 2 
```bladehtml
<universal-multiple-select2
search-property="full_name"
input-property="id"
name="full_name"
:options="{{json_encode($users->getCollection())}}"
>
</universal-multiple-select2>
```

* **name**: názov inputu ktorý bude prenášaný v requeste
* **selected**: array celých objektov, ktoré majú byť selected
* **search-property**: názov atribútu, podľa ktorého sa má vyhľadávať z options (napríklad full_name pri users)
* **input-property**: názov atribútu, ktorý má byť ako input value (napríklad id)
* **options**: Kolekcia/Array možností pre input, v prípade použitia z blade súboru musí byť s dvojbodkou (tj :options) a hodnoty musia byť json_encode (takže napríklad :options="json_encode($users)")
* **select-all-option**: boolean, slúži na to aby buď pridalo alebo nepridalo 'All' možnosť na začiatok inputu, takisto ako options, pri použití v blade musí byť s dvojbodkou a json encodnute
* **disable-logic**: boolean, slúži iba na zmenu zaoblednia inputu, využíva sa v UniversalFilterInput-e kde môžu byť pred inputom logické operácie a teda zmení zaoblenie ľavých rohov
* **custom-classes**: string, slúži na pridanie custom classov, smeruje to na <select ...> input takže vhodné napr na border-red pri errore
