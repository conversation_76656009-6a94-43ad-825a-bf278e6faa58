<?php

use App\Http\Controllers\Api\V1\Employee\Remunerations\ProposalController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'remunerations', 'as' => 'remunerations.'], function () {
    Route::apiResource('proposals', ProposalController::class);
    Route::post(
        'get-all-necessary-dynamic-data/{getUsersForAddMoreUsers?}',
        \App\Http\Controllers\Api\V1\Employee\Remunerations\Table\GetAllNecessaryDynamicDataController::class
    )
        ->name(
            'get-all-necessary-dynamic-data'
    );
    Route::post('get-all-necessary-static-data', \App\Http\Controllers\Api\V1\Employee\Remunerations\Table\GetAllNecessaryStaticDataController::class)
        ->name(
            'get-all-necessary-static-data'
        );
    Route::post(
        'get-proposal-logs/{proposal?}/{limit?}',
        \App\Http\Controllers\Api\V1\Employee\Remunerations\Table\GetAllLogsForProposalController::class
    )
        ->name(
            'get-proposal-logs'
        );
    Route::post('get-specific-proposal', \App\Http\Controllers\Api\V1\Employee\Remunerations\Table\GetSpecificProposalController::class)->name(
        'get-specific-proposal'
    );
});

