<?php

use App\Http\Controllers\Employee\ProfileController;
use App\Http\Controllers\Employee\Remuneration\ProposalController;
use App\Http\Controllers\Employee\Remuneration\RemunerationController;
use App\Http\Controllers\Employee\Remuneration\SalaryController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'profile', 'as' => 'profile.'], function () {
    Route::get('/', [ProfileController::class, 'edit'])->name('edit');
    Route::patch('/', [ProfileController::class, 'update'])->name('update');
    Route::delete('/', [ProfileController::class, 'destroy'])->name('destroy');
});

Route::group(['prefix' => 'remunerations', 'as' => 'remunerations.'], function () {
    Route::get('/', [RemunerationController::class, 'index'])->name('index');

    Route::group(['prefix' => 'proposals', 'as' => 'proposals.'], function () {
        Route::post('save-approver', [ProposalController::class, 'saveApprover'])->name('save_approver');
        Route::get('list/{tab?}', [ProposalController::class, 'listIndex'])->name('index.list');
        Route::get('export/{tab?}', [ProposalController::class, 'export'])->name('export');
        Route::get('table', [ProposalController::class, 'tableIndex'])->name('index.table');
        Route::post('change-status-for-multiple', [ProposalController::class, 'changeStatusForMultiple'])->name('change_status_for_multiple');
        Route::get('{proposal}/forward/{approver}', [ProposalController::class, 'forward'])->name('forward');
        Route::get('{proposal}/reject', [ProposalController::class, 'reject'])->name('reject');
        Route::get('{proposal}/approve', [ProposalController::class, 'approve'])->name('approve');
        Route::get('{proposal}/usurp', [ProposalController::class, 'usurp'])->name('usurp');
        Route::get('{proposal}/withdraw', [ProposalController::class, 'withdraw'])->name('withdraw');
        Route::get('{proposal}/rollback', [ProposalController::class, 'rollback'])->name('rollback');
        Route::get('{proposal}/change-is-paid-via-agreement', [ProposalController::class, 'changeIsPaidViaAgreement'])->name('change_is_paid_via_agreement');
        Route::get('{proposal}/change-payment-status', [ProposalController::class, 'changePaymentStatus'])->name('change_payment_status');
    });
    Route::resource('proposals', ProposalController::class)->except(['destroy']);

    Route::group(['prefix' => 'salaries', 'as' => 'salaries.'], function () {
        Route::get('/', [SalaryController::class, 'index'])->name('index');
        Route::get('/{contract}', [SalaryController::class, 'history'])->name('history')->where(['contract' => '[0-9]+']);
        Route::get('plans', [SalaryController::class, 'plans'])->name('plans.index');
        Route::post('plans/store', [SalaryController::class, 'createPlan'])->name('plans.store');
    });
});
