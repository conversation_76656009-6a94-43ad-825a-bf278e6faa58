<?php

Route::get('change-localization', \App\Http\Controllers\Global\ChangeLocalizationController::class)->name('change_localization');

Route::group(['prefix' => 'filter', 'as' => 'filter.'], function () {
    Route::get('get-default-data/{translationName?}', \App\Http\Controllers\Global\Filter\GetDefaultDataController::class)->name('get_default_data');
    Route::delete('delete-filter-preset/{userFilterPreset}', \App\Http\Controllers\Global\Filter\DeleteFilterBookmarkController::class)->name(
        'delete-filter-bookmark'
    );
    Route::get(
        'reset-filter-attribute/{filterName}/{filterAttribute?}/{filterAttributeValue?}',
        \App\Http\Controllers\Global\Filter\ResetFilterAttributeController::class
    )->name('reset-filter-attribute');
    Route::get('reset-filter/{filterName}', \App\Http\Controllers\Global\Filter\ResetFilterController::class)->name('reset-filter');
    Route::get('save-filter-preset/{filterName?}/{userFilterName?}/{filter?}', \App\Http\Controllers\Global\Filter\SaveFilterBookmarkController::class)->name(
        'save-filter-bookmark'
    );
});
