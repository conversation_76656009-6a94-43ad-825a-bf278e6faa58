<?php

use App\Http\Controllers\IT\Person\UserController;
use App\Models\RaP\Permission;
use Illuminate\Support\Facades\Route;

Route::view('/', 'it.index')->name('index');

Route::group(['prefix' => 'persons', 'as' => 'persons.'], function () {
    Route::group(['prefix' => 'users', 'as' => 'users.'], function () {
        Route::get('/', [UserController::class, 'index'])->name('index');
        Route::get('/{user}/{tab?}', [UserController::class, 'show'])->name('show');

        Route::group(['middleware' => 'can:'. Permission::IT_CAN_ASSIGN_ROLES], function(){
            Route::post('/{user}/role', [UserController::class, 'assignRole'])->name('assign_role');
            Route::post('/{user}/permission', [UserController::class, 'assignPermission'])->name('assign_permission');

            Route::delete('/{user}/role/{role}', [UserController::class, 'destroyRole'])->name('destroy_role');
            Route::delete('/{user}/permission/{permission}', [UserController::class, 'destroyPermission'])->name('destroy_permission');
        });
    });
});
