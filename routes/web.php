<?php

use App\Models\RaP\Permission;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return to_route(auth()->check() ? 'dashboard' : 'login');
});

Route::middleware('auth')->group(function () {
    Route::view('dashboard', 'employee.dashboard')->name('dashboard');

    Route::group(['prefix' => 'employee', 'as' => 'employee.'], function () {
        require __DIR__.'/web/employee.php';
    });

    Route::group(['prefix' => 'global', 'as' => 'global.'], function () {
        require __DIR__.'/web/global.php';
    });

    Route::group(['middleware' => \App\Http\Middleware\SecondStepVerificationMiddleware::class], function () {
        Route::group(['prefix' => 'hr', 'as' => 'hr.', 'middleware' => 'can:'.Permission::HR_ADMINISTRATION_ACCESS], function () {
            require __DIR__.'/web/hr.php';
        });

        Route::group(['prefix' => 'it', 'as' => 'it.', 'middleware' => 'can:'.Permission::IT_ADMINISTRATION_ACCESS], function () {
            require __DIR__.'/web/it.php';
        });
    });
});

require __DIR__.'/web/auth.php';

if (app()->isLocal()) {
    if (is_file(__DIR__.'/local.php')) {
        require __DIR__.'/local.php';
    }
}
