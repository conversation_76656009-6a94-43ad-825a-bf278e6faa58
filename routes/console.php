<?php

use App\Console\Commands\DataSync\CdbDataSyncCommand;
use App\Console\Commands\DataSync\Hrms1DataSyncCommand;
use App\Console\Commands\DataSync\MatrixCentreBudgetsLoaderCommand;
use App\Console\Commands\Employee\Remuneration\CreateSalaryProposalsFromPlansCommand;
use App\Console\Commands\Employee\Remuneration\PendingProposalsSendNotificationCommand;
use App\Console\Commands\HR\Remuneration\ApprovedProposalContractAssignmentCommand;
use App\Console\Commands\HR\Remuneration\ApprovedProposalsNotificationCommand;
use Illuminate\Support\Facades\Schedule;

/*** SYNC ***/
Schedule::command(CdbDataSyncCommand::class)->dailyAt('05:13');
Schedule::command(Hrms1DataSyncCommand::class)->dailyAt('05:17');
Schedule::command(MatrixCentreBudgetsLoaderCommand::class)->dailyAt('07:13');

/*** REMUNERATION PROPOSALS ***/
Schedule::command(CreateSalaryProposalsFromPlansCommand::class)->monthlyOn(25, '07:09');
Schedule::command(ApprovedProposalsNotificationCommand::class)->dailyAt('07:11');
Schedule::command(PendingProposalsSendNotificationCommand::class)->dailyAt('07:16');
Schedule::command(ApprovedProposalContractAssignmentCommand::class)->everyTwoHours('7');
