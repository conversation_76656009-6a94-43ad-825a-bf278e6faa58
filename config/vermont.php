<?php

use App\Enums\Remuneration\ProposalSubType;

return [
    'developer_notification_emails' => [
        '<EMAIL>',
    ],

    'available_localizations' => explode(',', env('AVAILABLE_LOCALIZATIONS', 'cs,sk,hu')),

    'ldap_auth' => [
        'master_server' => [
            'url' => env('LDAP_MASTER_URL', 'ldaps://mail.vermontservices.eu'),
            'port' => env('LDAP_MASTER_PORT', 636),
            'admin_name' => env('LDAP_MASTER_ADMIN_NAME', 'cn=rouser,dc=intranet,dc=vermont,dc=eu'),
            'admin_pass' => env('LDAP_MASTER_ADMIN_PASS', ''),
            'user_base' => env('LDAP_MASTER_USER_BASE', 'ou=people,dc=intranet,dc=vermont,dc=eu'),
        ],
        'slave_server' => [
            'url' => env('LDAP_SLAVE_URL', 'ldap://ldap.vermont.eu'),
            'port' => env('LDAP_SLAVE_PORT', 389),
            'admin_name' => env('LDAP_SLAVE_ADMIN_NAME', 'cn=ro,dc=intranet,dc=vermont,dc=eu'),
            'admin_pass' => env('LDAP_SLAVE_ADMIN_PASS', ''),
            'user_base' => env('LDAP_SLAVE_USER_BASE', 'ou=people,dc=intranet,dc=vermont,dc=eu'),
        ],
    ],

    'cdb' => [
        'url' => env('CDB_URL', 'https://cdb.vermont.eu/api/hrms/v1/'),
        'key' => env('CDB_KEY', ''),
    ],

    'hrms1' => [
        'url' => env('HRMS1_URL', 'https://hrms.vermont.eu/api/hrms2/'),
        'key' => env('HRMS1_KEY', ''),
    ],

    'hrms1_contracts' => [
        'url' => env('HRMS1_CONTRACTS_URL', 'https://hrms.vermont.eu/api/hrms2-contracts/'),
        'key' => env('HRMS1_CONTRACTS_KEY', ''),
        'forced_load_from_service' => env('HRMS1_CONTRACTS_FORCED', false),
        'forced_real_data_from_service' => env('HRMS1_CONTRACTS_FORCED_REAL_DATA', false),
    ],

    'matrix' => [
        'url' => env('MATRIX_URL', 'https://matrix.vermont.eu/api/hrms2/v1/'),
        'key' => env('MATRIX_KEY', ''),
        'money_crypt_key' => env('MATRIX_MONEY_CRYPT_KEY', null),
    ],

    'account' => [
        'url' => env('ACCOUNT_URL', 'https://account.vermontclub.eu/accountapi/hrms/v1/'),
        'user' => env('ACCOUNT_USER', ''),
        'pass' => env('ACCOUNT_PASS', ''),
    ],

    'enum_settings' => [
        'proposal_type' => [
            'addition_allowed' => explode(',', env('PROPOSAL_TYPE_WITH_ADDITION', \App\Enums\Remuneration\ProposalType::SALARY->value)),
            'contain_subtypes' => [
                \App\Enums\Remuneration\ProposalType::REWARD->value => 'financialSubTypes',
                \App\Enums\Remuneration\ProposalType::NON_FINANCIAL_REWARD->value => 'nonFinancialSubTypes',
            ],
            'enable_without_notify' => [
                \App\Enums\Remuneration\ProposalType::REWARD->value,
            ],
            'enable_is_paid_via_agreement' => [
                \App\Enums\Remuneration\ProposalType::REWARD->value,
            ],
            'enable_is_paid_via_another_way' => [
                \App\Enums\Remuneration\ProposalType::REWARD->value,
            ]
        ],
        'proposal_subtype' => [
            'one_of_kind_per_month' => [
                [
                    \App\Enums\Remuneration\ProposalSubType::F_ACTIVATION->value,
                    \App\Enums\Remuneration\ProposalSubType::F_FULFILLMENT_OF_FULL_YEAR->value,
                    \App\Enums\Remuneration\ProposalSubType::F_FULFILLMENT_OF_HALF_YEAR->value,
                    \App\Enums\Remuneration\ProposalSubType::F_FULFILLMENT_OF_MONTH->value
                ],
            ],
            'reason_required' => [
                \App\Enums\Remuneration\ProposalSubType::F_ACTIVATION->value,
                \App\Enums\Remuneration\ProposalSubType::F_FULFILLMENT_OF_MONTH->value,
                \App\Enums\Remuneration\ProposalSubType::F_OTHER->value,
                \App\Enums\Remuneration\ProposalSubType::N_OTHER->value,
            ],
            'show_retail_competition_link' => [
                \App\Enums\Remuneration\ProposalSubType::F_ACTIVATION->value,
                \App\Enums\Remuneration\ProposalSubtype::F_FULFILLMENT_OF_MONTH->value,
            ]
        ],
    ],
    'remuneration_proposals' => [
        'base_retail_competition_link' => env('BASE_RETAIL_COMPETITION_LINK', 'https://retail.vermont.eu/hrms-competitions')
    ]
];

