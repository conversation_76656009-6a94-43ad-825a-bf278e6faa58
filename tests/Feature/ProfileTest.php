<?php

use App\Models\Person\User;

test('profile page is displayed', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->get('/employee/profile');

    $response->assertOk();
});

//test('profile information can be updated', function () {
//    $user = User::factory()->create();
//
//    $response = $this
//        ->actingAs($user)
//        ->patch('/profile', [
//            'name' => 'Test User',
//            'email' => '<EMAIL>',
//        ]);
//
//    $response
//        ->assertSessionHasNoErrors()
//        ->assertRedirect('/profile');
//
//    $user->refresh();
//
//    $this->assertSame('Test User', $user->name);
//    $this->assertSame('<EMAIL>', $user->email);
//    $this->assertNull($user->email_verified_at);
//});
