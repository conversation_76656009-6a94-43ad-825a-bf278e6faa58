<?php

namespace App\Providers;

use App\Models\Person\User;
use App\Models\RaP\Permission;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Passport;
use Laravel\Pulse\Facades\Pulse;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        \Spatie\Flash\Flash::levels([
            'success' => 'success',
            'warning' => 'warning',
            'error' => 'error',
            'surprise' => 'primary',
            'info' => 'gray',
        ]);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->bootCustomSettings();
        $this->bootCustomCollections();
        $this->bootLaravelPassport();
        $this->bootLaravelPulse();
    }

    private function bootLaravelPulse(): void
    {
        Gate::define('viewPulse', function (User $user) {
            return $user->can(Permission::DEVELOPER_CAN_ACCEESS_LOGVIEWER);
        });

        Pulse::user(fn ($user) => [
            'name' => $user->first_name,
            'extra' => $user->login,
        ]);
    }

    private function bootLaravelPassport(): void
    {
        Passport::tokensCan([
            'matrix' => 'Use api endpoint for Matrix',
            'retail' => 'Use api endpoint for Retail',
        ]);
    }

    private function bootCustomSettings(): void
    {
        Date::use(CarbonImmutable::class);
        Model::preventLazyLoading(! app()->isProduction());
    }

    private function bootCustomCollections(): void
    {
        Collection::macro('forFormSelect', function (string $valueColumn = 'id', string $optionColumn = 'name', string $optionDetailColumn = null) {
            return $this->map(function ($item) use ($valueColumn, $optionColumn, $optionDetailColumn) {
                $item->form_select_value = $item->{$valueColumn};
                $item->form_select_option = $item->{$optionColumn}.($optionDetailColumn ? ' ('.$item->{$optionDetailColumn}.')' : '');
                return $item;
            })
                ->prepend(
                    (object)['form_select_value' => '', 'form_select_option' => __('main.form_select')]
                );
        });
    }
}
