<?php

namespace App\Facades\Vermont;

use Illuminate\Support\Facades\Vite;

class VermontVite extends Vite
{
    public static function asset($asset, $buildDirectory = null) : string
    {
        try {
            return Vite::asset($asset, $buildDirectory);
        } catch (\Throwable $t) {
            if(config('app.env') !== 'production') {
                throw $t;
            }

            return 'ASSET DOES NOT EXIST';
        }
    }
}
