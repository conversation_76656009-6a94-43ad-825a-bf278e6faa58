<?php

namespace App\Enums\Person\Assigment;

use App\Traits\Enums\EnumCollectionForFormSelectTrait;

enum AssigmentStatusEnum: int
{
    use EnumCollectionForFormSelectTrait;

    CASE DISMISSAL = 1;
    CASE PN = 2;
    CASE JOBS = 3;
    CASE LINKEDIN = 4;
    CASE MATERNITY = 5;
    CASE NO_ACTUAL = 6;
    CASE NOT_LIFT_CALL = 7;
    CASE NOT_LIFT_CALLED = 8;
    CASE NON_ENTER = 9;
    CASE UNPAID = 10;
    CASE NOT_COME = 11;
    CASE BAD = 12;
    CASE NEW = 13;
    CASE APOLOGIZED = 14;
    CASE SIGN_CONTRACT = 15;
    CASE SENDED_G = 16;
    CASE SEND_G = 17;
    CASE INVITED_2INTERW = 18;
    CASE INVITED_AC = 19;
    CASE INVITED_INTERN = 20;
    CASE INVITED_SNP = 21;
    CASE INVITED = 22;
    CASE INVITE_2INTERW = 23;
    CASE INVITE_AC = 24;
    CASE INVITE_INTERN = 25;
    CASE INVITE_SNP = 26;
    CASE INVITE = 27;
    CASE DISCHARGED = 28;
    CASE DISCHARGE = 29;
    CASE CANCELLATION_OWN = 30;
    CASE CANCELLATION_EMP = 31;
    CASE ACCEPT = 32;
    CASE ACCEPTED = 33;
    CASE AGENCY = 34;
    CASE COACHING = 35;
    CASE BACK_UP = 36;
    CASE OTHER_POSITION = 37;
    CASE REFUSE = 38;
    CASE DISALLOWED_PROSPECTIVE = 39;
    CASE REFUSED = 40;
    CASE REJECTED_BY_STAFF = 41;
    CASE ACCEPTED_EXT_B = 100;
    CASE ACCEPTED_EXT_NOB = 101;
    CASE DPP_ENDED = 102;
    CASE COMPLETED_SELECTION = 103;

    public function translation(): string
    {
        return __('hr.users.assigment_statuses.'.$this->name);
    }

    public static function fromHrmsId(int $hrmsId)
    {
        return match ($hrmsId) {
            469 => self::DISMISSAL,
            478 => self::PN,
            477 => self::JOBS,
            476 => self::LINKEDIN,
            466 => self::MATERNITY,
            471 => self::NO_ACTUAL,
            484 => self::NOT_LIFT_CALL,
            485 => self::NOT_LIFT_CALLED,
            479 => self::NON_ENTER,
            480 => self::UNPAID,
            467 => self::NOT_COME,
            447 => self::BAD,
            444 => self::NEW,
            468 => self::APOLOGIZED,
            457 => self::SIGN_CONTRACT,
            449 => self::SENDED_G,
            448 => self::SEND_G,
            472 => self::INVITED_2INTERW,
            455 => self::INVITED_AC,
            464 => self::INVITED_INTERN,
            453 => self::INVITED_SNP,
            446 => self::INVITED,
            465 => self::INVITE_2INTERW,
            454 => self::INVITE_AC,
            463 => self::INVITE_INTERN,
            452 => self::INVITE_SNP,
            445 => self::INVITE,
            460 => self::DISCHARGED,
            459 => self::DISCHARGE,
            482 => self::CANCELLATION_OWN,
            483 => self::CANCELLATION_EMP,
            456 => self::ACCEPT,
            458 => self::ACCEPTED,
            473 => self::AGENCY,
            462 => self::COACHING,
            475 => self::BACK_UP,
            474 => self::OTHER_POSITION,
            450 => self::REFUSE,
            461 => self::DISALLOWED_PROSPECTIVE,
            451 => self::REFUSED,
            470 => self::REJECTED_BY_STAFF,
            486 => self::ACCEPTED_EXT_B,
            487 => self::ACCEPTED_EXT_NOB,
            481 => self::DPP_ENDED,
            488 => self::COMPLETED_SELECTION,
        };
    }

    public function isEmployeeStatus() : bool
    {
        return match ($this) {
            self::ACCEPTED,
            self::DISCHARGE,
            self::COACHING,
            self::INVITE_INTERN,
            self::INVITED_INTERN,
            self::MATERNITY,
            self::PN,
            self::CANCELLATION_OWN,
            self::CANCELLATION_EMP,
            self::ACCEPTED_EXT_B,
            self::ACCEPTED_EXT_NOB => true,
            default => false
        };
    }
}
