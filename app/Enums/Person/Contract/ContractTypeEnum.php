<?php

namespace App\Enums\Person\Contract;

enum ContractTypeEnum: int
{
    case TPP = 1;
    case DVP = 2;
    case DPC = 3;
    case DBPS = 4;
    case EXT = 5;

    public function translation(): string
    {
        return __('hr.users.contract_types.'.$this->name);
    }

    public function shortTranslation(): string
    {
        return __('hr.users.contract_types_short.'.$this->name);
    }

    public static function fromHrms1Code(string $hrmsCode): self
    {
        return match (strtolower($hrmsCode)) {
            'tpp' => self::TPP,
            'dvp' => self::DVP,
            'dpc' => self::DPC,
            'dbps' => self::DBPS,
            'ext' => self::EXT,
            default => throw new \Exception('ContractTypeEnum can not be determined by hrms code "'.$hrmsCode.'"'),
        };
    }
}
