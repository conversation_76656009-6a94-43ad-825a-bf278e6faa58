<?php

namespace App\Enums\Person\Contract;

enum ContractStatusEnum: int
{
    case FUTURE = 1;
    case VALID = 2;
    case ENDED = 3;
    case UNFULFILLED = 4;

    public function translation(): string
    {
        return __('hr.users.contract_statuses.'.$this->name);
    }

    public static function fromContractDates($startDate, $endDate): self
    {
        if(is_null($startDate) && is_null($endDate)){
            return self::VALID;
        }

        if(!is_null($endDate) && ($endDate < date('Y-m-d'))) {
            return self::ENDED;
        }

        if(!is_null($startDate) && ($startDate > date('Y-m-d'))){
            return self::FUTURE;
        }

        return self::VALID;
    }
}
