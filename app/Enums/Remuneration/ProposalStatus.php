<?php

namespace App\Enums\Remuneration;

use App\Traits\Enums\EnumCasesForFilterTrait;
use Illuminate\Support\Collection;

enum ProposalStatus : int
{
    case CREATED = 1;
    case PENDING = 2;
    case FORWARDED = 3;
    case APPROVED = 4;
    case REJECTED = 5;
    case USURPED = 6;
    case WITHDRAWED = 7;
    case ROLLBACKED = 8;
    case UPDATED = 9;

    public function translation(): string
    {
        return __('employee.remunerations.proposals.statuses.'.$this->name);
    }

    public static function casesForFilter($withName = false): Collection
    {
        $cases = collect();

        foreach([self::APPROVED, self::REJECTED] as $case) {
            $cases->add(['value' => $case->value, 'translation' => $withName ? ($case->name.' ('.$case->translation().')') : $case->translation()]);
        }

        return $cases;
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::APPROVED => 'hrms hrms-acepted-circle text-success',
            self::REJECTED => 'hrms hrms-rejected-circle text-error',
            self::FORWARDED => 'hrms hrms-waiting5-aproval-circle-outline',
            self::UPDATED => 'hrms hrms-edit-text-outline',
            default => '',
        };
    }
}
