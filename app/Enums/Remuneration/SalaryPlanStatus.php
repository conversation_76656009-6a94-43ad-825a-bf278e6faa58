<?php

namespace App\Enums\Remuneration;

use App\Traits\Enums\EnumCasesForFilterTrait;
use Illuminate\Support\Collection;

enum SalaryPlanStatus : int
{
    case PLANNED = 1;
    case CREATED = 2;
    case CANCELLED = 3;

    public function translation(): string
    {
        return __('employee.remunerations.salaries.plans.statuses.'.$this->name);
    }

    public static function casesForFilter($withName = false): Collection
    {
        $cases = collect();

        foreach([self::PLANNED, self::CREATED, self::CANCELLED] as $case) {
            $cases->add(['value' => $case->value, 'translation' => $withName ? ($case->name.' ('.$case->translation().')') : $case->translation()]);
        }

        return $cases;
    }

    public function getIcon(): string
    {
        return match ($this) {
            self::PLANNED => 'hrms hrms-waiting5-aproval-circle-outline text-success',
            self::CREATED => 'hrms hrms-acepted-circle text-success',
            self::CANCELLED => 'hrms hrms-rejected-circle text-error',
            default => '',
        };
    }
}
