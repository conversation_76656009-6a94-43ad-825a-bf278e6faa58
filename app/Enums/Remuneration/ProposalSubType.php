<?php

namespace App\Enums\Remuneration;

use App\Traits\Enums\EnumCasesForFilterTrait;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

enum ProposalSubType: int
{
    use EnumCasesForFilterTrait;

    case F_ACTIVATION = 1;
    case F_FULFILLMENT_OF_MONTH = 2;
    case F_FULFILLMENT_OF_HALF_YEAR = 3;
    case F_FULFILLMENT_OF_FULL_YEAR = 4;
    case F_EXTRA = 5;
    case F_TRANSLATIONS = 6;
    case F_ORDERS = 7;
    case F_MONTH_EXTRA_WAREHOUSE = 8;
    case F_ATTENDANCE = 9;
    case F_OTHER = 10;

    CASE N_VOUCHER_VERMONT = 50;
    CASE N_EMPLOYEE_DISCOUNT = 51;
    CASE N_VOUCHER_OTHER = 52;
    CASE N_RECREATION = 53;
    CASE N_COURSE = 54;
    CASE N_OTHER = 55;

    public function translation(): string
    {
        return __('employee.remunerations.proposals.sub_types.'.$this->name);
    }

    public static function financialSubTypes(): Collection
    {
        return collect(self::cases())->filter(function ($item) {
            return Str::startsWith($item->name,'F_');
        });
    }

    public static function nonFinancialSubTypes(): Collection
    {
        return collect(self::cases())->filter(function ($item) {
            return Str::startsWith($item->name,'N_');
        });
    }
}
