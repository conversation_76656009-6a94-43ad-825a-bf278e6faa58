<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class AddContext
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        try{
            Context::add('url', $request?->url());
            Context::add('trace_id', Str::uuid()->toString());
        }catch (\Throwable $t) {
            Log::error('AddContext error');
        }

        return $next($request);
    }
}
