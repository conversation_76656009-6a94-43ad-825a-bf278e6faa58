<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecondStepVerificationMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        if(!auth()->user()->validSecondStepSmsCodes()->count()) {
            session(['second_step_intended' => $request->fullUrl()]);
            return to_route('auth.second_step_verification.show');
        }

        return $next($request);
    }
}
