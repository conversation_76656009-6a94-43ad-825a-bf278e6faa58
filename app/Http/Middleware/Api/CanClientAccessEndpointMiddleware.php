<?php

namespace App\Http\Middleware\Api;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class CanClientAccessEndpointMiddleware
{
    public function handle(Request $request, Closure $next, string $endpoint = null): Response
    {
        $client = auth('api')->client();

        if(empty($client) || Str::lower($client->name) !== $endpoint) {
            abort(Response::HTTP_FORBIDDEN, 'Thic client cannot access this endpoint');
        }

        return $next($request);
    }
}
