<?php

namespace App\Http\Controllers\Api\V1\External\Retail;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\External\Retail\IndexUsersRequest;
use App\Http\Resources\Api\V1\External\Retail\UserResource;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Position;
use App\Models\Person\User;
use Illuminate\Http\Request;

class UsersController extends Controller
{
    public function index(IndexUsersRequest $request)
    {
        $users = User::withWhereHas('validAssigments', function ($q) use ($request) {
                $q->withoutMaternityAndPN();
            })
            ->when($request->validated('positions'), function ($query) use ($request) {
                $query->whereHas('validAssigments', function ($query) use ($request) {
                    $positionsIds = Position::whereIn('code', $request->validated('positions'))->pluck('id');

                    $query->whereIn('position_id', $positionsIds);
                });
            })
            ->when($request->validated('centres'), function ($query) use ($request) {
                $query->whereHas('validAssigments', function ($query) use ($request) {
                    $centresIds = Centre::whereIn('code', $request->validated('centres'))->pluck('id');

                    $query->whereIn('centre_id', $centresIds);
                });
            })
            ->when($request->validated('users'), function ($query) use ($request) {
                $query->whereIn('login', $request->validated('users'));
            })
            ->with('validAssigments.centre', 'validAssigments.position', 'workData')
            ->paginate(50);

        return UserResource::collection($users);
    }
}
