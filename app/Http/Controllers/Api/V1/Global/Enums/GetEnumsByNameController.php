<?php

namespace App\Http\Controllers\Api\V1\Global\Enums;

use App;
use App\Enums\Remuneration\ProposalSubType;
use App\Enums\Remuneration\ProposalType;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class GetEnumsByNameController extends Controller
{
    private App\Services\HelperService $helperService;

    public function __construct(App\Services\HelperService $helperService)
    {
        $this->helperService = $helperService;
    }

    public function __invoke(Request $request): JsonResponse
    {
        $request->validate([
            'enum_names' => ['required', 'array'],
            'enum_names.*' => ['string'],
        ]);
        $enums = [
            'Remuneration\\ProposalType'
        ];
        foreach ($enums as $enumName) {
            $explodedEnumName = explode("\\", $enumName);
            $nameInArray = end($explodedEnumName);
            $enumData[$nameInArray] = $this->helperService->prepareEnumByName($enumName);
        }

        return response()->json($enumData, Response::HTTP_OK);
    }


}
