<?php

namespace App\Http\Controllers\Api\V1\Employee\Remunerations;

use App\Enums\Remuneration\ProposalStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Employee\Remunerations\StoreProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\UpdateProposalRequest;
use App\Http\Resources\Api\V1\Employee\Remunerations\ProposalResource;
use App\Models\RaP\Permission;
use App\Models\Remuneration\Proposal;
use App\Services\Employee\Remuneration\Proposal\CreateProposalService;
use App\Services\Employee\Remuneration\Proposal\GetProposalService;
use App\Services\Employee\Remuneration\Proposal\UpdateProposalService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use App\Traits\Controllers\Employee\Remuneration\PreselectedIsPaidViaAgreementValueTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class ProposalController extends Controller
{
    use PreselectedIsPaidViaAgreementValueTrait;
    use PermissionTrait;
    private GetProposalService $getProposalService;

    public function __construct(GetProposalService $getProposalService)
    {
        $this->getProposalService = $getProposalService;
    }

    public function index(): AnonymousResourceCollection
    {
        $proposals = Proposal::with([
                'user:id,first_name,last_name',
                'currency',
                'proposer:id,first_name,last_name',
                'approver:id,first_name,last_name',
                'centre:id,code,name',
            ])
            ->where('approver_id', auth()->id())
            ->paginate(30);

        return ProposalResource::collection($proposals);
    }

    public function store(StoreProposalRequest $request): JsonResponse|string
    {
        $validatedRequestData = $request->validated();
        $validatedRequestData['proposer_id'] = auth()->id();

        $validatedRequestData['is_paid_via_agreement'] = $this->getPreselectedIsPaidViaAgreementValue($validatedRequestData);

        if($errorReturnFromService = (new CreateProposalService())->createMultipleProposals($validatedRequestData)) {
            return response()->json(['error' => $errorReturnFromService], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json(['success' => 'Proposals ('.count($request->users).') has been created'], Response::HTTP_CREATED);
    }

    public function show(string $id): ProposalResource
    {
        $proposal = Proposal::where('id', $id)
            ->with([
                'user:id,first_name,last_name',
                'currency',
                'proposer:id,first_name,last_name',
                'approver:id,first_name,last_name',
                'centre:id,code,name',
            ])
            ->where(function($query) {
                $query->where('approver_id', auth()->id())
                    ->orWhereHas('logs', function($query) {
                        $query->where('user_id', auth()->id());
                    });
            })
            ->firstOrFail();

        return new ProposalResource($proposal);
    }

    public function update(UpdateProposalRequest $request, Proposal $proposal): JsonResponse
    {
        $validatedRequestData = $request->validated();

        $isApproved = false;
        if (auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
            if ((int)$validatedRequestData['approver_id'] === auth()->id()) {
                $isApproved = true;
            }
        }

        if (is_null($request->input('update_type'))) {
            if ($errorReturnFromService = (new UpdateProposalService())->updateProposal(
                $proposal,
                $validatedRequestData,
                auth()->id()
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }

        if ($request->input('update_type') === 'change_payment_status') {
            if ($errorReturnFromService = (new UpdateProposalService())->changePaymentStatusForProposal(
                $proposal,
                $validatedRequestData,
                auth()->id()
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }
        if ($request->input('update_type') === 'paid_via_agreement') {
            if ($errorReturnFromService = (new UpdateProposalService())->changeIsPaidViaAgreementForProposal(
                $proposal,
                $validatedRequestData,
                auth()->id()
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }

        if ($request->input('update_type') === 'is_paid_via_other_way') {
            if ($errorReturnFromService = (new UpdateProposalService())->changeIsPaidViaOtherWayForProposal(
                $proposal,
                $validatedRequestData,
                auth()->id()
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }

        if ($request->input('update_type') === 'approve' && $isApproved) {
            if ($errorReturnFromService = (new UpdateProposalService())->approveProposal(
                $proposal,
                $validatedRequestData,
                auth()->id(),
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }

        if ($request->input('update_type') === 'approve' && !$isApproved) {
            if ($errorReturnFromService = (new UpdateProposalService())->forwardProposal(
                $proposal,
                $validatedRequestData['approver_id'],
                $validatedRequestData,
                auth()->id(),
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }

        if ($request->input('update_type') === 'usurp') {
            if ($errorReturnFromService = (new UpdateProposalService())->usurpProposal(
                $proposal,
                $validatedRequestData,
                auth()->id(),
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }

        if ($request->input('update_type') === 'withdraw') {
            if ($errorReturnFromService = (new UpdateProposalService())->withdrawProposal(
                $proposal,
                $validatedRequestData,
                auth()->id(),
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }
        }


        if ($request->input('update_type') === 'rollback') {
            if ($errorReturnFromService = (new UpdateProposalService())->rollbackProposal(
                $proposal,
                $validatedRequestData,
                auth()->id(),
                $isApproved
            )
            ) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }

            flash()->success(__('employee.remunerations.proposals.edit.success_message'));
        }

        if ($request->input('update_type') === 'reject') {
            if ($errorReturnFromService = (new UpdateProposalService())->rejectProposal($proposal, $validatedRequestData, auth()->id())) {
                throw ValidationException::withMessages([$errorReturnFromService]);
            }

            flash()->success(__('employee.remunerations.proposals.reject.success_message'));
        }
        $validAssignments = $this->getValidAssigmentsWithChildrenForRemunerationProposals(true);
        $validAssigmentUserIds = $validAssignments->pluck('user_id')->unique()->filter(fn($userId) => $userId !== auth()->id());
        $proposal->is_usurpable = in_array($proposal->approver_id, $validAssigmentUserIds->toArray())
            && $proposal->approver_id !== auth()->id();
        $proposal->load([
            'currency',
            'approver:id,first_name,last_name',
            'logs' => function ($q) use ($validAssigmentUserIds) {
                $q->whereIn('user_id', $validAssigmentUserIds)->orWhere('user_id', auth()->id())->orWhere('status', ProposalStatus::ROLLBACKED)
                    ->orderByDesc('created_at')->limit(3);
            },
            'logs.user:id,first_name,last_name',
            'proposer:id,first_name,last_name'
        ]);
        $proposal = $this->getProposalService->filterLogsAndGetDataForStatuses($proposal);

        return response()->json([
            'message' => 'Proposal updated successfully',
            'data' => new ProposalResource($proposal),
        ], Response::HTTP_OK);
    }

    public function destroy(string $id)
    {
        //
    }
}
