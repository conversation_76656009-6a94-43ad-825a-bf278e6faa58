<?php

namespace App\Http\Controllers\Api\V1\Employee\Remunerations\Table;

use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalSubType;
use App\Enums\Remuneration\ProposalType;
use App\Http\Controllers\Controller;
use App\Models\Remuneration\Proposal;
use App\Services\Employee\Remuneration\Proposal\GetProposalService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpFoundation\Response;

class GetSpecificProposalController extends Controller
{
    use PermissionTrait;
    private GetProposalService $getProposalService;

    public function __construct(GetProposalService $getProposalService)
    {
        $this->getProposalService = $getProposalService;
    }

    public function __invoke(Request $request): JsonResponse
    {
        $request->validate([
            'proposal_id' => ['required_without_all:user_id,type,subtype,validity,department_id,centre_id'],
            'user_id' => ['required_without:proposal_id', 'integer', 'exists:person_users,id'],
            'approver_id' => ['required_without:proposal_id', 'integer', 'exists:person_users,id'],
            'type' => ['required_without:proposal_id', 'integer', 'min:1', Rule::enum(ProposalType::class)],
            'subtype' => ['nullable', 'integer', 'min:1', Rule::enum(ProposalSubType::class)],
            'validity' => ['required_without:proposal_id', 'date'],
            'department_id' => ['required_without:proposal_id', 'integer', 'exists:organisation_departments,id'],
            'centre_id' => ['nullable', 'integer', 'exists:organisation_centres,id'],
            'pairing_hash' => ['required_without:proposal_id', 'string'],

        ]);

        $loggedUser = auth()->user();
        $validAssignments = $this->getValidAssigmentsWithChildrenForRemunerationProposals(true);
        $validAssigmentUserIds = $validAssignments->pluck('user_id')->unique()->filter(fn($userId) => $userId !== auth()->id());

        $proposal = Proposal::when(!is_null($request->get('proposal_id')), function (Builder $query) use ($request) {
            $query->where('id', $request->get('proposal_id'));
        }, function (Builder $query) use ($request, $loggedUser) {
            $query->where('user_id', $request->input('user_id'))
                ->where('type', $request->input('type'))
                ->where('subtype', $request->input('subtype'))
                ->where('validity', Carbon::createFromDate($request->input('validity'))->format('Y-m-d'))
                ->where('department_id', $request->input('department_id'))->where(function ($query) use ($loggedUser) {
                    $query->where('approver_id', $loggedUser->id)->where('proposer_id', $loggedUser->id)->where(
                        'status',
                        '!=',
                        ProposalStatus::REJECTED
                    )->orWhere(
                        'approver_id',
                        $loggedUser->id
                    )->whereColumn('proposer_id', '!=', 'approver_id')
                        ->orWhere('approver_id', '!=', $loggedUser->id)->whereColumn(
                            'proposer_id',
                            'approver_id'
                        )
                        ->where('user_id', '!=', $loggedUser->id)
                        ->where(
                            'status',
                            '!=',
                            ProposalStatus::REJECTED->value
                        )->orWhere('approver_id', '!=', $loggedUser->id)->whereColumn(
                            'proposer_id',
                            '!=',
                            'approver_id'
                        )
                        ->where('user_id', '!=', $loggedUser->id)
                    ;
                })
                ->when(!is_null($request->input('pairing_hash')), function ($query) use ($request) {
                    $query->where('pairing_hash', $request->input('pairing_hash'));
                })
                ->when(!is_null($request->input('centre_id')), function ($query) use ($request) {
                    $query->where('centre_id', $request->input('centre_id'));
                });
        })->with([
            'currency',
            'proposer:id,first_name,last_name',
            'approver:id,first_name,last_name',
            'logs' => function ($q) use ($validAssigmentUserIds, $loggedUser) {
                $q->whereIn('user_id', $validAssigmentUserIds)->orWhere('user_id', $loggedUser->id)->orWhere('status', ProposalStatus::ROLLBACKED)
                    ->orderByDesc('created_at')->limit(3);
            },
            'logs.user:id,first_name,last_name'
        ])
            ->first();
        if (is_null($proposal)) {
            return response()->json([null], Response::HTTP_NOT_FOUND);
        }
        $proposal->is_usurpable = in_array($proposal->approver_id, $validAssigmentUserIds->toArray())
            && $proposal->approver_id !== $loggedUser->id;
        $proposal = $this->getProposalService->filterLogsAndGetDataForStatuses($proposal);

        return response()->json([$proposal], Response::HTTP_OK);
    }
}
