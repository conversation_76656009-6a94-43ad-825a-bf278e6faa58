<?php

namespace App\Http\Controllers\Api\V1\Employee\Remunerations\Table;

use App\Enums\Remuneration\ProposalStatus;
use App\Http\Controllers\Controller;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\ProposalLog;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetAllLogsForProposalController extends Controller
{
    use PermissionTrait;

    public function __invoke(Proposal $proposal, int $limit = null): JsonResponse
    {
        $validAssignments = $this->getValidAssigmentsWithChildrenForRemunerationProposals(true);
        $validAssigmentUserIds = $validAssignments->pluck('user_id')->unique()->filter(fn($userId) => $userId !== auth()->id());
        $logs = ProposalLog::where('proposal_id', $proposal->id)->where(function (Builder $query) use ($validAssigmentUserIds) {
            $query->whereIn('user_id', $validAssigmentUserIds)->orWhere('user_id', auth()->id())->orWhere('status', ProposalStatus::ROLLBACKED);
        })->when($limit, function ($q) use ($limit) {
            $q->limit($limit);
        })->with('user:id,first_name,last_name')->orderByDesc('created_at')->get();

        return response()->json(['logs' =>$logs], Response::HTTP_OK);
    }
}
