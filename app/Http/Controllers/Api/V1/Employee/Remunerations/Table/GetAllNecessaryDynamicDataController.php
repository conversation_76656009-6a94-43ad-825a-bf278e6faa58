<?php

namespace App\Http\Controllers\Api\V1\Employee\Remunerations\Table;

use App\Enums\Remuneration\ProposalStatus;
use App\Http\Controllers\Controller;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Department;
use App\Models\Organisation\Position;
use App\Models\Organisation\Section;
use App\Models\Person\Assigment;
use App\Models\Person\User;
use App\Models\Remuneration\Proposal;
use App\Services\Employee\Remuneration\Proposal\CreateProposalService;
use App\Services\Employee\Remuneration\Proposal\GetProposalService;
use App\Services\HelperService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Symfony\Component\HttpFoundation\Response;


class GetAllNecessaryDynamicDataController extends Controller
{
    use PermissionTrait;

    private HelperService $helperService;
    private GetProposalService $getProposalService;

    public function __construct(HelperService $helperService, GetProposalService $getProposalService)
    {
        $this->helperService = $helperService;
        $this->getProposalService = $getProposalService;
    }

    public function __invoke(Request $request, bool $getUsersForAddMoreUsers = false): Response
    {
        $createProposalService = new CreateProposalService();
        $loggedUser = auth()->user();
        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position:id,code,name',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents'
        );

        $request->validate(
            [
                'section_id' => ['required', 'integer', 'exists:organisation_sections,id'],
                'department_id' => ['required', 'integer', 'exists:organisation_departments,id'],
                'validity' => ['required', 'string'],
                'centre_id' => ['nullable', 'integer', 'exists:organisation_centres,id'],
                'user_ids' => ['nullable', 'array'],
                'user_ids.*' => ['int'],
                'get_only_specified_user_ids' => ['nullable', 'boolean'],
            ]
        );


        $centres = Centre::where('is_active', 1)->with(
            ['address:id,country_id', 'address.country:id,currency_id']
        )->get(
            ['id', 'address_id', 'code', 'name']
        );

        $allSections = Section::get()->keyBy('id');
        $allDepartments = Department::get()->keyBy('id');
        $allPositions = Position::with('pivotSections', 'pivotDepartments')->get();

        $selectedSection = is_null($request->input('section_id')) ? null : Section::where('id', $request->input('section_id'))->first();
        $selectedDepartment = is_null($request->input('department_id')) ? null : Department::where('id', $request->input('department_id'))->first();
        $selectedCentre = is_null($request->input('centre_id'))
            ? null
            : $centres->where('id', $request->input('centre_id'))->first();


        $validAssignments = $this->getValidAssigmentsWithChildrenForRemunerationProposals(true);
        if ($validAssignments === null) {
            $validAssignments = Assigment::where('id', 0)->get();
        }

        $assigmentCentres = $centres->whereIn('id', $validAssignments->pluck('centre_id')->unique());

        $assigmentPositions = $allPositions
            ->whereIn('id', $validAssignments->pluck('position_id')->unique());
        $assigmentSections = $allSections->whereIn(
            'id',
            $assigmentPositions->pluck('pivotSections')->flatten()->unique('section_id')->pluck('section_id')
        );
        $assigmentDepartments = $allDepartments->whereIn(
            'id',
            $assigmentPositions->pluck('pivotDepartments')->flatten()->unique('department_id')->pluck('department_id')
        );

        if ($assigmentSections->count() === 1) {
            $selectedSection = $assigmentSections->first();
        }

        if ($assigmentDepartments->count() === 1) {
            $selectedDepartment = $assigmentDepartments->first();
        }
        if ($assigmentCentres->count() === 1) {
            $selectedCentre = $assigmentCentres->first();
        }
        $filteredAssignments = clone $validAssignments;
        $filteredAssignments->loadMissing('position.pivotSections', 'position.pivotDepartments');


        $filteredAssignments = $filteredAssignments->filter(function ($assigment, $key) use ($selectedSection) {
            $sectionsIds = $assigment->position->pivotSections->pluck('section_id')->unique()->toArray() ?? [];
            return in_array($selectedSection?->id, $sectionsIds, false);
        });

        if ($selectedSection) {
            $filteredAssignments = $filteredAssignments->filter(function ($assigment, $key) use ($selectedSection) {
                $sectionsIds = $assigment->position->pivotSections->pluck('section_id')->unique()->toArray() ?? [];
                return in_array($selectedSection?->id, $sectionsIds, false);
            });
        }

        if ($selectedCentre) {
            $filteredAssignments = $filteredAssignments->where('centre_id', $selectedCentre?->id);
        }


        $filteredAssignments = $filteredAssignments->filter(function ($assigment, $key) use ($selectedDepartment) {
            $departmentsIds = $assigment->position->pivotDepartments->pluck('department_id')->unique()->toArray() ?? [];
            return in_array($selectedDepartment?->id, $departmentsIds, false);
        });

        if (!is_null($selectedDepartment->departmentCountryId())) {
            $filteredAssignments = $filteredAssignments->whereIn('centre_id', $assigmentCentres->pluck('id'));
        }

        if (!is_null($selectedCentre)) {
            $filteredAssignments = $filteredAssignments->where('centre_id', $selectedCentre->id);
        }
        $validAssigmentUserIds = $validAssignments->pluck('user_id')->unique()->filter(fn($userId) => $userId !== auth()->id());




        $proposals = Proposal::where('department_id', $selectedDepartment->id)->when(!is_null($selectedCentre), function ($q) use ($selectedCentre) {
            $q->where('centre_id', $selectedCentre->id);
        })->where('validity', $request->input('validity'))
            ->where(function (Builder $query) use ($validAssigmentUserIds, $loggedUser) {
                $query->whereIn('user_id', $validAssigmentUserIds)->orWhere('proposer_id', $loggedUser->id)->orWhereHas(
                    'logs',
                    function ($q) use ($validAssigmentUserIds, $loggedUser) {
                        $q->where('user_id', $loggedUser->id);
                    }
                )->orWhere('approver_id', $loggedUser->id);
            })
            ->with([
                'logs' => function ($q) use ($validAssigmentUserIds, $loggedUser) {
                    $q->whereIn('user_id', $validAssigmentUserIds)->orWhere('user_id', $loggedUser->id)->orWhere('status', ProposalStatus::ROLLBACKED)
                        ->orderByDesc('created_at')->limit(3);
                },
                'currency:id,code',
                'approver:id,first_name,last_name',
                'proposer:id,first_name,last_name',
            ])
            ->get(
                [
                    'id',
                    'user_id',
                    'status',
                    'type',
                    'subtype',
                    'validity',
                    'centre_id',
                    'department_id',
                    'approver_id',
                    'proposer_id',
                    'currency_id',
                    'base',
                    'addition',
                    'is_paid_via_agreement',
                    'is_paid_via_another_way',
                    'payment_status',
                    'pairing_hash',
                    'reason'
                ]
            );

        $proposalUserIds = $proposals->pluck('user_id')->unique()->toArray();

        /*  Get users for table form */
        $filteredAssignmentsForCentre = clone $filteredAssignments;
        $filteredAssignmentsForCentre = $filteredAssignmentsForCentre->reject(function ($assignment) use ($loggedUser) {
            return $assignment->user_id == $loggedUser->id;
        });

        $users = User::hasValidContractForDate($request->input('validity'))->with([
            'validAssigments.position:id,name,code',
            'validAssigments.position.excludedForBudgets',
            'validAssigments.centre:id,name,code',
            'remunerationProposals' => function ($q) use ($request, $loggedUser) {
                $q->where('validity', $request->input('validity'))
                    ->where('centre_id', $request->input('centre_id'))
                    ->where('department_id', $request->input('department_id'))
                    ->where(function (Builder $query) use ($loggedUser) {
                        $query->where('approver_id', $loggedUser->id)->where('proposer_id', $loggedUser->id)->where(
                            'status',
                            '!=',
                            ProposalStatus::REJECTED
                        )
                            ->orWhere(
                                'approver_id',
                                $loggedUser->id
                            )->whereColumn('proposer_id', '!=', 'approver_id')

                            ->orWhere('approver_id', '!=', $loggedUser->id)->whereColumn(
                                'proposer_id',
                                'approver_id'
                            )
                            ->where(
                                'status',
                                '!=',
                                ProposalStatus::REJECTED->value
                            )
                            ->where('user_id', '!=', $loggedUser->id)

                            ->orWhere('approver_id', '!=', $loggedUser->id)->whereColumn(
                                'proposer_id',
                                '!=',
                                'approver_id'
                            )->where('user_id', '!=', $loggedUser->id)
                        ->orWhereHas('logs', function ($q) use ($loggedUser) {
                            $q->where('user_id', $loggedUser->id);
                        })
                        ;
                    })->orderBy('status')->orderByDesc('updated_at')->select([
                        'id',
                        'user_id',
                        'status',
                        'type',
                        'subtype',
                        'validity',
                        'centre_id',
                        'department_id',
                        'approver_id',
                        'proposer_id',
                        'currency_id',
                        'base',
                        'addition',
                        'is_paid_via_agreement',
                        'is_paid_via_another_way',
                        'payment_status',
                        'pairing_hash',
                        'reason'
                    ]);
            },
            'remunerationProposals.currency:id,code',
            'remunerationProposals.approver:id,first_name,last_name',
            'remunerationProposals.proposer:id,first_name,last_name',
            'remunerationProposals.logs' => function ($q) use ($validAssigmentUserIds, $loggedUser) {
                $q->whereIn('user_id', $validAssigmentUserIds)->orWhere('user_id', $loggedUser->id)->orWhere('status', ProposalStatus::ROLLBACKED)
                    ->orderByDesc('created_at')->limit(3);
            },
            'remunerationProposals.logs.user:id,first_name,last_name',
        ])
            ->orderBy('last_name')->orderBy('first_name')->get(['id', 'first_name', 'last_name']);

        $users->each(function ($user) use ($loggedUser, $validAssigmentUserIds) {
            $user->remunerationProposals->each(function ($proposal) use ($loggedUser, $validAssigmentUserIds) {
                $proposal->is_usurpable = in_array($proposal->approver_id, $validAssigmentUserIds->toArray())
                    && $proposal->approver_id !== $loggedUser->id;
                return $this->getProposalService->filterLogsAndGetDataForStatuses($proposal);
            });
        });

        $userIdsForForm = array_merge($proposalUserIds, $filteredAssignmentsForCentre->pluck('user_id')->unique()->toArray());
        $usersForForm = $users->whereIn('id', $userIdsForForm);
        $userForFormIds = $usersForForm->pluck('id')->toArray();

        $usersForAddMorePeople = $getUsersForAddMoreUsers ? $users->reject(function ($user) use ($userForFormIds, $request) {
            if ($request->input('get_only_specified_user_ids', false)) {
                return !in_array($user->id, $request->input('user_ids', [])) || in_array($user->id, $userForFormIds);
            }
            return in_array($user->id, $userForFormIds) || in_array($user->id, $request->input('user_ids', []));
        })->map(function ($user) {
            $assignedCentres = $user->validAssigments->pluck('centre.code')->unique()->toArray();
            $user->full_name_with_centres = count($assignedCentres) > 0 ? $user->full_name.' ('.implode(', ', $assignedCentres).')'
                : $user->full_name;

            return $user;
        }) : collect();

        /*Default currency in create proposal form */
        if ($selectedCentre) {
            $selectedCurrencyId = $selectedCentre->address->country->currency_id;
        } else {
            $myCentresIds = $createProposalService->getUserCentresIds($loggedUser);
            $myCentres = $centres->whereIn('id', $myCentresIds);
            $selectedCurrencyId = null;
            if (($myCentresCurrenciesIds = $myCentres->pluck('address.country.currency_id'))->unique()->count() === 1) {
                $selectedCurrencyId = $myCentresCurrenciesIds->first();
            }
        }


        if ($getUsersForAddMoreUsers) {
            return response()->json(
                [
                    'usersForAddMorePeople' => $usersForAddMorePeople,
                ],
                Response::HTTP_OK
            );
        }
        return response()->json(
            [
                'usersForForm' => $usersForForm->values(),
                'proposals' => $proposals,
                'proposerDefaultCurrency' => $selectedCurrencyId,
                'proposer' => $loggedUser
            ],
            Response::HTTP_OK
        );
    }
}
