<?php

namespace App\Http\Controllers\Api\V1\Employee\Remunerations\Table;

use App\Http\Controllers\Controller;
use App\Models\Base\Currency;
use App\Models\Remuneration\ProposalSubTypeBudget;
use App\Services\Employee\Remuneration\Proposal\GetProposalService;
use App\Services\HelperService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;


class GetAllNecessaryStaticDataController extends Controller
{
    use PermissionTrait;

    private HelperService $helperService;
    private GetProposalService $getProposalService;

    public function __construct(HelperService $helperService, GetProposalService $getProposalService)
    {
        $this->helperService = $helperService;
        $this->getProposalService = $getProposalService;
    }

    public function __invoke(Request $request)
    {
        $loggedUser = auth()->user();
        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position:id,code,name',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents'
        );

        $request->validate(
            [
                'section_id' => ['required', 'integer', 'exists:organisation_sections,id'],
                'department_id' => ['required', 'integer', 'exists:organisation_departments,id'],
                'validity' => ['required', 'string'],
                'centre_id' => ['nullable', 'integer', 'exists:organisation_centres,id'],
            ]
        );
        $currencies = Currency::where('is_active', 1)->get();

        $enums = [];

        $budgets = ProposalSubTypeBudget::where('centre_id', $request->input('centre_id'))->where('validity', '<=', $request->input('validity'))
            ->with('currency')
            ->orderByDesc('validity')->get();
        foreach (['Remuneration\ProposalType', 'Remuneration\ProposalStatus'] as $enumName) {
            $explodedEnumName = explode("\\", $enumName);
            $nameInArray = end($explodedEnumName);
            $enum = $this->helperService->prepareEnumByName($enumName, $request->input('validity'));
            foreach ($enum['cases'] ?? [] as $caseKey => $case) {
                foreach ($case['subcases'] ?? [] as $subcaseKey => $subCase) {
                    $matchedBudget = $budgets->where('subtype', $subCase['value'])->first();
                    if (!is_null($matchedBudget)) {
                        $enum['cases'][$caseKey]['subcases'][$subcaseKey]['budget'] = $matchedBudget;
                    }
                }
            }
            $enums[$nameInArray] = $enum;
        }


        return response()->json(
            [
                'currencies' => $currencies->keyBy('id'),
                'translations' => __('employee.remunerations.component'),
                'createProposalsRoute' => route('api.v1.employee.remunerations.proposals.store'),
                'getSpecificProposalRoute' => route('api.v1.employee.remunerations.get-specific-proposal'),
                'getProposalLogsRoute' => route('api.v1.employee.remunerations.get-proposal-logs'),
                'proposer' => $loggedUser,
                'enums' => $enums,
                'baseRetailCompetitionUrl' => config('vermont.remuneration_proposals.base_retail_competition_link')
            ],
            Response::HTTP_OK
        );
    }
}
