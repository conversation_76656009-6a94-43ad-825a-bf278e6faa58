<?php

namespace App\Http\Controllers\Employee\Remuneration;

use App\Enums\Remuneration\ProposalContractStatus;
use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalSubType;
use App\Enums\Remuneration\ProposalType;
use App\Exports\Employee\Remunerations\ProposalsExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Employee\Remunerations\ApproveProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\ChangeIsPaidViaAgreementRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\ChangePaymentStatusRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\ChangeStatusForMultipleRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\EditProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\ForwardProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\RejectProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\RollbackProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\ShowProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\StoreProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\UpdateProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\UsurpProposalRequest;
use App\Http\Requests\Api\V1\Employee\Remunerations\WithdrawProposalRequest;
use App\Http\Requests\Employee\Remuneration\Proposal\CreateProposalRequest;
use App\Http\Requests\Employee\Remuneration\Proposal\TableIndexProposalRequest;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Department;
use App\Models\Organisation\Position;
use App\Models\Organisation\Section;
use App\Models\Person\User;
use App\Models\RaP\Permission;
use App\Models\Remuneration\Proposal;
use App\Services\Base\CacheService;
use App\Services\Employee\Remuneration\Proposal\CreateProposalService;
use App\Services\Employee\Remuneration\Proposal\UpdateProposalService;
use App\Services\FilterService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use App\Traits\Controllers\Employee\Remuneration\PreselectedIsPaidViaAgreementValueTrait;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Maatwebsite\Excel\Facades\Excel;

class ProposalController extends Controller
{
    use PermissionTrait, PreselectedIsPaidViaAgreementValueTrait;

    public function index()
    {
        return to_route('employee.remunerations.index');
    }

    public function tableIndex(TableIndexProposalRequest $request): View|RedirectResponse
    {
        $allPositions = Position::with('pivotSections', 'pivotDepartments')->get()->keyBy('id');
        $allCentres = Centre::select('id', 'code', 'name', 'centre_type_id', 'address_id')
            ->with('pivotDepartments:centre_id,department_id')
            ->where('is_active', true)
            ->get()
            ->keyBy('id');
        $allSections = Section::with('departments', 'departments.centres')->get()->keyBy('id');
        $allDepartments = Department::get()->keyBy('id');

        $selectedSection = request()->input('section') ? $allSections->where('id', request()->input('section'))->first() : null;
        $selectedDepartment = request()->input('department') ? $allDepartments->where('id', request()->input('department'))->first() : null;
        $selectedCentre = request()->input('centre') ? $allCentres->where('id', request()->input('centre'))->first() : null;

        $colOptions = [
            __('employee_main.remunerations.employee_list.name') => ['type' => 'value', 'path_in_collection' => 'full_name'],
            __('employee_main.remunerations.employee_list.position') => ['type' => 'many_values', 'path_in_collection' => 'validAssigments', 'get_attribute' => 'position.name'],
        ];

        $validAssigments = $this->getValidAssigmentsWithChildren(false);

        if($validAssigments === null && !auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_CREATOR_WITHOUT_HIERARCHY)) {
            return to_route('dashboard');
        }

        if($validAssigments === null) {
            $validAssigments = collect();
        }

        $departmentCentreValidPairs = [];
        foreach($validAssigments as $va) {
            foreach($allPositions[$va->position_id]->pivotDepartments as $pd){
                if(!in_array($va->centre_id, $departmentCentreValidPairs[$pd->department_id] ?? [], false)) {
                    $departmentCentreValidPairs[$pd->department_id][] = $va->centre_id;
                }
            }
        }

        $assigmentCentres = $allCentres->whereIn('id', $validAssigments->pluck('centre_id')->unique());
        $assigmentCentresDepartmentsIds = $assigmentCentres->pluck('pivotDepartments.*.department_id')->unique()->flatten();

        $assigmentPositions = $allPositions
            ->whereIn('id', $validAssigments->pluck('position_id')->unique());

        $assigmentSections = $allSections->whereIn('id', $assigmentPositions->pluck('pivotSections')->flatten()->unique('section_id')->pluck('section_id'));
        $assigmentDepartments = $allDepartments->whereIn('id', $assigmentPositions->pluck('pivotDepartments')->flatten()->unique('department_id')->pluck('department_id'))->filter(function($value, $key) use ($assigmentCentresDepartmentsIds) {
            return $assigmentCentresDepartmentsIds->contains($key);
        });

        if($assigmentSections->count() === 1) {
            $selectedSection = $assigmentSections->first();
        }

        if($assigmentDepartments->count() === 1) {
            $selectedDepartment = $assigmentDepartments->first();
        }

        if($assigmentCentres->count() === 1) {
            $selectedCentre = $assigmentCentres->first();
        }

        $filteredAssigments = clone $validAssigments;
        if(is_a($filteredAssigments, Collection::class)) {
            $filteredAssigments->loadMissing('position.pivotSections', 'position.pivotDepartments');
        }

        if($selectedSection) {
            $filteredAssigments = $filteredAssigments->filter(function($assigment, $key) use ($selectedSection) {
                $sectionsIds = $assigment->position->pivotSections->pluck('section_id')->unique()->toArray() ?? [];
                return in_array($selectedSection?->id, $sectionsIds, false);
            });
        }

        if($selectedCentre) {
            $filteredAssigments = $filteredAssigments->where('centre_id', $selectedCentre?->id);
        }

        if($selectedDepartment) {
            $filteredAssigments = $filteredAssigments->filter(function($assigment, $key) use ($selectedDepartment) {
                $departmentsIds = $assigment->position->pivotDepartments->pluck('department_id')->unique()->toArray() ?? [];
                return in_array($selectedDepartment?->id, $departmentsIds, false);
            });
        } else {
            $finalDepartments = $assigmentDepartments;
            if($selectedCentre) {
                $selectedCentre->loadMissing('departments');
                $centreDepartments = $selectedCentre->departments;
                $finalDepartments = $assigmentDepartments->intersect($centreDepartments);
            }

            if($finalDepartments->count() === 1) {
                $selectedDepartment = $finalDepartments->first();
            }
        }

        $users = User::whereIn('id', $filteredAssigments->pluck('user_id')->unique())
            ->with('validAssigments.position', 'validAssigments.centre')
            ->when('')
            ->orderBy('last_name')->orderBy('first_name')
            ->paginate(20);

        $validity = $request->validated('validity') ?? date('Y-m-01', strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_open_last_month')) ? 'last month' : 'this month'));

        $activeProposalsCount = Proposal::where('approver_id', auth()->id())
            ->where('validity', $validity)
            ->where('status', ProposalStatus::PENDING->value)
            ->selectRaw('count(*) as count, centre_id, department_id')
            ->groupByRaw('centre_id, department_id')
            ->get();


        $userParentAssigments = $this->getValidAssigmentsWithParents();

        $parentUsersIds = $userParentAssigments?->pluck('user_id')?->toArray() ?? [];

        $parentUsers = User::query()
            ->whereIn('id', $parentUsersIds)
            ->select('id', 'first_name', 'last_name')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->with('validAssigments.position')
            ->get();
        $additionalApprovers = auth()->user()->remunerationProposalAdditionalApprovers()->with('validAssigments.position')->get();
        $parentUsers = $parentUsers->concat($additionalApprovers);

        $createProposalService = new CreateProposalService();
        $selectedApproverId = $createProposalService->getUserPreselectedApproverId(auth()->user(), $userParentAssigments);
        $isManuallySelectedApprover = false;
        $sessionApproverId = session()->get('remunerations.proposals.list.approver');
        if (!is_null($sessionApproverId)) {
            if ($parentUsers->pluck('id')->contains($sessionApproverId)) {
                $selectedApproverId = $sessionApproverId;
                $isManuallySelectedApprover = true;
            }
        }
        $isFinalApprover = auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER);
        $previousCentreData = [];
        $nextCentreData = [];
        if($selectedSection && $selectedDepartment && $selectedCentre) {
            $departmentCentresForPrevNext = null;
            try{
                $departmentCentresForPrevNext = $allSections[$selectedSection->id]->departments->where('id', $selectedDepartment->id)->first()->centres;
            } catch(\Exception $e) {}

            if(!is_null($departmentCentresForPrevNext)) {
                $activeProposalsCountGrouped = $activeProposalsCount->where('department_id', $selectedDepartment->id)->pluck('centre_id');

                $departmentCentresForPrevNext = $departmentCentresForPrevNext->whereIn('id', $activeProposalsCountGrouped);
                $previousCentreWithProposals = $departmentCentresForPrevNext->where('code', '<', $selectedCentre->code)
                    ->sortByDesc('code')
                    ->first();
                if (!is_null($previousCentreWithProposals)) {
                    $previousCentreData = [
                        'url' => route(
                            'employee.remunerations.proposals.index.table',
                            [
                                'section' => $selectedSection->id,
                                'department' => $selectedDepartment->id,
                                'centre' => $previousCentreWithProposals->id,
                                'validity' => $validity
                            ],
                        ),
                        'centre_code' => $previousCentreWithProposals->code

                    ];
                }
                $nextCentreWithProposals = $departmentCentresForPrevNext->where('code', '>', $selectedCentre->code)
                    ->sortBy('code')
                    ->first();

                if (!is_null($nextCentreWithProposals)) {
                    $nextCentreData = [
                        'url' => route(
                            'employee.remunerations.proposals.index.table',
                            [
                                'section' => $selectedSection->id,
                                'department' => $selectedDepartment->id,
                                'centre' => $nextCentreWithProposals->id,
                                'validity' => $validity
                            ],
                        ),
                        'centre_code' => $nextCentreWithProposals->code

                    ];
                }
            }
        }

        return view('employee.remunerations.proposals.index', compact(
            'users',
            'colOptions',
            'assigmentCentres',
            'assigmentPositions',
            'assigmentSections',
            'assigmentDepartments',
            'selectedSection',
            'selectedDepartment',
            'selectedCentre',
            'allSections',
            'activeProposalsCount',
            'validity',
            'departmentCentreValidPairs',
            'selectedApproverId',
            'parentUsers',
            'isFinalApprover',
            'previousCentreData',
            'nextCentreData',
        ));
    }

    public function listIndex(string $tab = null): View|RedirectResponse
    {
        $validAssigments = $this->getValidAssigmentsWithChildren(true);

        $loggedUser = auth()->user();
        $loggedUser->loadMissing('validAssigments');

        if($validAssigments === null && !auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_CREATOR_WITHOUT_HIERARCHY)) {
            return to_route('dashboard');
        }

        if($validAssigments === null) {
            $validAssigments = collect();
            $canBeShownDown = false;
        } else {
            $canBeShownDown = $this->getIfCanBeShownDownTab($validAssigments);
        }

        $allowedTabs = ($canBeShownDown) ? ['my', 'up', 'down', 'completed'] : ['my', 'up', 'completed'];
        if(auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
            $allowedTabs = ['my', 'down', 'completed'];
        }
        $tab = in_array($tab, $allowedTabs, false) ? $tab : 'my';

        [$proposalsTableHeaders, $proposalsTableDynamicColumns] = $this->getListIndexTableColumns($tab);

        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter'),
            'employee_remunerations_proposals_'.$tab
        );

        $fullNameSearchValue = $filterData['filter']['custom']['like']['full_name'] ?? null;
        $validityFromValue = $filterData['filter']['custom']['equal']['validity_from'] ?? null;
        $validityToValue = $filterData['filter']['custom']['equal']['validity_to'] ?? null;

        $proposals = Proposal::with([
                'currency',
                'user:id,login,first_name,last_name',
                'user.validAssigments.position:id,code,name',
                'user.validAssigments.centre:id,code,name',
                'centre:id,code,name',
                'department:id,code,name',
                'approver:id,login,first_name,last_name',
                'lastLog:id,remuneration_proposal_logs.proposal_id,user_id,note',
            ])
            ->filter($filterData['filter'])
            ->when(!is_null($fullNameSearchValue), function ($query) use ($fullNameSearchValue){
                $query->whereHas('user', function ($query) use ($fullNameSearchValue){
                    $query->where(DB::raw('CONCAT(last_name, " ", first_name)'), 'like', '%'.$fullNameSearchValue.'%')
                    ->orWhere('login', 'like', $fullNameSearchValue);
                });
            })
            ->when(!is_null($validityFromValue), function ($query) use ($validityFromValue){
                $query->where('validity', '>=', $validityFromValue);
            })
            ->when(!is_null($validityToValue), function ($query) use ($validityToValue){
                $query->where('validity', '<=', $validityToValue);
            })
            ->where(function ($query) use ($tab, $validAssigments){
                $showOnlyValue = $tab;
                if(in_array($showOnlyValue, ['my', 'up', 'down'], false)){
                    $query->where('status', ProposalStatus::PENDING->value);

                    if($showOnlyValue === 'up') {
                        $downUsersIds = $validAssigments->pluck('user_id')
                            ->unique()
                            ->filter(fn($userId) => $userId !== auth()->id());

                        $query->where('approver_id', '!=', auth()->id());
                        $query->whereNotIn('approver_id', $downUsersIds);
                        $query->whereHas('logs', function ($query) {
                            $query->where('user_id', auth()->id());
                        });
                    } elseif($showOnlyValue === 'down') {
                        $downUsersIds = $validAssigments->pluck('user_id')
                            ->unique()
                            ->filter(fn($userId) => $userId !== auth()->id());

                        $query->whereIn('approver_id', $downUsersIds);
                    } else {
                        $query->where('approver_id', auth()->id());
                    }
                } else {
                    $query->whereIn('status', [ProposalStatus::APPROVED->value, ProposalStatus::REJECTED->value]);
                    $query->whereHas('logs', function ($query){
                        $query->where('user_id', auth()->id());
                    });
                }
            })
            ->orderBy('id', 'desc')
            ->paginate(50);

        $filterInputs = $this->getFilterInputs($tab);

        $userParentAssigments = $this->getValidAssigmentsWithParents();

        $parentUsersIds = $userParentAssigments?->pluck('user_id')?->toArray() ?? [];

        $parentUsers = User::query()
            ->whereIn('id', $parentUsersIds)
            ->select('id', 'first_name', 'last_name')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get();

        $createProposalService = new CreateProposalService();
        $selectedApproverId = $createProposalService->getUserPreselectedApproverId($loggedUser, $userParentAssigments);
        $isManuallySelectedApprover = false;

        if(is_null($selectedApproverId)) {
            $sessionApproverId = session()->get('remunerations.proposals.list.approver');
            if($parentUsers->pluck('id')->contains($sessionApproverId)) {
                $selectedApproverId = $sessionApproverId;
                $isManuallySelectedApprover = true;
            }
        }

        return view('employee.remunerations.proposals.index_list', compact(
            'proposals',
            'filterInputs',
            'filterData',
            'proposalsTableHeaders',
            'proposalsTableDynamicColumns',
            'selectedApproverId',
            'parentUsers',
            'isManuallySelectedApprover',
            'allowedTabs',
            'tab',
        ));
    }

    public function export(string $tab = null): BinaryFileResponse
    {
        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter'),
            'employee_remunerations_proposals_'.$tab
        );

        return Excel::download(new ProposalsExport($tab, $filterData), 'proposals_'.$tab.'.xlsx');
    }

    public function create(CreateProposalRequest $request): View
    {
        $types = ProposalType::cases();
        $selectedType = $request->validated('type', null);
        if(!is_null($selectedType)) {
            $selectedType = ProposalType::from($selectedType);
        }

        if(empty($selectedType)){
            return view('employee.remunerations.proposals.create', compact(
                'types',
                'selectedType',
            ));
        }

        $cacheService = new CacheService();
        $currencies = $cacheService->getActiveCurrencies();
        $centres = $cacheService->getCentres();
        $departments = $cacheService->getDepartments();
        $allUsers = $cacheService->allUsersWithValidAssigmentsForProposals();

        $createProposalService = new CreateProposalService();

        $loggedUser = auth()->user();
        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents',
            'remunerationProposalAdditionalApproversPivot',
        );

        $userParentAssigments = $this->getValidAssigmentsWithParents();

        $selectedApproverId = $createProposalService->getUserPreselectedApproverId($loggedUser, $userParentAssigments);

        if(is_null($selectedApproverId)) {
            if(auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
                $selectedApproverId = auth()->id();
            }
        }

        $parentUsersIds = $userParentAssigments?->pluck('user_id')?->toArray() ?? [];
        $parentUsersIds[] = auth()->id();

        if($loggedUser->remunerationProposalAdditionalApproversPivot->count()) {
            $additionalParentUsers = $loggedUser->remunerationProposalAdditionalApproversPivot->pluck('approver_id')->toArray();
            $parentUsersIds = array_unique(array_merge($parentUsersIds, $additionalParentUsers));
        }

        $parentUsers = User::query()
            ->whereIn('id', $parentUsersIds)
            ->select('id', 'first_name', 'last_name')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get();

        $myDepartmentsIds = $createProposalService->getUserDepartmentsIds($loggedUser);
        $myCentresIds = $createProposalService->getUserCentresIds($loggedUser);

        $myCentres = $centres->whereIn('id', $myCentresIds) ->values();
        $myDepartments = $departments->whereIn('id', $myDepartmentsIds)->values();

        $selectedCentreId = null;
        $selectedCentre = ($myCentres->count() === 1) ? $myCentres->first() : null;

        if($selectedCentre) {
            $selectedCentre->loadMissing('departments');
            $myDepartments = $myDepartments->filter(function ($department) use ($selectedCentre) {
                return $selectedCentre->departments->contains($department);
            })->values();
            $selectedCentreId = $selectedCentre->id;
        }

        $selectedDepartmentId = ($myDepartments->count() === 1) ? $myDepartments->first()->id : null;

        $selectedCurrencyId = null;
        if(($myCentresCurrenciesIds = $myCentres->pluck('address.country.currency_id'))->unique()->count() === 1) {
            $selectedCurrencyId = $myCentresCurrenciesIds->first();
        }

        if($selectedType !== ProposalType::SALARY) {
            $validityCurrentDate = date('Y-m-01', strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? 'last month' : 'this month'));
            $validityMinDate = $validityCurrentDate;
            if (auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
                $validityMinDate = date('Y-m-01', strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? '-2 months' : 'last month'));
            }
        } else {
            $validityCurrentDate = date('Y-m-01', strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? 'this month' : 'next month'));
            $validityMinDate = $validityCurrentDate;
            if (auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
                $validityMinDate = date('Y-m-01', strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? 'last month' : 'this month'));
            }
        }

        return view('employee.remunerations.proposals.create', compact(
            'types',
            'selectedType',
            'allUsers',
            'parentUsers',
            'currencies',
            'centres',
            'departments',
            'myCentres',
            'myDepartments',
            'selectedCentreId',
            'selectedDepartmentId',
            'selectedCurrencyId',
            'selectedApproverId',
            'validityMinDate',
            'validityCurrentDate',
        ));
    }

    public function store(StoreProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();
        $validatedRequestData['proposer_id'] = auth()->id();

        $isApproved = false;
        if(auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
            if((int) $validatedRequestData['approver_id'] === auth()->id()) {
                $isApproved = true;
            }
        }

        $validatedRequestData['is_paid_via_agreement'] = $this->getPreselectedIsPaidViaAgreementValue($validatedRequestData);
        if($isApproved) {
            $validatedRequestData['payment_status'] = ProposalPaymentStatus::TO_BE_NOTIFIED->value;
            $validatedRequestData['contract_status'] = ProposalContractStatus::TO_BE_ASSIGNED->value;
        }

        if($errorReturnFromService = (new CreateProposalService)->createMultipleProposals($validatedRequestData, $isApproved)) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(trans_choice('employee.remunerations.proposals.create.success_message', count($validatedRequestData['users'])));
        return to_route('employee.remunerations.proposals.index.list');
    }

    public function show(Proposal $proposal, ShowProposalRequest $request): View
    {
        $proposal->loadMissing([
            'user:id,login,first_name,last_name',
            'currency:id,code',
            'proposer:id,first_name,last_name',
            'centre:id,code,name',
            'department:id,code,name',
        ]);

        $logs = $proposal->logs()->latest()->paginate(10);

        $childUserAssigments = $this->getValidAssigmentsWithChildrenForRemunerationProposals(true);
        $childUserIds = $childUserAssigments?->pluck('user_id')?->unique() ?? collect();

        $logsTableHeaders = [
            __('employee.remunerations.proposals.show.logs.created_at'),
            __('employee.remunerations.proposals.show.logs.user'),
            __('employee.remunerations.proposals.show.logs.status'),
            __('employee.remunerations.proposals.show.logs.note'),
        ];

        return view('employee.remunerations.proposals.show', compact(
            'proposal',
            'childUserIds',
            'logs',
            'logsTableHeaders',
        ));
    }

    public function edit(Proposal $proposal, EditProposalRequest $request): View|RedirectResponse
    {
        $proposal->loadMissing('user:id,login,first_name,last_name');

        $cacheService = new CacheService();
        $currencies = $cacheService->getActiveCurrencies();
        $centres = $cacheService->getCentres();
        $departments = $cacheService->getDepartments();

        $createProposalService = new CreateProposalService();

        $loggedUser = auth()->user();
        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents'
        );

        $userParentAssigments = $this->getValidAssigmentsWithParents();

        $selectedApproverId = $createProposalService->getUserPreselectedApproverId($loggedUser, $userParentAssigments);

        if(is_null($selectedApproverId)) {
            if(auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
                $selectedApproverId = auth()->id();
            }
        }

        $parentUsersIds = $userParentAssigments?->pluck('user_id')?->toArray() ?? [];
        $parentUsersIds[] = auth()->id();

        $parentUsers = User::query()
            ->whereIn('id', $parentUsersIds)
            ->select('id', 'first_name', 'last_name')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get();

        $myDepartmentsIds = $createProposalService->getUserDepartmentsIds($loggedUser);
        $myCentresIds = $createProposalService->getUserCentresIds($loggedUser);

        $myCentres = $centres->whereIn('id', $myCentresIds);
        $myDepartments = $departments->whereIn('id', $myDepartmentsIds);

        $showDepartmentSelector = ($myCentres->filter(fn($centre) => !$centre->isRetailCentre())->count() > 0);

        $validityCurrentDate = date('Y-m-01', strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? 'last month' : 'this month'));
        $validityMinDate = $validityCurrentDate;
        if(auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
            $validityMinDate = date('Y-m-01', strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? '-2 months' : 'last month'));
        }

        return view('employee.remunerations.proposals.edit', compact(
            'proposal',
            'parentUsers',
            'currencies',
            'centres',
            'departments',
            'myCentres',
            'myDepartments',
            'showDepartmentSelector',
            'selectedApproverId',
            'validityMinDate',
            'validityCurrentDate',
        ));
    }

    public function update(Proposal $proposal, UpdateProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        $isApproved = false;
        if(auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
            if((int) $validatedRequestData['approver_id'] === auth()->id()) {
                $isApproved = true;
            }
        }

        if($errorReturnFromService = (new UpdateProposalService())->updateProposal($proposal, $validatedRequestData, auth()->id(), $isApproved)) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.edit.success_message'));
        return to_route('employee.remunerations.proposals.index.list');
    }

    public function approve(Proposal $proposal, ApproveProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->approveProposal($proposal, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.approve.success_message'));
        return back();
    }

    public function usurp(Proposal $proposal, UsurpProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->usurpProposal($proposal, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.usurp.success_message'));
        return back();
    }

    public function withdraw(Proposal $proposal, WithdrawProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->withdrawProposal($proposal, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.withdraw.success_message'));
        return back();
    }

    public function rollback(Proposal $proposal, RollbackProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->rollbackProposal($proposal, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.rollback.success_message'));
        return back();
    }

    public function forward(Proposal $proposal, User $approver, ForwardProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->forwardProposal($proposal, $approver, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.forward.success_message'));
        return back();
    }

    public function reject(Proposal $proposal, RejectProposalRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->rejectProposal($proposal, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.reject.success_message'));
        return back();
    }

    public function changeIsPaidViaAgreement(Proposal $proposal, ChangeIsPaidViaAgreementRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->changeIsPaidViaAgreementForProposal($proposal, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.change_is_paid_via_agreement.success_message'));
        return back();
    }

    public function changePaymentStatus(Proposal $proposal, ChangePaymentStatusRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        if($errorReturnFromService = (new UpdateProposalService())->changePaymentStatusForProposal($proposal, $validatedRequestData, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.change_payment_status.success_message'));
        return back();
    }

    public function changeStatusForMultiple(ChangeStatusForMultipleRequest $request): RedirectResponse
    {
        $validatedRequestData = $request->validated();

        $proposals = Proposal::select('id', 'approver_id', 'status')->whereIn('id', $validatedRequestData['checked_proposals'])->get();
        $approverId = $validatedRequestData['approver_id'];
        $newStatus = ProposalStatus::from($validatedRequestData['status']);

        if($errorReturnFromService = (new UpdateProposalService())->changeStatusForMultipleProposals($proposals, $newStatus, $validatedRequestData, $approverId, auth()->id())) {
            throw ValidationException::withMessages([$errorReturnFromService]);
        }

        flash()->success(__('employee.remunerations.proposals.status_multiple.message.'.$newStatus->name));
        return to_route('employee.remunerations.proposals.index.list');
    }

    public function saveApprover(): RedirectResponse
    {
        session()->put('remunerations.proposals.list.approver', (int) request()->input('approver_id'));

        flash()->success(__('employee.remunerations.proposals.save_approver.success_message'));

        return back();
    }

    private function getFilterInputs(string $tab): array
    {
        $months = collect();
        $period = CarbonPeriod::create(now()->startOfMonth(), '1 month', Carbon::parse('2025-04-01'))->invert();
        foreach($period as $date) {
            $months->push((object) ['value' => $date->startOfMonth()->toDateSTring(), 'label' => $date->format('m / Y')]);
        }

        $filterInputs = [
            [
                'attribute' => 'full_name',
                'label' => __('employee.remunerations.proposals.list.user'),
                'icon' => '',
                'placeholder' => '',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'validity_from',
                'label' => __('employee.remunerations.proposals.list.validity_from'),
                'icon' => '',
                'placeholder' => '',
                'options' => $months,
                'searchProperty' => 'label',
                'inputProperty' => 'value',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'validity_to',
                'label' => __('employee.remunerations.proposals.list.validity_to'),
                'icon' => '',
                'placeholder' => '',
                'options' => $months,
                'searchProperty' => 'label',
                'inputProperty' => 'value',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'centre_id',
                'label' => __('employee.remunerations.proposals.list.created_by_centre'),
                'icon' => '',
                'placeholder' => '',
                'options' => Centre::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'department_id',
                'label' => __('employee.remunerations.proposals.list.created_by_department'),
                'icon' => '',
                'placeholder' => '',
                'options' => Department::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'type',
                'label' => __('employee.remunerations.proposals.list.type'),
                'icon' => '',
                'placeholder' => '',
                'options' => ProposalType::casesForFilter(),
                'searchProperty'=>'translation',
                'inputProperty'=>'value',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'subtype',
                'label' => __('employee.remunerations.proposals.list.subtype'),
                'icon' => '',
                'placeholder' => '',
                'options' => ProposalSubType::casesForFilter(),
                'searchProperty'=>'translation',
                'inputProperty'=>'value',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
        ];

        if($tab === 'completed') {
            $filterInputs[] = [
                'attribute' => 'status',
                'label' => __('employee.remunerations.proposals.list.status'),
                'icon' => '',
                'placeholder' => '',
                'options' => ProposalStatus::casesForFilter(),
                'searchProperty'=>'translation',
                'inputProperty'=>'value',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ];

            if(auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)){
                $filterInputs[] = [
                    'attribute' => 'status',
                    'label' => __('employee.remunerations.proposals.list.payment_status'),
                    'icon' => '',
                    'placeholder' => '',
                    'options' => ProposalPaymentStatus::casesForFilter(),
                    'searchProperty'=>'translation',
                    'inputProperty'=>'value',
                    'allowedActions' => [
                        'equal' => "=",
                        'notEqual' => "!=",
                        'in' => "[.]",
                        'notIn' => "![.]"
                    ]
                ];
            }
        }

        return $filterInputs;
    }

    private function getIfCanBeShownDownTab(\Illuminate\Database\Eloquent\Collection $validAssigments): bool
    {
        $downUsersIds = $validAssigments->pluck('user_id')
            ->unique()
            ->filter(fn($userId) => $userId !== auth()->id());

        return (bool) Proposal::whereIn('approver_id', $downUsersIds)
            ->where('status', ProposalStatus::PENDING->value)
            ->count();
    }

    private function getListIndexTableColumns(string $tab): array
    {
        $proposalsTableHeaders = [
            __('employee.remunerations.proposals.list.month'),
            __('employee.remunerations.proposals.list.created_by'),
            __('employee.remunerations.proposals.list.user'),
            __('employee.remunerations.proposals.list.type'),
            __('employee.remunerations.proposals.list.value'),
            __('employee.remunerations.proposals.list.reason'),
        ];
        $proposalsTableDynamicColumns = [];

        if($tab === 'completed') {
            $proposalsTableHeaders[] = __('employee.remunerations.proposals.list.status');
        }

        if(in_array($tab, ['up', 'down'], false)) {
            $proposalsTableHeaders[] = __('employee.remunerations.proposals.list.current_approver');
            $proposalsTableDynamicColumns[] = 'current_approver';
        }

        if($tab === 'completed' && auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
            $proposalsTableHeaders[] = __('employee.remunerations.proposals.list.payment_status');
            $proposalsTableDynamicColumns[] = 'payment_status';

        }

        if($tab === 'my') {
            array_unshift($proposalsTableHeaders, __('employee.remunerations.proposals.list.checkbox'));
            $proposalsTableHeaders[] = '<div class="flex flex-col lg:flex-row justify-between items-center gap-2">'.__('employee.remunerations.proposals.list.actions').' <a href="'.route('employee.remunerations.proposals.create').'" class="btn btn-primary btn-soft btn-sm h-auto p-2">'.__('employee.remunerations.proposals.list.add_proposal').'</a></div>';
        } else {
            $proposalsTableHeaders[] = __('employee.remunerations.proposals.list.actions');
        }

        $proposalsTableDynamicColumns[] = 'actions';

        return [$proposalsTableHeaders, $proposalsTableDynamicColumns];
    }
}
