<?php

namespace App\Http\Controllers\Employee\Remuneration;

use App\Http\Controllers\Controller;
use App\Http\Requests\Employee\Remuneration\SalaryPlan\StoreRequest;
use App\Models\Base\Currency;
use App\Models\Organisation\Centre;
use App\Models\Organisation\CentreDepartment;
use App\Models\Organisation\Department;
use App\Models\Organisation\DepartmentPosition;
use App\Models\Organisation\Position;
use App\Models\Person\Contract;
use App\Models\Person\User;
use App\Models\Remuneration\Salary;
use App\Models\Remuneration\SalaryPlan;
use App\Services\FilterService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class SalaryController extends Controller
{
    use PermissionTrait;

    public function index(): View|RedirectResponse
    {
        $validAssigments = $this->getValidAssigmentsWithChildrenForRemunerationSalaries(false);

        $loggedUser = auth()->user();
        $loggedUser->loadMissing('validAssigments');

        if($validAssigments === null) {
            return to_route('dashboard');
        }

        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter')
        );

        $fullNameSearchValue = $filterData['filter']['custom']['like']['full_name'] ?? null;
        $departmentFilter = $filterData['filter']['custom']['equal']['department'] ?? null;
        $centreFilter = $filterData['filter']['custom']['equal']['centre'] ?? null;
        $positionFilter = $filterData['filter']['custom']['equal']['position'] ?? null;
        $showSalaries = $filterData['filter']['custom']['equal']['show_salaries'] ?? null;

        if(!empty($departmentFilter)) {
            $pd = DepartmentPosition::where('department_id', $departmentFilter)->pluck('position_id');
            $cd = CentreDepartment::where('department_id', $departmentFilter)->pluck('centre_id');

            $validAssigments = $validAssigments->whereIn('position_id', $pd)->whereIn('centre_id', $cd);
        }

        if(!empty($centreFilter)) {
            $validAssigments = $validAssigments->where('centre_id', $centreFilter);
        }

        if(!empty($positionFilter)) {
            $validAssigments = $validAssigments->whereIn('position_id', $positionFilter);
        }

        $contracts = Contract::with([
                'user:id,login,first_name,last_name',
                'user.validAssigments.position:id,code,name',
                'user.validAssigments.centre:id,code,name',
            ])
            ->withWhereHas('currentSalary')
            ->filter($filterData['filter'])
            ->when(!is_null($fullNameSearchValue), function ($query) use ($fullNameSearchValue){
                $query->whereHas('user', function ($query) use ($fullNameSearchValue){
                    $query->where(DB::raw('CONCAT(last_name, " ", first_name)'), 'like', '%'.$fullNameSearchValue.'%')
                        ->orWhere('login', 'like', $fullNameSearchValue);
                });
            })
            ->whereIn('user_id', $validAssigments->pluck('user_id')->unique())
            ->orderBy('id', 'desc')
            ->paginate(20);

        $allCurrencies = Currency::get()->keyBy('id');

        $filterInputs = [
            [
                'attribute' => 'full_name',
                'label' => __('employee.remunerations.salaries.list.user'),
                'icon' => '',
                'placeholder' => '',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'centre',
                'label' => __('employee.remunerations.salaries.list.centre'),
                'icon' => '',
                'placeholder' => '',
                'options' => Centre::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'department',
                'label' => __('employee.remunerations.salaries.list.department'),
                'icon' => '',
                'placeholder' => '',
                'options' => Department::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'position',
                'label' => __('employee.remunerations.salaries.list.position'),
                'icon' => '',
                'placeholder' => '',
                'options' => Position::active()->select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'show_salaries',
                'label' => __('employee.remunerations.salaries.list.show_salaries'),
                'icon' => '',
                'placeholder' => '',
                'options' => collect([['value' => '', 'translation' => __('main.no')], ['value' => 'yes', 'translation' => __('main.yes')]]),
                'searchProperty' => 'translation',
                'disableAllOption' => true,
                'inputProperty' => 'value',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
        ];


        return view('employee.remunerations.salaries.index', compact(
            'contracts',
            'filterInputs',
            'filterData',
            'allCurrencies',
            'showSalaries',
        ));
    }

    public function history(Contract $contract): View|RedirectResponse
    {
        $validAssigments = $this->getValidAssigmentsWithChildrenForRemunerationSalaries(false);

        if(is_null($validAssigments) || !$validAssigments->pluck('user_id')->contains($contract->user_id)) {
            return to_route('dashboard');
        }

        $contract->load('user', 'company', 'currentSalary');

        $salaries = $contract->salaries()
            ->with('currency')
            ->orderBy('validity', 'desc')
            ->orderBy('id', 'desc')
            ->paginate(20);

        return view('employee.remunerations.salaries.history', compact(
            'contract',
            'salaries',
        ));
    }

    public function plans(): View|RedirectResponse
    {
        $validAssigments = $this->getValidAssigmentsWithChildrenForRemunerationSalaries(false);

        $loggedUser = auth()->user();
        $loggedUser->loadMissing('validAssigments');

        if($validAssigments === null) {
            return to_route('dashboard');
        }

        $salaryPlans = SalaryPlan::with([
                'user:id,login,first_name,last_name',
                'user.validAssigments',
                'user.validAssigments.centre',
                'user.validAssigments.position',
                'creator:id,login,first_name,last_name',
            ])
            ->where('validity', '>=', now()->startOfMonth()->toDateString())
            ->whereIn('user_id', $validAssigments->pluck('user_id')->unique())
            ->orderBy('validity', 'asc')
            ->paginate(20);

        $allCurrencies = Currency::get()->keyBy('id');

        $employees = User::whereIn('id', $validAssigments->pluck('user_id')->unique())
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get()
            ->map(function ($user) {
                $selectOption = $user->full_name;
                $user->select_option = $selectOption;
                return $user;
            });

        $validityMinDate = now()->addMonths(2);

        return view('employee.remunerations.salaries.plans', compact(
            'salaryPlans',
            'allCurrencies',
            'employees',
            'validityMinDate',
        ));
    }

    public function createPlan(StoreRequest $request): RedirectResponse
    {
        SalaryPlan::updateOrCreate(
            [
                'user_id' => $request->integer('user_id'),
                'validity' => $request->date('validity'),
            ],
            $request->validated() +
            [
                'creator_id' => auth()->id()
            ]
        );

        flash()->success(__('employee.remunerations.salaries.plans.create.created'));

        return to_route('employee.remunerations.salaries.plans.index');
    }
}
