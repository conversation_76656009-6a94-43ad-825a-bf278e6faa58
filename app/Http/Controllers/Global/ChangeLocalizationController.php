<?php

namespace App\Http\Controllers\Global;

use App\Http\Controllers\Controller;
use App\Models\Person\User;
use App\Services\Person\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Opcodes\LogViewer\Facades\Cache;

class ChangeLocalizationController extends Controller
{
    public function __invoke(Request $request)
    {
        $request->validate(['lang'=>['string', 'required', 'min:2', 'max:2']]);

        $languages = config('vermont.available_localizations');
        $selectedLang = in_array($request->lang, $languages)
            ? $request->lang
            : 'en';

        App::setLocale($selectedLang);

        if(auth()->id()) {
            User::where('id', auth()->id())->update([
                'lang' => $selectedLang
            ]);
            UserService::clearMenuItemsCache(auth()->id());
            UserService::clearHrMenuItemsCache(auth()->id());
            UserService::clearItMenuItemsCache(auth()->id());
        }

        return back()->with('success', __('actions.success-language-changed'));

    }
}
