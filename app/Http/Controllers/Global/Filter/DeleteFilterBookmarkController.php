<?php

namespace App\Http\Controllers\Global\Filter;

use App\Http\Controllers\Controller;
use App\Models\Person\UserFilterBookmark;
use Illuminate\Http\Request;

class DeleteFilterBookmarkController extends Controller
{


    public function __invoke(Request $request, UserFilterBookmark $userFilterPreset)
    {
        $explodedUrl = explode('?', url()->previous());
        $userFilterPreset->delete();
        return redirect(reset($explodedUrl));
    }
}
