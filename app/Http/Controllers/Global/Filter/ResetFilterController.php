<?php

namespace App\Http\Controllers\Global\Filter;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;

class ResetFilterController extends Controller
{

    public function __invoke(string $filterName): RedirectResponse
    {
        Session::remove($filterName);
        $explodedUrl = explode('?', url()->previous());

        return redirect(reset($explodedUrl));
    }
}
