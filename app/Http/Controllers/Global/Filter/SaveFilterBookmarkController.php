<?php

namespace App\Http\Controllers\Global\Filter;

use App\Http\Controllers\Controller;
use App\Models\Person\UserFilterBookmark;

class SaveFilterBookmarkController extends Controller
{


    public function __invoke(string $filterName, string $userFilterName, string $filter)
    {
        $explodedUrl = explode('?', url()->previous());
        UserFilterBookmark::create(
            [
                'user_id' => auth()->id(),
                'preset_name' => $userFilterName,
                'filter_name' => $filterName,
                'settings' => $filter
            ]
        );
        return redirect(reset($explodedUrl));
    }
}
