<?php

namespace App\Http\Controllers\Global\Filter;

use App\Http\Controllers\Controller;
use App\Models\SeasonOrder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;

class ResetFilterAttributeController extends Controller
{
    public function __invoke(
        string $filterName,
        string $filterAttribute = null,
        string $filterAttributeValue = null
    ): RedirectResponse {
        $filterData = collect(Session::get($filterName));

        $filterData = $filterData->map(function ($item, $key) use ($filterAttribute, $filterAttributeValue) {
            if ($key == 'custom' || $key == 'statistics' || 'centreFilter') {
                if (array_key_exists($filterAttribute, $item)) {
                    if ($filterAttributeValue) {
                        $valueKey = array_search($filterAttributeValue, $item[$filterAttribute]);
                        unset($item[$filterAttribute][$valueKey]);
                        $item[$filterAttribute] = array_values($item[$filterAttribute]);
                        if (empty($item[$filterAttribute])) {
                            unset($item[$filterAttribute]);
                        }
                        return $item;
                    }
                    unset($item[$filterAttribute]);
                }
                foreach ($item as $k => $i) {
                    if (is_array($i) && array_key_exists($filterAttribute, $i)) {
                        if ($filterAttributeValue) {
                            $valueKey = array_search($filterAttributeValue, $item[$k][$filterAttribute]);
                            unset($item[$k][$filterAttribute][$valueKey]);
                            $item[$k][$filterAttribute] = array_values($item[$k][$filterAttribute]);
                            if (empty($item[$k][$filterAttribute])) {
                                unset($item[$k][$filterAttribute]);
                            }
                            return $item;
                        }
                        unset($item[$k][$filterAttribute]);
                        return $item;
                    }
                }
            } else {
                if (array_key_exists($filterAttribute, $item)) {
                    if ($filterAttributeValue) {
                        $valueKey = array_search($filterAttributeValue, $item[$filterAttribute]);
                        unset($item[$filterAttribute][$valueKey]);
                        $item[$filterAttribute] = array_values($item[$filterAttribute]);
                        if (empty($item[$filterAttribute])) {
                            unset($item[$filterAttribute]);
                        }
                        return $item;
                    }
                    unset($item[$filterAttribute]);
                }
            }
            return $item;
        });

        Session::put($filterName, $filterData->toArray());

        $explodedUrl = explode('?', url()->previous());

        return redirect($explodedUrl[0]);
    }
}
