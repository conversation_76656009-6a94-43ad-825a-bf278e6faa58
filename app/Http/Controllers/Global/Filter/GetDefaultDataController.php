<?php

namespace App\Http\Controllers\Global\Filter;

use App\Http\Controllers\Controller;
use App\Services\FilterService;
use Session;

class GetDefaultDataController extends Controller
{
    public function __invoke(string $translationName = null)
    {
        if (!is_null($translationName)) {
            return response()->json(__('filter')[$translationName]);
        }
        $filterService = new FilterService();
        return response()->json($filterService->processAllFilterDataForUniversalFilterComponent([], 'undefined', 'undefined', true));
    }


}
