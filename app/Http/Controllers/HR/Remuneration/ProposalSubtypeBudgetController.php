<?php

namespace App\Http\Controllers\HR\Remuneration;

use App\Http\Controllers\Controller;
use App\Models\Organisation\Centre;
use App\Models\Remuneration\ProposalSubTypeBudget;
use Carbon\CarbonImmutable;

class ProposalSubtypeBudgetController extends Controller
{
    public function index(string $month = null)
    {
        $viewData['currentTab'] = 'proposal_subtype_budgets';
        $viewData['month'] = CarbonImmutable::parse((empty($month) ? strtotime(date('j') < 20 ? '-1 month' : 'this month') : $month))->startOfMonth();

        $viewData['budgets'] = ProposalSubTypeBudget::select(ProposalSubTypeBudget::getTableName().'.*', Centre::getTableName().'.code', Centre::getTableName().'.name')
            ->with('currency')
            ->where('validity', $viewData['month']->toDateString())
            ->leftJoin(Centre::getTableName(), ProposalSubTypeBudget::getTableName().'.centre_id', '=', Centre::getTableName().'.id')
            ->orderBy(Centre::getTableName().'.code')
            ->orderBy('subtype')
            ->get();

        return view('hr.remunerations.proposal_subtype_budgets.index', $viewData);
    }
}
