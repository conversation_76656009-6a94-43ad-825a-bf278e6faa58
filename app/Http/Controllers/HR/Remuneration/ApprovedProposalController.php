<?php

namespace App\Http\Controllers\HR\Remuneration;

use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalType;
use App\Exports\HR\Remunerations\ApprovedProposalsExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\HR\Remuneration\ApprovedProposal\UpdateProposalContractRequest;
use App\Models\Organisation\Centre;
use App\Models\Organisation\CentreDepartment;
use App\Models\Organisation\Company;
use App\Models\Organisation\Department;
use App\Models\Organisation\DepartmentPosition;
use App\Models\Organisation\Position;
use App\Models\Person\Contract;
use App\Models\Person\User;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\Salary;
use App\Services\FilterService;
use Carbon\CarbonImmutable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Mccarlosen\LaravelMpdf\Facades\LaravelMpdf;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;

class ApprovedProposalController extends Controller
{
    public function index(string $month = null): View
    {
        $viewData['currentTab'] = 'approved_proposals';
        $viewData['month'] = CarbonImmutable::parse((empty($month) ? strtotime(date('j') < 20 ? '-1 month' : 'this month') : $month))->startOfMonth();

        $viewData['filterInputs'] = self::indexFilterInputs();
        $viewData['filterData'] = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter'),
            'hr_remunerations_approved-proposals_index_export'
        );

        $fullNameFilter = $viewData['filterData']['filter']['custom']['like']['full_name'] ?? null;
        $assigmentDepartmentFilter = $viewData['filterData']['filter']['custom']['equal']['assigments_department'] ?? null;
        $proposalContractFilter = $viewData['filterData']['filter']['custom']['equal']['proposal_contract'] ?? null;
        $proposalTypeFilter = $viewData['filterData']['filter']['custom']['equal']['proposal_type'] ?? null;

        $viewData['proposalsWithoutContract'] = User::select('id')
            ->whereHas('validAssigments', function ($query) {
                $query->fromAllowedCountries();
            })
            ->whereHas('remunerationProposals', function ($query) use ($viewData) {
                $query
                    ->where('status', ProposalStatus::APPROVED)
                    ->where(function($query) {
                        $query->where('payment_status', '!=', ProposalPaymentStatus::WITHOUT_NOTIFICATION)
                            ->orWhereNull('payment_status');
                    })
                    ->where('type', '!=', ProposalType::NON_FINANCIAL_REWARD)
                    ->where('validity', $viewData['month']->toDateString())
                    ->whereNull('contract_id');
            })
            ->withCount(['remunerationProposals' => function ($query) use ($viewData) {
                $query
                    ->where('status', ProposalStatus::APPROVED)
                    ->where(function($query) {
                        $query->where('payment_status', '!=', ProposalPaymentStatus::WITHOUT_NOTIFICATION)
                            ->orWhereNull('payment_status');
                    })
                    ->where('type', '!=', ProposalType::NON_FINANCIAL_REWARD)
                    ->where('validity', $viewData['month']->toDateString())
                    ->whereNull('contract_id');
            }])
            ->get()
            ->sum('remuneration_proposals_count');

        $viewData['users'] = User::whereHas('assigments', function ($query) use ($viewData) {
                $query->fromAllowedCountries()
                    ->with('position:id,code,name,name_hr', 'centre:id,code,name');
            })
            ->with(['assigments' => function ($query) use ($viewData) {
                $query->fromAllowedCountries()
                    ->valid()
                    ->with('position:id,code,name,name_hr', 'centre:id,code,name');
            }])
            ->withWhereHas('remunerationProposals', function ($query) use ($viewData, $proposalContractFilter, $proposalTypeFilter) {
                $query
                    ->with('currency', 'contract', 'contract.company')
                    ->where('status', ProposalStatus::APPROVED)
                    ->where('type', '!=', ProposalType::NON_FINANCIAL_REWARD)
                    ->where(function($query) {
                        $query->where('payment_status', '!=', ProposalPaymentStatus::WITHOUT_NOTIFICATION)
                            ->orWhereNull('payment_status');
                    })
                    ->where('validity', $viewData['month']->toDateString())
                    ->when($proposalContractFilter === 'yes', function ($query) {
                        $query->whereNotNull('contract_id');
                    })
                    ->when($proposalContractFilter === 'no', function ($query) {
                        $query->whereNull('contract_id');
                    })
                    ->when($proposalTypeFilter, function ($query) use ($proposalTypeFilter) {
                        $query->where('type', $proposalTypeFilter);
                    });
            })
            ->with(['contracts' => function($query) use ($viewData){
                $query->where(function ($query) use ($viewData) {
                    $query->whereNull('start_date')
                        ->orWhere('start_date', '<=', $viewData['month']->endOfMonth()->toDateString());
                })->where(function ($query) use ($viewData) {
                    $query->whereNull('end_date')
                        ->orWhere('end_date', '>=', $viewData['month']->startOfMonth()->toDateString());
                });
            }, 'contracts.company'])
            ->when(!is_null($fullNameFilter), function ($query) use ($fullNameFilter){
                $query->where(DB::raw('CONCAT(last_name, " ", first_name)'), 'like', '%'.$fullNameFilter.'%')
                    ->orWhere('login', 'like', $fullNameFilter);
            })
            ->when(!is_null($assigmentDepartmentFilter), function ($query) use ($assigmentDepartmentFilter, $viewData){
                $pd = DepartmentPosition::where('department_id', $assigmentDepartmentFilter)->pluck('position_id');
                $cd = CentreDepartment::where('department_id', $assigmentDepartmentFilter)->pluck('centre_id');
                $query->whereHas('assigments', function($query) use($pd, $cd , $viewData){
                    $query->whereIn('position_id', $pd)
                        ->whereIn('centre_id', $cd);
                });
            })
            ->filter($viewData['filterData']['filter'])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->paginate(50);

        return view('hr.remunerations.approved_proposals.index', $viewData);
    }

    public function print(Proposal $proposal): Response|null
    {
        $proposal->load([
            'contract',
            'contract.user',
            'contract.user.assigments' => function ($query) use ($proposal){
                $query->valid($proposal->validity->toDateString())
                    ->primary()
                    ->with('position');
            },
            'contract.company',
            'contract.company.address.country',
        ]);

        $positionsNames = $proposal->contract->user->assigments->pluck('position.name')->implode(', ');

        $documentName = 'salary_'.strtolower($proposal?->contract?->company?->address?->country?->code);

        $pdf = LaravelMpdf::loadView('hr.remunerations.approved_proposals.pdf.' . $documentName, compact('proposal', 'positionsNames'));
        return $pdf->stream('proposal_' . $proposal->id . '.pdf');
    }

    public function export(string $month = null): BinaryFileResponse
    {
        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter'),
            'hr_remunerations_approved-proposals_index_export'
        );

        return Excel::download(new ApprovedProposalsExport($month, $filterData), 'approved_proposals.xlsx');
    }

    private static function indexFilterInputs(): array
    {
        return [
            [
                'attribute' => 'full_name',
                'label' => __('hr.filters.full_name'),
                'icon' => '',
                'placeholder' => '',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'assigments.centre_id',
                'label' => __('hr.filters.centre'),
                'icon' => '',
                'placeholder' => '',
                'options' => Centre::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'assigments.position_id',
                'label' => __('hr.filters.position'),
                'icon' => '',
                'placeholder' => '',
                'options' => Position::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'assigments_department',
                'label' => __('hr.filters.department'),
                'icon' => '',
                'placeholder' => '',
                'options' => Department::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'proposal_type',
                'label' => __('hr.remunerations.approved_proposals.proposal_type'),
                'icon' => '',
                'placeholder' => '',
                'options' => ProposalType::casesForFilter(),
                'searchProperty' => 'translation',
                'inputProperty' => 'value',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'proposal_contract',
                'label' => __('hr.remunerations.approved_proposals.proposal_contract'),
                'icon' => '',
                'placeholder' => '',
                'options' => collect([['code' => 'no', 'name' => __('hr.remunerations.approved_proposals.proposal_contract_no')],['code' => 'yes', 'name' => __('hr.remunerations.approved_proposals.proposal_contract_yes')]]),
                'searchProperty' => 'name',
                'inputProperty' => 'code',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'remunerationProposals.contract.company_id',
                'label' => __('hr.filters.company'),
                'icon' => '',
                'placeholder' => '',
                'options' => Company::select(['id', 'code', DB::raw('CONCAT(code, " (", name, ")") as name')])->active()->orderBy('code')->get(),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
        ];
    }

    public function updateContractProposal(UpdateProposalContractRequest $request, Proposal $proposal, Contract $contract): RedirectResponse
    {
        $contract->load('company.address.country');
        if($contract->company->address->country->currency_id !== $proposal->currency_id) {
            flash()->error(__('hr.remunerations.approved_proposals.contract_set_error_currency'));
            return back();
        }

        $proposal->contract()->associate($contract);
        $proposal->save();

        if($proposal->type === ProposalType::SALARY) {
            Salary::create([
                'contract_id' => $contract->id,
                'validity' => $proposal->validity,
                'base' => $proposal->base,
                'addition' => $proposal->addition,
                'currency_id' => $proposal->currency_id,
                'creator_id' => $proposal->approver_id,
            ]);
        }

        flash()->success(__('hr.remunerations.approved_proposals.contract_set_success'));

        return back();
    }
}
