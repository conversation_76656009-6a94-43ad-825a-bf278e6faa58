<?php

namespace App\Http\Controllers\HR\User;

use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\HR\User\Contract\Salary\StoreContractSalaryRequest;
use App\Models\Person\Contract;
use App\Models\Person\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\ValidationException;

class ContractSalaryController extends Controller
{
    public function store(string $type, User $user, Contract $contract, StoreContractSalaryRequest $request): RedirectResponse
    {
        if($contract->user_id !== $user->id) {
            throw new ValidationException('Nope');
        }

        $currentAssigmentType = AssigmentTypeEnum::getFromUrlParameter($type);

        $createArray = $request->validated();
        $createArray['creator_id'] = auth()->id();

        $contract->salaries()->create($createArray);

        flash()->success(__('hr.users.contracts.edit.salary.create.success_message'));
        return to_route('hr.users.contracts.edit', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user, 'contract' => $contract]);
    }
}
