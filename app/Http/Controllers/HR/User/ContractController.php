<?php

namespace App\Http\Controllers\HR\User;

use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Http\Controllers\Controller;
use App\Models\Base\Currency;
use App\Models\Organisation\Company;
use App\Models\Person\Contract;
use App\Models\Person\User;
use App\Models\Remuneration\Salary;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class ContractController extends Controller
{
    public function show(string $type, User $user, Contract $contract): RedirectResponse
    {
        return to_route('hr.users.contracts.edit', ['type' => $type, 'user' => $user, 'contract' => $contract]);
    }

    public function create(string $type, User $user, Contract $contract): View
    {
        return view('hr.users.contracts.create', compact('type', 'user', 'contract'));
    }

    public function edit(string $type, User $user, Contract $contract): View
    {
        $viewData['currentAssigmentType'] = AssigmentTypeEnum::getFromUrlParameter($type);
        $viewData['allCompanies'] = Company::with('address:id,country_id', 'address.country:id,currency_id')->get();
        $viewData['allCurrencies'] = Currency::active()->get()->keyBy('id');

        $viewData['contractSalaries'] = $contract->salaries()->with('creator')->paginate(5);
        $viewData['salariesHeaders'] = [
            __('hr.users.contracts.edit.salary.validity'),
            __('hr.users.contracts.edit.salary.value'),
            __('hr.users.contracts.edit.salary.creator'),
        ];

        return view('hr.users.contracts.edit', compact('type', 'user', 'contract') + $viewData);
    }
}
