<?php

namespace App\Http\Controllers\HR\User;

use App\Enums\Person\Assigment\AssigmentStatusEnum;
use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\HR\User\Assigment\StoreAssigmentRequest;
use App\Http\Requests\HR\User\Assigment\UpdateAssigmentRequest;
use App\Models\Base\Country;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Position;
use App\Models\Organisation\PositionSection;
use App\Models\Person\Assigment;
use App\Models\Person\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AssigmentController extends Controller
{
    public function show(string $type, User $user, Assigment $assigment): RedirectResponse
    {
        $viewData['currentAssigmentType'] = AssigmentTypeEnum::getFromUrlParameter($type);
        return to_route('hr.users.assigments.edit', ['type' => $type, 'user' => $user, 'assigment' => $assigment]);
    }

    public function create(string $type, User $user): View
    {
        $viewData = $this->getViewDataForCreateAndEdit($type);

        return view('hr.users.assigments.create', compact('type', 'user') + $viewData);
    }

    public function store(string $type, User $user, StoreAssigmentRequest $request): RedirectResponse
    {
        $currentAssigmentType = AssigmentTypeEnum::getFromUrlParameter($type);

        $user->assigments()->create($request->validated());

        flash()->success(__('hr.users.assigments.create.success_message'));
        return to_route('hr.users.show', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user]);
    }

    public function edit(string $type, User $user, Assigment $assigment): View
    {
        $viewData = $this->getViewDataForCreateAndEdit($type);
        $assigment->load('position:id,code,name,name_hr', 'centre:id,code,name');

        return view('hr.users.assigments.edit', compact('type', 'user', 'assigment') + $viewData);
    }

    public function update(string $type, User $user, Assigment $assigment, UpdateAssigmentRequest $request): RedirectResponse
    {
        $currentAssigmentType = AssigmentTypeEnum::getFromUrlParameter($type);

        $assigment->update($request->validated());

        flash()->success(__('hr.users.assigments.edit.success_message'));
        return to_route('hr.users.show', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user]);
    }

    private function getViewDataForCreateAndEdit(string $type): array
    {
        $viewData['currentAssigmentType'] = AssigmentTypeEnum::getFromUrlParameter($type);
        $viewData['allPositions'] = Position::orderBy('code')->active()->get()->forFormSelect(optionColumn: 'code', optionDetailColumn: 'name');
        $viewData['allCentres'] = Centre::orderBy('code')->active()->get()->forFormSelect(optionColumn: 'code', optionDetailColumn: 'name');
        $viewData['allCountries'] = Country::orderBy('code')->get()->forFormSelect(optionColumn: 'code', optionDetailColumn: 'name');
        $viewData['allTypes'] = AssigmentTypeEnum::collectionForFormSelect();
        $viewData['allStatuses'] = AssigmentStatusEnum::collectionForFormSelect();

        return $viewData;
    }
}
