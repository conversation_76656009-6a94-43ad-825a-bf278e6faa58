<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Http\Requests\HR\Organisation\Position\AttachDepartmentRequest;
use App\Http\Requests\HR\Organisation\Position\AttachParentPositionRequest;
use App\Http\Requests\HR\Organisation\Position\AttachSectionRequest;
use App\Http\Requests\HR\Organisation\Position\CopyParentPositionRequest;
use App\Http\Requests\HR\Organisation\Position\DetachDepartmentRequest;
use App\Http\Requests\HR\Organisation\Position\DetachParentPositionRequest;
use App\Http\Requests\HR\Organisation\Position\DetachSectionRequest;
use App\Http\Requests\HR\Organisation\Position\UpdatePositionRequest;
use App\Models\Organisation\Department;
use App\Models\Organisation\Position;
use App\Models\Organisation\PositionHierarchy;
use App\Models\Organisation\Section;
use App\Services\FilterService;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class PositionController extends Controller
{
    public function index()
    {
        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter')
        );

        foreach(['equal', 'notEqual', 'lessThan', 'greaterThan'] as $positionsOperationValue) {
            $positionsCountValue = $filterData['filter']['custom'][$positionsOperationValue]['parents_count'] ?? null;
            if($positionsCountValue !== null) {
                break;
            }
        }
        foreach(['equal', 'notEqual', 'lessThan', 'greaterThan'] as $validAssigmentsOperationValue) {
            $validAssigmentsCountValue = $filterData['filter']['custom'][$validAssigmentsOperationValue]['valid_assigments_count'] ?? null;
            if($validAssigmentsCountValue !== null) {
                break;
            }
        }

        $positions = Position::query()
            ->filter($filterData['filter'])
            ->with('sections:id,code', 'departments:id,code')
            ->withCount('parents', 'children', 'validAssigments')
            ->when(!is_null($positionsCountValue), function ($query) use ($positionsCountValue, $positionsOperationValue){
                $realOperation = match ($positionsOperationValue) {
                    'equal' => '=',
                    'notEqual' => '!=',
                    'lessThan' => '<',
                    'greaterThan' => '>',
                };
                $query->having('parents_count', $realOperation, $positionsCountValue);
            })
            ->when(!is_null($validAssigmentsCountValue), function ($query) use ($validAssigmentsCountValue, $validAssigmentsOperationValue){
                $realOperation = match ($validAssigmentsOperationValue) {
                    'equal' => '=',
                    'notEqual' => '!=',
                    'lessThan' => '<',
                    'greaterThan' => '>',
                };
                $query->having('valid_assigments_count', $realOperation, $validAssigmentsCountValue);
            })
            ->orderBy('name')
            ->paginate(20);

        $colOptions = [
            __('hr.organisation.position.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.position.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.position.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium'],
            __('hr.organisation.position.name_hr') => ['type' => 'value', 'path_in_collection' => 'name_hr'],
            __('hr.organisation.position.section') => ['type' => 'many_values', 'path_in_collection' => 'sections', 'get_attribute' => 'code'],
            __('hr.organisation.position.department') => ['type' => 'many_values', 'path_in_collection' => 'departments', 'get_attribute' => 'code'],
            __('hr.organisation.position.parents_count') => ['type' => 'value', 'path_in_collection' => 'parents_count'],
            __('hr.organisation.position.children_count') => ['type' => 'value', 'path_in_collection' => 'children_count'],
            __('hr.organisation.position.valid_assigments_count') => ['type' => 'value', 'path_in_collection' => 'valid_assigments_count'],
            __('hr.organisation.position.is_active') => ['type' => 'yesno', 'path_in_collection' => 'is_active'],
            __('hr.organisation.position.action') => ['type' => 'actions', 'base_path' => route('hr.organisation.positions.index'), 'available_actions' => [ 'show' => false,'edit' => true,]],
        ];

        $filterInputs = [
            [
                'attribute' => 'code',
                'label' => __('hr.organisation.position.code'),
                'icon' => '',
                'placeholder' => '',
            ],
            [
                'attribute' => 'name',
                'label' => __('hr.organisation.position.name'),
                'icon' => '',
                'placeholder' => '',
                'allowedActions' => [
                    'like' => "~",
                    'in' => "[.]",
                    'notEqual' => "!=",
                ],
            ],
            [
                'attribute' => 'sections.section_id',
                'label' => __('hr.organisation.position.section'),
                'icon' => '',
                'placeholder' => '',
                'options' => Section::select(['id', 'name'])->orderBy('name')->get(),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'departments.department_id',
                'label' => __('hr.organisation.position.department'),
                'icon' => '',
                'placeholder' => '',
                'options' => Department::select(['id', 'name'])->orderBy('name')->get(),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'parents_count',
                'label' => __('hr.organisation.position.parents_count'),
                'icon' => '',
                'placeholder' => '',
                'customAction' => 'custom',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'lessThan' => "<",
                    'greaterThan' => ">",
                ],
            ],
            [
                'attribute' => 'valid_assigments_count',
                'label' => __('hr.organisation.position.valid_assigments_count'),
                'icon' => '',
                'placeholder' => '',
                'customAction' => 'custom',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'lessThan' => "<",
                    'greaterThan' => ">",
                ],
            ],
            [
                'attribute' => 'is_active',
                'label' => __('hr.organisation.currency.is_active'),
                'icon' => '',
                'placeholder' => '',
                'options' => collect([['id' => 1, 'name' => __('main.yes')], ['id' => 0, 'name' => __('main.no')]]),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                ]
            ],
        ];

        return view('hr.organisation.position.index', compact('positions', 'colOptions', 'filterInputs', 'filterData'));
    }

    public function show(Position $position)
    {
        return redirect()->route('hr.organisation.positions.edit', ['position' => $position]);
    }

    public function edit(Position $position, ?string $tab = null):View
    {
        $data = [];

        switch ($tab) {
            case 'hierarchy':
                $data['allPositions'] = Position::orderBy('name')->get()->forFormSelect(optionColumn: 'name_hr', optionDetailColumn: 'code');

                $data['parents'] = $position->parents()->orderBy('pivot_hierarchy_order')->paginate(20, pageName: 'parents');
                $data['children'] = $position->children()->orderBy('pivot_hierarchy_order')->paginate(20, pageName: 'children');

                $view = 'hierarchy';
                break;
            default:
                $data['positionSectionsIds'] = $position->sections()->pluck('id');
                $data['positionDepartmentsIds'] = $position->departments()->pluck('id');
                $data['positionSections'] = $position->sections()->paginate(10, pageName: 'sections');
                $data['positionDepartments'] = $position->departments()->paginate(10, pageName: 'departments');

                $data['sectionsColOptions'] = [
                    __('hr.organisation.position.edit.sections.name') => ['type' => 'value', 'path_in_collection' => 'name'],
                    __('hr.organisation.position.edit.sections.delete') => ['type' => 'actions', 'base_path' => route('hr.organisation.positions.show', $position).'/detach-section', 'available_actions' => [ 'show' => false,'edit' => false, 'delete' => true ],],
                ];

                $data['departmentsColOptions'] = [
                    __('hr.organisation.position.edit.departments.name') => ['type' => 'value', 'path_in_collection' => 'name'],
                    __('hr.organisation.position.edit.departments.delete') => ['type' => 'actions', 'base_path' => route('hr.organisation.positions.show', $position).'/detach-department', 'available_actions' => [ 'show' => false,'edit' => false, 'delete' => true ],],
                ];

                $data['allSections'] = Section::all()->forFormSelect();
                $data['allDepartments'] = Department::all()->forFormSelect();

                $tab = 'main';
                $view = 'main';
                break;
        }

        return view('hr.organisation.position.edit.'.$view, compact('position', 'tab') + $data);
    }

    public function update(Position $position, UpdatePositionRequest $request): RedirectResponse
    {
        $position->update($request->validated());

        flash(__('hr.organisation.position.edit.main.saved'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id]);
    }

    public function attachSection(Position $position, AttachSectionRequest $request): RedirectResponse
    {
        $position->sections()->attach($request->input('section'), ['created_at' => now(), 'updated_at' => now()]);

        flash()->success(__('hr.organisation.position.edit.sections.attached'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id]);
    }

    public function detachSection(Position $position, Section $section, DetachSectionRequest $request): RedirectResponse
    {
        $position->sections()->detach($section->id);

        flash()->success(__('hr.organisation.position.edit.sections.deatached'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id]);
    }

    public function attachDepartment(Position $position, AttachDepartmentRequest $request): RedirectResponse
    {
        $position->departments()->attach($request->input('department'), ['created_at' => now(), 'updated_at' => now()]);

        flash()->success(__('hr.organisation.position.edit.departments.attached'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id]);
    }

    public function detachDepartment(Position $position, Department $department, DetachDepartmentRequest $request): RedirectResponse
    {
        $position->departments()->detach($department->id);

        flash()->success(__('hr.organisation.position.edit.departments.deatached'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id]);
    }

    public function attachParentPosition(Position $position, AttachParentPositionRequest $request): RedirectResponse
    {
        if($request->input('shift_hierarchy_order')) {
            PositionHierarchy::where('position_id', $position->id)
                ->where('hierarchy_order', '>=', $request->validated('hierarchy_order'))
                ->increment('hierarchy_order');
        }

        $position->parents()->syncWithoutDetaching([
            $request->input('parent_id') =>
            $request->only('hierarchy_order', 'is_pay_approver', 'is_centre_pay_approver', 'is_pay_viewer') + ['created_at' => now(), 'updated_at' => now()]
        ]);

        flash()->success(__('hr.organisation.position.edit.hierarchy.add.attached'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id, 'tab' => 'hierarchy']);
    }

    public function detachParentPosition(Position $position, Position $parent, DetachParentPositionRequest $request): RedirectResponse
    {
        $position->parents()->detach($parent->id);

        $hierarchyOrders = PositionHierarchy::where('position_id', $position->id)
            ->selectRaw('hierarchy_order, count(*) as count')
            ->groupBy('hierarchy_order')
            ->orderBy('hierarchy_order')
            ->get()
            ->keyBy('hierarchy_order');

        $maxOrder = $hierarchyOrders->max('hierarchy_order');

        for($currentOrder = 1; $currentOrder <= $maxOrder; $currentOrder++) {
            if($hierarchyOrders->has($currentOrder)) {
                $hierarchyOrders->forget($currentOrder);
                continue;
            }

            $minOrder = $hierarchyOrders->min('hierarchy_order');

            if(is_null($minOrder)) {
                break;
            }

            PositionHierarchy::where('position_id', $position->id)
                ->where('hierarchy_order', $minOrder)
                ->update(['hierarchy_order' => $currentOrder]);

            //priradzujem
            $hierarchyOrders->forget($minOrder);
        }

        flash()->success(__('hr.organisation.position.edit.hierarchy.parents.deatached'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id, 'tab' => 'hierarchy']);
    }

    public function copyParentPositions(Position $position, CopyParentPositionRequest $request): RedirectResponse
    {
        $positionFrom = Position::find($request->validated('position_id'));
        $parentPositionsToCopy = $positionFrom->parents()->get();

        foreach($parentPositionsToCopy as $parentPositionToCopy){
            $position->parents()->attach(
                $parentPositionToCopy->pivot->parent_id,
                $parentPositionToCopy->pivot->only([
                    'hierarchy_order', 'is_pay_approver', 'is_centre_pay_approver', 'is_pay_viewer'
                ]) + ['created_at' => now(), 'updated_at' => now()]
            );
        }

        flash()->success(__('hr.organisation.position.edit.hierarchy.add.copied'));
        return to_route('hr.organisation.positions.edit', ['position' => $position->id, 'tab' => 'hierarchy']);
    }
}
