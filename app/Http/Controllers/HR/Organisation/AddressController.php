<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Models\Base\Address;

class AddressController extends Controller
{
    public function index()
    {
        $addresses = Address::with('country:id,code', 'city:id,name')
            ->paginate(20);

        $colOptions = [
            __('hr.organisation.address.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.address.country') => ['type' => 'value', 'path_in_collection' => 'country.code'],
            __('hr.organisation.address.city') => ['type' => 'value', 'path_in_collection' => 'city.name'],
            __('hr.organisation.address.city_section') => ['type' => 'value', 'path_in_collection' => 'city_section'],
            __('hr.organisation.address.shopping_center') => ['type' => 'value', 'path_in_collection' => 'shopping_center'],
            __('hr.organisation.address.street') => ['type' => 'value', 'path_in_collection' => 'street'],
            __('hr.organisation.address.number') => ['type' => 'value', 'path_in_collection' => 'number'],
            __('hr.organisation.address.zip') => ['type' => 'value', 'path_in_collection' => 'zip'],
        ];

        return view('hr.organisation.address.index', compact('addresses', 'colOptions'));
    }
}
