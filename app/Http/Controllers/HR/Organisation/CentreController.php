<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Http\Requests\HR\Organisation\Centre\AttachDepartmentRequest;
use App\Http\Requests\HR\Organisation\Centre\AttachSectionRequest;
use App\Http\Requests\HR\Organisation\Centre\DetachDepartmentRequest;
use App\Http\Requests\HR\Organisation\Centre\DetachSectionRequest;
use App\Http\Requests\HR\Organisation\Centre\UpdateCentreRequest;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Department;
use App\Models\Organisation\Section;
use App\Services\FilterService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class CentreController extends Controller
{
    public function index()
    {
        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter')
        );

        $centres = Centre::query()
            ->filter($filterData['filter'])
            ->with('parent', 'address', 'address.city', 'company', 'departments')
            ->orderBy('code')
            ->paginate(20);

        $colOptions = [
            __('hr.organisation.centre.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.centre.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.centre.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.centre.parent') => ['type' => 'value', 'path_in_collection' => 'parent.code'],
            __('hr.organisation.centre.city') => ['type' => 'value', 'path_in_collection' => 'address.city.name'],
            __('hr.organisation.centre.company') => ['type' => 'value', 'path_in_collection' => 'company.code'],
//            __('hr.organisation.centre.email') => ['type' => 'value', 'path_in_collection' => 'email'],
//            __('hr.organisation.centre.phone') => ['type' => 'value', 'path_in_collection' => 'phone'],
            __('hr.organisation.centre.departments') => ['type' => 'many_values', 'path_in_collection' => 'departments', 'get_attribute' => 'code'],
            __('hr.organisation.centre.is_active') => ['type' => 'yesno', 'path_in_collection' => 'is_active'],
            __('hr.organisation.centre.action') => ['type' => 'actions', 'base_path' => route('hr.organisation.centres.index'), 'available_actions' => [ 'show' => false,'edit' => true,]],
        ];

        $filterInputs = [
            [
                'attribute' => 'code',
                'label' => __('hr.organisation.centre.code'),
                'icon' => '',
                'placeholder' => '',
            ],
            [
                'attribute' => 'name',
                'label' => __('hr.organisation.centre.name'),
                'icon' => '',
                'placeholder' => '',
                'allowedActions' => [
                    'like' => "~",
                    'in' => "[.]",
                    'notEqual' => "!=",
                ],
            ],
            [
                'attribute' => 'is_active',
                'label' => __('hr.organisation.centre.is_active'),
                'icon' => '',
                'placeholder' => '',
                'options' => collect([['id' => 1, 'name' => __('main.yes')], ['id' => 0, 'name' => __('main.no')]]),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                ]
            ],
        ];

        return view('hr.organisation.centre.index', compact('centres', 'colOptions', 'filterData', 'filterInputs'));
    }

    public function show(Centre $centre)
    {
        return redirect()->route('hr.organisation.centres.edit', ['centre' => $centre]);
    }

    public function edit(Centre $centre):View
    {
        $data = [];

        $data['centreSectionsIds'] = $centre->sections()->pluck('id');
        $data['centreDepartmentsIds'] = $centre->departments()->pluck('id');
        $data['centreSections'] = $centre->sections()->paginate(10, pageName: 'sections');
        $data['centreDepartments'] = $centre->departments()->paginate(10, pageName: 'departments');

        $data['sectionsColOptions'] = [
            __('hr.organisation.centre.edit.sections.name') => ['type' => 'value', 'path_in_collection' => 'name'],
            __('hr.organisation.centre.edit.sections.delete') => ['type' => 'actions', 'base_path' => route('hr.organisation.centres.show', $centre).'/detach-section', 'available_actions' => [ 'show' => false,'edit' => false, 'delete' => true ],],
        ];

        $data['departmentsColOptions'] = [
            __('hr.organisation.centre.edit.departments.name') => ['type' => 'value', 'path_in_collection' => 'name'],
            __('hr.organisation.centre.edit.departments.delete') => ['type' => 'actions', 'base_path' => route('hr.organisation.centres.show', $centre).'/detach-department', 'available_actions' => [ 'show' => false,'edit' => false, 'delete' => true ],],
        ];

        $data['allSections'] = Section::all()->forFormSelect();
        $data['allDepartments'] = Department::all()->forFormSelect();

        return view('hr.organisation.centre.edit', compact('centre') + $data);
    }

    public function update(Centre $centre, UpdateCentreRequest $request): RedirectResponse
    {
        $centre->update($request->validated());

        flash()->success(__('hr.organisation.centre.edit.saved'));
        return back();
    }

    public function attachSection(Centre $centre, AttachSectionRequest $request): RedirectResponse
    {
        $centre->sections()->attach($request->input('section'), ['created_at' => now(), 'updated_at' => now()]);

        flash()->success(__('hr.organisation.centre.edit.sections.attached'));
        return to_route('hr.organisation.centres.edit', ['centre' => $centre->id]);
    }

    public function detachSection(Centre $centre, Section $section, DetachSectionRequest $request): RedirectResponse
    {
        $centre->sections()->detach($section->id);

        flash()->success(__('hr.organisation.centre.edit.sections.deatached'));
        return to_route('hr.organisation.centres.edit', ['centre' => $centre->id]);
    }

    public function attachDepartment(Centre $centre, AttachDepartmentRequest $request): RedirectResponse
    {
        $centre->departments()->attach($request->input('department'), ['created_at' => now(), 'updated_at' => now()]);

        flash()->success(__('hr.organisation.centre.edit.departments.attached'));
        return to_route('hr.organisation.centres.edit', ['centre' => $centre->id]);
    }

    public function detachDepartment(Centre $centre, Department $department, DetachDepartmentRequest $request): RedirectResponse
    {
        $centre->departments()->detach($department->id);

        flash()->success(__('hr.organisation.centre.edit.departments.deatached'));
        return to_route('hr.organisation.centres.edit', ['centre' => $centre->id]);
    }
}
