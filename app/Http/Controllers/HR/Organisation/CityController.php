<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Models\Base\City;

class CityController extends Controller
{
    public function index()
    {
        $cities = City::with('country')->orderBy('country_id')->orderBy('name')->paginate(20);

        $colOptions = [
            __('hr.organisation.city.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.city.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.city.country') => ['type' => 'value', 'path_in_collection' => 'country.code'],
            __('hr.organisation.city.name_cs') => ['type' => 'value', 'path_in_collection' => 'name_cs'],
            __('hr.organisation.city.name_sk') => ['type' => 'value', 'path_in_collection' => 'name_sk'],
            __('hr.organisation.city.name_hu') => ['type' => 'value', 'path_in_collection' => 'name_hu'],
        ];

        return view('hr.organisation.city.index', compact('cities', 'colOptions'));
    }
}
