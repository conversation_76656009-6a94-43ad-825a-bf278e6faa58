<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Models\Base\Country;

class CountryController extends Controller
{
    public function index()
    {
        $countries = Country::paginate(20);

        $colOptions = [
            __('hr.organisation.country.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.country.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.country.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.country.name_cs') => ['type' => 'value', 'path_in_collection' => 'name_cs'],
            __('hr.organisation.country.name_sk') => ['type' => 'value', 'path_in_collection' => 'name_sk'],
            __('hr.organisation.country.name_hu') => ['type' => 'value', 'path_in_collection' => 'name_hu'],
        ];

        return view('hr.organisation.country.index', compact('countries', 'colOptions'));
    }
}
