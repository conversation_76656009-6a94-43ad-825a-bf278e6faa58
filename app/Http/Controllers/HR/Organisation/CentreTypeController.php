<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Models\Organisation\CentreType;

class CentreTypeController extends Controller
{
    public function index()
    {
        $centreTypes = CentreType::paginate(20);

        $colOptions = [
            __('hr.organisation.centre_type.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.centre_type.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.centre_type.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.centre_type.name_cs') => ['type' => 'value', 'path_in_collection' => 'name_cs'],
            __('hr.organisation.centre_type.name_sk') => ['type' => 'value', 'path_in_collection' => 'name_sk'],
            __('hr.organisation.centre_type.name_hu') => ['type' => 'value', 'path_in_collection' => 'name_hu'],
        ];

        return view('hr.organisation.centre_type.index', compact('centreTypes', 'colOptions'));
    }
}
