<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Http\Requests\HR\Organisation\Company\UpdateCompanyRequest;
use App\Models\Organisation\Company;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class CompanyController extends Controller
{
    public function index()
    {
        $companies = Company::with('address', 'address.city')->paginate(20);

        $colOptions = [
            __('hr.organisation.company.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.company.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.company.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.company.city') => ['type' => 'value', 'path_in_collection' => 'address.city.name'],
            __('hr.organisation.company.street') => ['type' => 'value', 'path_in_collection' => 'address.street'],
            __('hr.organisation.company.number') => ['type' => 'value', 'path_in_collection' => 'address.number'],
            __('hr.organisation.company.is_active') => ['type' => 'yesno', 'path_in_collection' => 'is_active'],
            __('hr.organisation.company.action') => ['type' => 'actions', 'base_path' => route('hr.organisation.companies.index'), 'available_actions' => [ 'show' => false,'edit' => true,]],
        ];

        return view('hr.organisation.company.index', compact('companies', 'colOptions'));
    }

    public function show(Company $company): RedirectResponse
    {
        return redirect()->route('hr.organisation.companies.edit', ['company' => $company]);
    }

    public function edit(Company $company): View
    {
        $data = [];

        return view('hr.organisation.company.edit', compact('company') + $data);
    }

    public function update(Company $company, UpdateCompanyRequest $request): RedirectResponse
    {
        $company->update($request->validated());

        flash()->success(__('hr.organisation.company.edit.saved'));
        return back();
    }
}
