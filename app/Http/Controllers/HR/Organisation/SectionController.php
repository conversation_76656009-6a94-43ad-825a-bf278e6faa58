<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Models\Organisation\Section;
use Illuminate\Http\Request;

class SectionController extends Controller
{
    public function index()
    {
        $sections = Section::paginate(20);

        $colOptions = [
            __('hr.organisation.section.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.section.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.section.name_cs') => ['type' => 'value', 'path_in_collection' => 'name_cs'],
            __('hr.organisation.section.name_sk') => ['type' => 'value', 'path_in_collection' => 'name_sk'],
            __('hr.organisation.section.name_hu') => ['type' => 'value', 'path_in_collection' => 'name_hu'],
//            __('hr.organisation.section.action') => ['type' => 'actions', 'base_path' => route('hr.organisation.sections.index'), 'available_actions' =>  ['show' => true, 'edit' => true,]],
        ];

        return view('hr.organisation.section.index', compact('sections', 'colOptions'));
    }

}
