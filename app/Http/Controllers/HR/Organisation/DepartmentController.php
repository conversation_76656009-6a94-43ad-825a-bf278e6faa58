<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Http\Requests\HR\Organisation\Department\AttachSectionRequest;
use App\Http\Requests\HR\Organisation\Department\DetachSectionRequest;
use App\Http\Requests\HR\Organisation\Department\UpdateDepartmentRequest;
use App\Models\Organisation\Department;
use App\Models\Organisation\Section;
use App\Services\FilterService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DepartmentController extends Controller
{
    public function index()
    {
        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter')
        );

        $departments = Department::query()
            ->filter($filterData['filter'])
            ->with('parent', 'sections')
            ->paginate(20);

        $colOptions = [
            __('hr.organisation.department.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.department.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.department.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.department.parent') => ['type' => 'value', 'path_in_collection' => 'parent.code'],
            __('hr.organisation.department.sections') => ['type' => 'many_values', 'path_in_collection' => 'sections', 'get_attribute' => 'name'],
            __('hr.organisation.department.is_active') => ['type' => 'yesno', 'path_in_collection' => 'is_active'],
            __('hr.organisation.department.action') => ['type' => 'actions', 'base_path' => route('hr.organisation.departments.index'), 'available_actions' => [ 'show' => false,'edit' => true,]],
        ];

        $filterInputs = [
            [
                'attribute' => 'code',
                'label' => __('hr.organisation.department.code'),
                'icon' => '',
                'placeholder' => '',
            ],
            [
                'attribute' => 'name',
                'label' => __('hr.organisation.department.name'),
                'icon' => '',
                'placeholder' => '',
                'allowedActions' => [
                    'like' => "~",
                    'in' => "[.]",
                    'notEqual' => "!=",
                ],
            ],
            [
                'attribute' => 'sections.id',
                'label' => __('hr.organisation.department.sections'),
                'icon' => '',
                'placeholder' => '',
                'options' => Section::get(),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                ]
            ],
            [
                'attribute' => 'is_active',
                'label' => __('hr.organisation.centre.is_active'),
                'icon' => '',
                'placeholder' => '',
                'options' => collect([['id' => 1, 'name' => __('main.yes')], ['id' => 0, 'name' => __('main.no')]]),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                ]
            ],
        ];

        return view('hr.organisation.department.index', compact('departments', 'colOptions', 'filterData', 'filterInputs'));
    }

    public function show(Department $department)
    {
        return redirect()->route('hr.organisation.departments.edit', ['department' => $department]);
    }

    public function edit(Department $department):View
    {
        $data = [];

        $data['departmentSectionsIds'] = $department->sections()->pluck('id');
        $data['departmentSections'] = $department->sections()->paginate(10, pageName: 'sections');

        $data['sectionsColOptions'] = [
            __('hr.organisation.department.edit.sections.name') => ['type' => 'value', 'path_in_collection' => 'name'],
            __('hr.organisation.department.edit.sections.delete') => ['type' => 'actions', 'base_path' => route('hr.organisation.departments.show', $department).'/detach-section', 'available_actions' => [ 'show' => false,'edit' => false, 'delete' => true ],],
        ];

        $data['allSections'] = Section::all()->forFormSelect();

        return view('hr.organisation.department.edit', compact('department') + $data);
    }

    public function update(Department $department, UpdateDepartmentRequest $request): RedirectResponse
    {
        $department->update($request->validated());

        flash()->success(__('hr.organisation.department.edit.saved'));
        return back();
    }

    public function attachSection(Department $department, AttachSectionRequest $request): RedirectResponse
    {
        $department->sections()->attach($request->input('section'), ['created_at' => now(), 'updated_at' => now()]);

        flash()->success(__('hr.organisation.department.edit.sections.attached'));
        return to_route('hr.organisation.departments.edit', ['department' => $department->id]);
    }

    public function detachSection(Department $department, Section $section, DetachSectionRequest $request): RedirectResponse
    {
        $department->sections()->detach($section->id);

        flash()->success(__('hr.organisation.department.edit.sections.deatached'));
        return to_route('hr.organisation.departments.edit', ['department' => $department->id]);
    }
}
