<?php

namespace App\Http\Controllers\HR\Organisation;

use App\Http\Controllers\Controller;
use App\Http\Requests\HR\Organisation\Currency\UpdateCurrencyRequest;
use App\Models\Base\Currency;
use App\Services\FilterService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class CurrencyController extends Controller
{
    public function index()
    {
        $filterData = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter')
        );

        $currencies = Currency::query()
            ->filter($filterData['filter'])
            ->paginate(20);

        $colOptions = [
            __('hr.organisation.currency.id') => ['type' => 'value', 'path_in_collection' => 'id'],
            __('hr.organisation.currency.code') => ['type' => 'value', 'path_in_collection' => 'code'],
            __('hr.organisation.currency.name') => ['type' => 'value', 'path_in_collection' => 'name', 'custom_classes'=> 'font-medium' ],
            __('hr.organisation.currency.symbol') => ['type' => 'value', 'path_in_collection' => 'symbol'],
            __('hr.organisation.currency.is_active') => ['type' => 'yesno', 'path_in_collection' => 'is_active'],
            __('hr.organisation.currency.action') => ['type' => 'actions', 'base_path' => route('hr.organisation.currencies.index'), 'available_actions' => [ 'show' => false,'edit' => true,]],
        ];

        $filterInputs = [
            [
                'attribute' => 'code',
                'label' => __('hr.organisation.position.code'),
                'icon' => '',
                'placeholder' => '',
            ],
            [
                'attribute' => 'name',
                'label' => __('hr.organisation.position.name'),
                'icon' => '',
                'placeholder' => '',
                'allowedActions' => [
                    'like' => "~",
                    'in' => "[.]",
                    'notEqual' => "!=",
                ],
            ],
            [
                'attribute' => 'is_active',
                'label' => __('hr.organisation.currency.is_active'),
                'icon' => '',
                'placeholder' => '',
                'options' => collect([['id' => 1, 'name' => __('main.yes')], ['id' => 0, 'name' => __('main.no')]]),
                'searchProperty'=>'name',
                'inputProperty'=>'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                ]
            ],
        ];

        return view('hr.organisation.currency.index', compact('currencies', 'colOptions', 'filterData', 'filterInputs'));
    }

    public function show(Currency $currency): RedirectResponse
    {
        return redirect()->route('hr.organisation.currencies.edit', ['currency' => $currency]);
    }

    public function edit(Currency $currency): View
    {
        $data = [];

        return view('hr.organisation.currency.edit', compact('currency') + $data);
    }

    public function update(Currency $currency, UpdateCurrencyRequest $request): RedirectResponse
    {
        $currency->update($request->validated());

        flash()->success(__('hr.organisation.currency.edit.saved'));
        return back();
    }
}
