<?php

namespace App\Http\Controllers\HR;

use App\Enums\HR\User\ShowUserTabEnum;
use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Http\Controllers\Controller;
use App\Models\Organisation\Centre;
use App\Models\Organisation\CentreDepartment;
use App\Models\Organisation\Department;
use App\Models\Organisation\DepartmentPosition;
use App\Models\Organisation\Position;
use App\Models\Person\Assigment;
use App\Models\Person\User;
use App\Services\FilterService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class UserController extends Controller
{
    public function index(string $type = 'interested')
    {
        $viewData['currentAssigmentType'] = AssigmentTypeEnum::getFromUrlParameter($type);

        $viewData['typesCounts'] = Assigment::selectRaw('count(DISTINCT user_id) as count, type')
            ->fromAllowedCountries()
            ->groupByRaw('type')
            ->get();

        $viewData['tableHeaders'] = [
            __('hr.filters.login'),
            __('hr.filters.full_name'),
            __('hr.filters.assigments'),
            __('hr.filters.actions'),
        ];

        $viewData['filterInputs'] = self::indexFilterInputs();

        $viewData['filterData'] = (new FilterService())->processAllFilterDataForUniversalFilterComponent(
            request()->input('filter')
        );

        $fullNameFilter = $viewData['filterData']['filter']['custom']['like']['full_name'] ?? null;
        $assigmentDepartmentFilter = $viewData['filterData']['filter']['custom']['equal']['assigments_department'] ?? null;

        $viewData['users'] = User::withWhereHas('assigments', function ($query) use ($viewData) {
                $query->fromAllowedCountries()
                    ->where('type', $viewData['currentAssigmentType'])
                    ->with('position:id,code,name,name_hr', 'centre:id,code,name');
            })
            ->when(!is_null($fullNameFilter), function ($query) use ($fullNameFilter){
                $query->where(DB::raw('CONCAT(last_name, " ", first_name)'), 'like', '%'.$fullNameFilter.'%')
                        ->orWhere('login', 'like', $fullNameFilter);
            })
            ->when(!is_null($assigmentDepartmentFilter), function ($query) use ($assigmentDepartmentFilter, $viewData){
                $pd = DepartmentPosition::where('department_id', $assigmentDepartmentFilter)->pluck('position_id');
                $cd = CentreDepartment::where('department_id', $assigmentDepartmentFilter)->pluck('centre_id');
                $query->whereHas('assigments', function($query) use($pd, $cd , $viewData){
                    $query->whereIn('position_id', $pd)
                        ->whereIn('centre_id', $cd)
                        ->where('type', $viewData['currentAssigmentType']);
                });
            })
            ->filter($viewData['filterData']['filter'])
            ->orderBy('id', 'desc')
            ->paginate(15);

        return view('hr.users.index.index', $viewData);
    }

    public function show(string $type = 'interested', User $user = null): View|RedirectResponse
    {
        if(is_null($user)){
            return to_route('hr.users.index');
        }

        $viewData['currentAssigmentType'] = AssigmentTypeEnum::getFromUrlParameter($type);
        $viewData['currentShowTab'] = ShowUserTabEnum::ASSIGMENTS_CONTRACTS;
        $viewData['user'] = $user;

        $viewData['assigments'] = $user->assigments()->fromAllowedCountries()->with('position:id,name,name_hr', 'centre:id,code,name')->paginate(15, pageName: 'assigments_page');
        $viewData['contracts'] = $user->contracts()->fromAllowedCountries()->with('company:id,code')->paginate(15, pageName: 'contracts_page');

        $viewData['assigmentsHeaders'] = [
            __('hr.users.assigments.assigment'),
            __('hr.users.assigments.type'),
            __('hr.users.assigments.status'),
            __('hr.users.assigments.validity'),
//            __('hr.users.assigments.is_from_hrms1'),
            __('hr.filters.actions'),
        ];

        $viewData['contractsHeaders'] = [
            __('hr.users.contracts.type'),
            __('hr.users.contracts.status'),
            __('hr.users.contracts.is_primary'),
            __('hr.users.contracts.percentage'),
            __('hr.users.contracts.validity'),
            __('hr.filters.actions'),
        ];

        return view($viewData['currentAssigmentType']->hrUsersShowView(), $viewData);
    }

    private static function indexFilterInputs(): array
    {
        return [
            [
                'attribute' => 'full_name',
                'label' => __('hr.filters.full_name'),
                'icon' => '',
                'placeholder' => '',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
            [
                'attribute' => 'assigments.centre_id',
                'label' => __('hr.filters.centre'),
                'icon' => '',
                'placeholder' => '',
                'options' => Centre::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'assigments.position_id',
                'label' => __('hr.filters.position'),
                'icon' => '',
                'placeholder' => '',
                'options' => Position::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'allowedActions' => [
                    'equal' => "=",
                    'notEqual' => "!=",
                    'in' => "[.]",
                    'notIn' => "![.]"
                ]
            ],
            [
                'attribute' => 'assigments_department',
                'label' => __('hr.filters.department'),
                'icon' => '',
                'placeholder' => '',
                'options' => Department::select('id', 'code', 'name', DB::raw('CONCAT(code, " (", name, ")") as code_name'))->orderBy('code_name')->get(),
                'searchProperty' => 'code_name',
                'inputProperty' => 'id',
                'customAction' => 'custom',
                'disableLogic' => true,
            ],
        ];
    }
}
