<?php

namespace App\Http\Resources\Api\V1\External\Retail;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{

    public function toArray(Request $request): array
    {
        return [
            'uid' => $this->login,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'work_email' => $this?->workData?->email,
            'assigments' => $this->validAssigments->map(function ($assigment) {
                return [
                    'position_id' => $assigment?->position?->id,
                    'position_code' => $assigment?->position?->code,
                    'position_name' => $assigment?->position?->name,
                    'centre_id' => $assigment?->centre?->id,
                    'centre_code' => $assigment?->centre?->code,
                    'centre_name' => $assigment?->centre?->name,
                ];
            })
        ];
    }
}
