<?php

namespace App\Http\Resources\Api\V1\External\Matrix;

use App\Enums\Remuneration\ProposalType;
use App\Services\Vermont\MatrixService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RetailContractsMonthDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        [$reward, $cut] = $this->getRewardAndCut();

        return [
            'contract_hrms_id' => $this->hrms_id,
            'salary' => MatrixService::encryptMoneyValue($this->getSalary()),
            'reward' => MatrixService::encryptMoneyValue($reward),
            'cut' => MatrixService::encryptMoneyValue($cut),
            'currency_code' => $this->company->address->country->currency->code,
            'currency_cdb_id' => $this->company->address->country->currency->cdb_id,
        ];
    }

    private function getSalary(): ?int
    {
        if(!$this->salaries || $this->salaries->count() !== 1) {
            $salary = null;
        } else {
            $firstSalary = $this->salaries->first();

            if($firstSalary->currency_id !== $this->company->address->country->currency_id) {
                return null;
            }

            $salary = $firstSalary->base + ($firstSalary->addition ?? 0);
        }

        return $salary;
    }

    private function getRewardAndCut(): array
    {
        if(!$this->remunerationProposals){
            return [null, null];
        }

        $rewards = $this->remunerationProposals->where('type', ProposalType::REWARD->value);
        $cuts = $this->remunerationProposals->where('type', ProposalType::CUT->value);

        return [$this->getRewardOrCut($rewards), $this->getRewardOrCut($cuts)];
    }

    private function getRewardOrCut($rewardsOrCuts): ?int
    {
        if($rewardsOrCuts->count() === 0){
            return null;
        }

        $currencies = $rewardsOrCuts->pluck('currency_id')->unique();
        if($currencies->count() > 1) {
            return null;
        }

        if($currencies->first() !== $this->company->address->country->currency_id) {
            return null;
        }

        return $rewardsOrCuts->sum('base');
    }
}
