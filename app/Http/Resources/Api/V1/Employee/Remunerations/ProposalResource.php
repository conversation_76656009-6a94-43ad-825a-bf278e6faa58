<?php

namespace App\Http\Resources\Api\V1\Employee\Remunerations;

use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalSubType;
use App\Enums\Remuneration\ProposalType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProposalResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $array = parent::toArray($request);

        $array['status_translation'] = ProposalStatus::tryFrom($array['status'])?->translation();
        $array['payment_status_translation'] = ProposalPaymentStatus::tryFrom($array['payment_status'])?->translation();
        $array['type_translation'] = ProposalType::tryFrom($array['type'])?->translation();
        $array['subtype_translation'] = ProposalSubType::tryFrom($array['subtype'])?->translation();

        return $array;
    }
}
