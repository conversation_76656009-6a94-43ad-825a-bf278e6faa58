<?php

namespace App\Http\Requests\HR\User\Contract\Salary;

use App\Models\Base\Currency;
use App\Models\Person\Contract;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreContractSalaryRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'validity' => [
                'required',
                'date',
                function ($attribute, $value, $fail) {
                    if(!Carbon::parse($value)->isStartOfMonth()) {
                        $fail(__('hr.users.contracts.edit.salary.create.validation.date_has_to_be_first'));
                    }
                }
            ],
            'base' => [
                'required',
                'numeric',
                'min:0',
            ],
            'addition' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'currency_id' => [
                'required',
                'exists:'.Currency::class.',id',
            ],
        ];
    }
}
