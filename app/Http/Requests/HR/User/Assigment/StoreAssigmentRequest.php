<?php

namespace App\Http\Requests\HR\User\Assigment;

use App\Enums\Person\Assigment\AssigmentStatusEnum;
use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Models\Base\Country;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Position;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAssigmentRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        if($this->centre_id) {
            $selectedCentre = Centre::with('address')->find($this->centre_id);

            $this->merge([
                'country_id' => $selectedCentre?->address?->country_id,
            ]);
        }
    }

    public function rules(): array
    {
        return [
            'position_id' => [
                'required',
                'exists:'.Position::class.',id',
            ],
            'centre_id' => [
                'nullable',
                'exists:'.Centre::class.',id',
            ],
            'country_id' => [
                'required',
                'exists:'.Country::class.',id',
            ],
            'type' => [
                'required',
                Rule::enum(AssigmentTypeEnum::class),
            ],
            'status' => [
                'required',
                Rule::enum(AssigmentStatusEnum::class),
            ],
            'valid_from_date' => [
                'nullable',
                'date',
                Rule::requiredIf(function () {
                    return in_array((int) $this->type, [AssigmentTypeEnum::FUTURE_EMPLOYEE->value, AssigmentTypeEnum::EMPLOYEE->value], false);
                }),
            ],
            'valid_to_date' => [
                'nullable',
                'date',
                Rule::requiredIf(function () {
                    return ((int) $this->type) === AssigmentTypeEnum::FINISHED->value;
                }),
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'position_id' => '"'.__('hr.users.assigments.create.position').'"',
            'centre_id' => '"'.__('hr.users.assigments.create.centre').'"',
            'country_id' => '"'.__('hr.users.assigments.create.country').'"',
            'type' => '"'.__('hr.users.assigments.create.type').'"',
            'status' => '"'.__('hr.users.assigments.create.status').'"',
            'valid_from_date' => '"'.__('hr.users.assigments.create.valid_from_date').'"',
            'valid_to_date' => '"'.__('hr.users.assigments.create.valid_to_date').'"',
        ];
    }

    public function messages(): array
    {
        $messages = parent::messages();
        $messages['valid_from_date']['required'] = __('hr.users.assigments.create.validation.valid_from_date.required_if');
        $messages['valid_to_date']['required'] = __('hr.users.assigments.create.validation.valid_to_date.required_if');

        return $messages;
    }
}
