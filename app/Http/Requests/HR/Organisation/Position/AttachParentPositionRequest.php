<?php

namespace App\Http\Requests\HR\Organisation\Position;

use App\Models\Organisation\Position;
use Illuminate\Foundation\Http\FormRequest;

class AttachParentPositionRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'parent_id' => [
                'required',
                'exists:'.Position::getTableName().',id',
            ],
            'is_pay_approver' => [
                'required',
                'boolean',
            ],
            'is_centre_pay_approver' => [
                'required',
                'boolean',
            ],
            'is_pay_viewer' => [
                'required',
                'boolean',
            ],
            'hierarchy_order' => [
                'required',
                'integer',
                'min:1',
            ],
            'shift_hierarchy_order' => [
                'required',
                'boolean',
            ],
        ];
    }
}
