<?php

namespace App\Http\Requests\HR\Organisation\Position;

use App\Models\Organisation\Position;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePositionRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'string',
                Rule::unique(Position::getTableName(), 'code')->ignore($this->position),
            ],
            'name' => [
                'required',
                'string',
            ],
            'name_hr' => [
                'required',
                'string',
            ],
            'is_active' => [
                'required',
                'boolean',
            ],
        ];
    }

    public function attributes()
    {
        return [
            'code' => __('hr.organisation.position.edit.main.code'),
            'name' => __('hr.organisation.position.edit.main.name'),
            'name_hr' => __('hr.organisation.position.edit.main.name_hr'),
            'is_active' => __('hr.organisation.position.edit.main.is_active'),
        ];
    }
}
