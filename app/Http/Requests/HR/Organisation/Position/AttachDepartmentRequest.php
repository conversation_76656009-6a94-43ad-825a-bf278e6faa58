<?php

namespace App\Http\Requests\HR\Organisation\Position;

use App\Models\Organisation\Department;
use Illuminate\Foundation\Http\FormRequest;

class AttachDepartmentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'department' => [
                'required',
                'exists:'.Department::getTableName().',id',
            ],
        ];
    }
}
