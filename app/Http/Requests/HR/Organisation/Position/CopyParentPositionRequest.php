<?php

namespace App\Http\Requests\HR\Organisation\Position;

use App\Models\Organisation\Position;
use Illuminate\Foundation\Http\FormRequest;

class CopyParentPositionRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'position_id' => [
                'required',
                'exists:'.Position::getTableName().',id',
            ],
        ];
    }
}
