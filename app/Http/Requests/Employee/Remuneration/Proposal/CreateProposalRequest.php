<?php

namespace App\Http\Requests\Employee\Remuneration\Proposal;

use App\Enums\Remuneration\ProposalType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateProposalRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => [
                'nullable',
                Rule::enum(ProposalType::class),
            ],
        ];
    }
}
