<?php

namespace App\Http\Requests\Employee\Remuneration\Proposal;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TableIndexProposalRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'section' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'department' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'centre' => [
                'nullable',
                'integer',
                'min:1',
            ],
            'validity' => [
                'nullable',
                'date',
                function (string $attribute, mixed $value, \Closure $fail) {
                    if (!is_null($value) && ((int) date('j', strtotime($value)) !== 1)) {
                        $fail(__('employee.remunerations.proposals.table.validation_date'));
                    }
                },
            ],
        ];
    }
}
