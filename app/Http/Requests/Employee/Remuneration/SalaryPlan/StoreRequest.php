<?php

namespace App\Http\Requests\Employee\Remuneration\SalaryPlan;

use App\Models\Base\Currency;
use App\Models\Person\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                Rule::exists(User::class, 'id'),
            ],
            'base' => [
                'required',
                'numeric',
                'min:1',
            ],
            'addition' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'currency_id' => [
                'required',
                Rule::exists(Currency::class, 'id'),
            ],
            'note' => [
                'nullable',
                'string',
            ],
            'validity' => [
                'required',
                'date',
                Rule::date()->afterOrEqual(now()->addMonths(2)->startOfMonth()),
            ],
        ];
    }

    public function attributes(): array
    {
        $attributes = parent::attributes();

        $attributes['user_id'] = __('employee.remunerations.salaries.plans.create.user_id');
        $attributes['base'] = __('employee.remunerations.salaries.plans.create.base');
        $attributes['addition'] = __('employee.remunerations.salaries.plans.create.addition');
        $attributes['currency_id'] = __('employee.remunerations.salaries.plans.create.currency_id');
        $attributes['note'] = __('employee.remunerations.salaries.plans.create.note');
        $attributes['validity'] = __('employee.remunerations.salaries.plans.create.validity');

        return $attributes;
    }
}
