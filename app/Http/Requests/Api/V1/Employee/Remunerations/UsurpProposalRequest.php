<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use App\Models\RaP\Permission;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Foundation\Http\FormRequest;

class UsurpProposalRequest extends FormRequest
{
    use PermissionTrait;
    public function authorize(): bool
    {
        if(!$this->proposal) {
            return false;
        }

        $loggedUser = auth()->user();
        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position',
            'validAssigments.position.children'
        );

        $userParentAssigments = $this->getValidAssigmentsWithChildren();

        $allowedApproversIds = $userParentAssigments->pluck('user_id')
                ->unique()
                ->filter(fn($userId) => $userId !== auth()->id());

        return $allowedApproversIds->contains($this->proposal->approver_id);
    }

    public function rules(): array
    {
        return [
            'note' => ['nullable', 'string'],
        ];
    }
}
