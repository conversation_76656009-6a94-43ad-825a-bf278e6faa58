<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use Illuminate\Foundation\Http\FormRequest;

class RollbackProposalRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->proposal && $this->proposal->approver_id === $this->user()->id && $this->proposal->proposer_id !== $this->user()->id;
    }

    public function rules(): array
    {
        return [
            'note' => ['nullable', 'string'],
        ];
    }
}
