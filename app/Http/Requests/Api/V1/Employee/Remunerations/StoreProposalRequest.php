<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalSubType;
use App\Enums\Remuneration\ProposalType;
use App\Models\Base\Currency;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Department;
use App\Models\Person\User;
use App\Models\RaP\Permission;
use App\Models\Remuneration\Proposal;
use App\Services\Employee\Remuneration\Proposal\AttributeService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use App\Traits\Requests\Api\V1\Employee\Remunerations\StoreUpdateProposalSameMethodsTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreProposalRequest extends FormRequest
{
    use StoreUpdateProposalSameMethodsTrait;

    public function rules(): array
    {
        $attributeService = new AttributeService();
        $validityMinDate = $attributeService->validityMinDate($this->type);

        $centre = Centre::with('departments')->find($this->centre_id);

        return [
            'users' => [
                'required',
                'array'
            ],
            'type' => [
                'required',
                'int',
                'min:1',
                Rule::enum(ProposalType::class)
            ],
            'subtype' => [
                Rule::requiredIf(in_array($this->type, [ProposalType::REWARD->value, ProposalType::NON_FINANCIAL_REWARD->value], false)),
                Rule::prohibitedIf(!in_array($this->type, [ProposalType::REWARD->value, ProposalType::NON_FINANCIAL_REWARD->value], false)),
                'nullable',
                'int',
                'min:1',
                Rule::enum(ProposalSubType::class),
                function (string $attribute, mixed $value, \Closure $fail) {
                    if((int) $this->type !== ProposalType::REWARD->value) {
                        return;
                    }

                    if(!in_array((int) $this->subtype, [ProposalSubType::F_ACTIVATION->value, ProposalSubType::F_FULFILLMENT_OF_MONTH->value], false)) {
                        return;
                    }

                    $activeProposalsCount = Proposal::whereIn('user_id', $this->users)
                        ->whereIn('subtype', [ProposalSubType::F_ACTIVATION->value, ProposalSubType::F_FULFILLMENT_OF_MONTH->value])
                        ->where('validity', $this->validity)
                        ->where('status', '!=', ProposalStatus::REJECTED->value)
                        ->count();

                    if($activeProposalsCount) {
                        $fail(__('employee.remunerations.proposals.create.validation.existing_subtype_for_some_user'));
                    }
                }
            ],
            'base' => [
                'required',
                'numeric',
                'min:1',
            ],
            'addition' => [
                'nullable',
                'numeric',
                'min:0',
                Rule::prohibitedIf((int) $this->type !== ProposalType::SALARY->value),
            ],
            'currency_id' => [
                'required',
                Rule::exists(Currency::class, 'id'),
            ],
            'reason' => [
                'nullable',
                'string',
            ],
            'validity' => [
                'required',
                'date',
                Rule::date()->afterOrEqual($validityMinDate),
            ],
            'centre_id' => [
                'required',
                'integer',
                Rule::exists(Centre::class, 'id'),
            ],
            'department_id' => [
                'required',
                'integer',
                Rule::exists(Department::class, 'id'),
                function (string $attribute, mixed $value, \Closure $fail) use ($centre) {
                    $centreDepartmentsIds = $centre->departments->pluck('id') ?? collect();

                    if(!$centreDepartmentsIds->contains((int) $value)) {
                        $fail(__('employee.remunerations.proposals.create.validation.centre_department_combination_not_valid'));
                    }
                },
            ],
            'approver_id' => [
                'required',
                'integer',
                Rule::exists(User::class, 'id'),
                function (string $attribute, mixed $value, \Closure $fail) {
                    if(!$this->isApproverValidForSelectedCentreAndDepartment()) {
                        $fail(__('employee.remunerations.proposals.create.validation.approver_not_valid_for_centre_department'));
                    }
                }
            ],
        ];
    }

    public function messages(): array
    {
        $messages = parent::messages();

        $messages['users']['required'] = __('employee.remunerations.proposals.create.validation.users_required');

        return $messages;
    }

    public function attributes(): array
    {
        $attributes = parent::attributes();

        $attributes['users'] = __('employee.remunerations.proposals.create.validation.attributes.users');
        $attributes['type'] = __('employee.remunerations.proposals.create.validation.attributes.type');
        $attributes['subtype'] = __('employee.remunerations.proposals.create.validation.attributes.subtype');
        $attributes['base'] = __('employee.remunerations.proposals.create.validation.attributes.base');
        $attributes['addition'] = __('employee.remunerations.proposals.create.validation.attributes.addition');
        $attributes['currency_id'] = __('employee.remunerations.proposals.create.validation.attributes.currency_id');
        $attributes['reason'] = __('employee.remunerations.proposals.create.validation.attributes.reason');
        $attributes['validity'] = __('employee.remunerations.proposals.create.validation.attributes.validity');
        $attributes['approver_id'] = __('employee.remunerations.proposals.create.validation.attributes.approver_id');
        $attributes['centre_id'] = __('employee.remunerations.proposals.create.validation.attributes.centre_id');
        $attributes['department_id'] = __('employee.remunerations.proposals.create.validation.attributes.department_id');

        return $attributes;
    }
}
