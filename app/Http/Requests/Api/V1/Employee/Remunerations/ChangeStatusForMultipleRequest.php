<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use App\Enums\Remuneration\ProposalStatus;
use App\Models\Person\User;
use App\Models\RaP\Permission;
use App\Models\Remuneration\Proposal;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ChangeStatusForMultipleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'status' => [
                'required',
                Rule::enum(ProposalStatus::class),
                function (string $attribute, mixed $value, \Closure $fail) {
                    if((int)$value === ProposalStatus::APPROVED->value) {
                        if(!$this->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)){
                            $fail(__('employee.remunerations.proposals.status_multiple.validation_status_approve_not_allowed'));
                        }
                    }
                },
            ],
            'approver_id' => [
                'integer',
                'nullable',
                'required_if:status,' . ProposalStatus::FORWARDED->value,
                function (string $attribute, mixed $value, \Closure $fail) {
                    if((int) $this->status === ProposalStatus::FORWARDED->value) {
                        if(!User::where('id', $value)->exists()) {
                            $fail(__('employee.remunerations.proposals.status_multiple.validation_approver_not_exists'));
                        }
                    }
                },
            ],
            'note' => [
                'string',
                'nullable',
            ],
            'checked_proposals' => [
                'array',
                'required',
                function (string $attribute, mixed $value, \Closure $fail) {
                    $currentApproverIds = Proposal::whereIn('id', $value)
                        ->pluck('approver_id')
                        ->unique()
                        ->toArray();

                    if(count($currentApproverIds) !== 1) {
                        $fail(__('employee.remunerations.proposals.status_multiple.validation_incorrect_current_approver'));
                    }

                    if($currentApproverIds[0] !== auth()->id()) {
                        $fail(__('employee.remunerations.proposals.status_multiple.validation_incorrect_current_approver'));
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        $messages = parent::messages();

        $messages['checked_proposals']['required'] = __('employee.remunerations.proposals.status_multiple.validation_required');

        return $messages;
    }
}
