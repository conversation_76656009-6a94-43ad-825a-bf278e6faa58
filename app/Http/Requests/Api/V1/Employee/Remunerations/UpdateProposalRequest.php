<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use App\Enums\Remuneration\ProposalSubType;
use App\Enums\Remuneration\ProposalType;
use App\Models\Base\Currency;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Department;
use App\Models\Person\User;
use App\Models\RaP\Permission;
use App\Services\Employee\Remuneration\Proposal\AttributeService;
use App\Traits\Requests\Api\V1\Employee\Remunerations\StoreUpdateProposalSameMethodsTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProposalRequest extends FormRequest
{
    use StoreUpdateProposalSameMethodsTrait;

    public function authorize(): bool
    {
        if (!$this->proposal) {
            return false;
        }

        if ($this->input('update_type') && $this->input('update_type') === 'usurp') {
            $loggedUser = auth()->user();
            $loggedUser->loadMissing(
                'validAssigments',
                'validAssigments.position',
                'validAssigments.position.children'
            );

            $userParentAssigments = $this->getValidAssigmentsWithChildren();

            $allowedApproversIds = $userParentAssigments->pluck('user_id')
                ->unique()
                ->filter(fn($userId) => $userId !== auth()->id());

            return $allowedApproversIds->contains($this->proposal->approver_id);
        }
        return $this->proposal->approver_id === $this->user()->id
            || $this->proposal->logs()->orderByDesc('created_at')->first()?->user_id === $this->user()->id;
    }

//    protected function prepareForValidation(): void
//    {
//        if(empty($this->department_id)) {
//            $selectedCentre = Centre::with('departments')->find($this->centre_id);
//
//            $this->merge([
//                'department_id' => $selectedCentre->departments->first()->id ?? null,
//            ]);
//        }
//    }

    public function rules(): array
    {
        $attributeService = new AttributeService();
        $validityMinDate = $attributeService->validityMinDate($this->type);

        $centre = Centre::with('departments')->find($this->centre_id);

        return [
            'subtype' => [
                Rule::requiredIf(in_array($this->proposal->type, [ProposalType::REWARD, ProposalType::NON_FINANCIAL_REWARD], false)),
                Rule::prohibitedIf(!in_array($this->proposal->type, [ProposalType::REWARD, ProposalType::NON_FINANCIAL_REWARD], false)),
                'nullable',
                'int',
                'min:1',
                Rule::enum(ProposalSubType::class),
            ],
            'base' => [
                'required',
                'numeric',
                'min:1',
            ],
            'addition' => [
                'nullable',
                'numeric',
                'min:0',
                Rule::prohibitedIf($this->proposal->type !== ProposalType::SALARY),
            ],
            'currency_id' => [
                'required',
                Rule::exists(Currency::class, 'id'),
            ],
            'reason' => [
                'nullable',
                'string',
            ],
            'validity' => [
                'required',
                'date',
                Rule::when(
                    $this->validity !== $this->proposal->validity,
                    [
                        Rule::date()->afterOrEqual($validityMinDate),
                    ]
                ),
            ],
            'centre_id' => [
                'required',
                'integer',
                Rule::exists(Centre::class, 'id'),
            ],
            'department_id' => [
                'required',
                'integer',
                Rule::exists(Department::class, 'id'),
                function (string $attribute, mixed $value, \Closure $fail) use ($centre) {
                    $centreDepartmentsIds = $centre->departments->pluck('id');

                    if(!$centreDepartmentsIds->contains((int) $value)) {
                        $fail(__('employee.remunerations.proposals.create.validation.centre_department_combination_not_valid'));
                    }
                },
            ],
            'approver_id' => [
                Rule::requiredIf(function (): bool {
                    return is_null($this->input('update_type')) || $this->input('update_type') === 'approve';
                }),
                Rule::when(is_null($this->input('update_type')) || $this->input('update_type') === 'approve', 'integer', 'nullable'),
                Rule::exists(User::class, 'id'),
                function (string $attribute, mixed $value, \Closure $fail) {
                    if(!$this->isApproverValidForSelectedCentreAndDepartment()) {
                        $fail(__('employee.remunerations.proposals.create.validation.approver_not_valid_for_centre_department'));
                    }
                }
            ],
            'update_type' => ['nullable', 'string'],
            'note' => ['nullable', 'string'],
            'is_paid_via_agreement' => ['nullable', 'boolean'],
            'payment_status' => ['nullable', 'int'],
        ];
    }

    public function attributes(): array
    {
        $attributes = parent::attributes();

        $attributes['subtype'] = __('employee.remunerations.proposals.create.validation.attributes.subtype');
        $attributes['base'] = __('employee.remunerations.proposals.create.validation.attributes.base');
        $attributes['addition'] = __('employee.remunerations.proposals.create.validation.attributes.addition');
        $attributes['currency_id'] = __('employee.remunerations.proposals.create.validation.attributes.currency_id');
        $attributes['reason'] = __('employee.remunerations.proposals.create.validation.attributes.reason');
        $attributes['validity'] = __('employee.remunerations.proposals.create.validation.attributes.validity');
        $attributes['approver_id'] = __('employee.remunerations.proposals.create.validation.attributes.approver_id');
        $attributes['centre_id'] = __('employee.remunerations.proposals.create.validation.attributes.centre_id');
        $attributes['department_id'] = __('employee.remunerations.proposals.create.validation.attributes.department_id');

        return $attributes;
    }
}
