<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use App\Models\RaP\Permission;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Foundation\Http\FormRequest;

class WithdrawProposalRequest extends FormRequest
{
    use PermissionTrait;
    public function authorize(): bool
    {
        if(!$this->proposal) {
            return false;
        }

        $this->proposal->loadMissing('lastLog');

        return $this->proposal->lastLog->user_id === $this->user()->id;
    }

    public function rules(): array
    {
        return [
            'note' => ['nullable', 'string'],
        ];
    }
}
