<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use App\Models\RaP\Permission;
use App\Models\Remuneration\ProposalLog;
use Illuminate\Foundation\Http\FormRequest;

class ShowProposalRequest extends FormRequest
{
    public function authorize(): bool
    {
        $logUsers = $this->proposal->logs()->pluck('user_id');
        return $this->proposal &&
            (
                $this->proposal->approver_id === $this->user()->id ||
                $logUsers->contains($this->user()->id)
            );
    }

    public function rules(): array
    {
        return [

        ];
    }
}
