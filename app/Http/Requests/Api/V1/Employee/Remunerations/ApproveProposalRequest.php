<?php

namespace App\Http\Requests\Api\V1\Employee\Remunerations;

use App\Models\RaP\Permission;
use Illuminate\Foundation\Http\FormRequest;

class ApproveProposalRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->proposal &&
            $this->proposal->approver_id === $this->user()->id &&
            $this->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER);
    }

    public function rules(): array
    {
        return [
            'note' => ['nullable', 'string'],
        ];
    }
}
