<?php

namespace App\Factories;

use App\Interfaces\SecondStepVerificationCodeSenderInterface;
use App\Models\Person\User;
use App\Strategies\SecondStepVerificationCodeSender\AccountSmsSenderStrategy;
use App\Strategies\SecondStepVerificationCodeSender\EmailSenderStrategy;
use App\Strategies\SecondStepVerificationCodeSender\LocalFakeSenderStrategy;

class SecondStepVerificationCodeSenderFactory
{
    public function setCodeSender(?string $provider = null, ?User $user = null): SecondStepVerificationCodeSenderInterface
    {
        if(is_null($provider) && !app()->isProduction()) {
            $provider = 'fake';
        }

        //docasne
        if(is_null($provider) && !is_null($user) && ($user?->login === 'u1182')) {
            $provider = 'email';
        }

        return match ($provider) {
            'fake' => new LocalFakeSenderStrategy(),
            'email' => new EmailSenderStrategy(),
            default => new AccountSmsSenderStrategy(),
        };
    }
}
