<?php

namespace App\View\Components;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\View\Component;
use Illuminate\View\View as PaginationView;

class SimpleDataTable extends Component
{

    public Collection|EloquentCollection|array $dataForTable;
    public array $colOptions;

    public array $headerNames;
    public array $preparedTableData;
    public ?PaginationView $paginationLinks;
    public ?string $emptyTableText;

    public function __construct(Collection|EloquentCollection|array $dataForTable, array $colOptions, ?PaginationView $paginationLinks = null, ?string $emptyTableText = null)
    {
        $this->dataForTable = $dataForTable;
        $this->colOptions = $colOptions;
        $this->headerNames = [];
        $this->preparedTableData = [];
        $this->paginationLinks = $paginationLinks;
        $this->emptyTableText = $emptyTableText;
    }

    public function render(): View
    {
        foreach ($this->colOptions as $headerName => $option) {
            $this->headerNames[$headerName] = [$option['type'] ];
    //       Prepared space for sortable option etc.
        }
        foreach ($this->dataForTable as $data) {
            foreach ($this->colOptions as $headerName => $option) {
                /*  Value, yesno, and many_values type options  */
                if (in_array($option['type'], ['value', 'yesno', 'many_values'])) {
                    $tempValue = '';
                    $foreachData = $data;
                    foreach (explode('.', $option['path_in_collection']) as $path) {
//                        $tempValue = $data->{$path} ?? null;
                        $tempValue = $foreachData[$path] ?? null;

                        if (is_null($tempValue)) {
                            break;
                        }

                        if(is_array($tempValue) || is_object($tempValue)) {
                            $foreachData = $tempValue;
                        }
                    }

                    if ($option['type'] == 'many_values' && !empty($tempValue)) {
                        $arrayOfValues = [];
                        foreach ($tempValue as $valueData) {
                            $arrayOfValues[] = $valueData[$option['get_attribute']] ?? null;
                        }
                        $tempValue = $arrayOfValues;
                    }
                    $this->preparedTableData[$data->id][$headerName]['value'] = $tempValue;

                    if (isset($option['custom_classes'])) {
                        $this->preparedTableData[$data->id][$headerName]['custom_classes'] = $option['custom_classes'];
                    }
                }

                /*  Action type options   */
                if ($option['type'] === 'actions') {
                    foreach ($option['available_actions'] as $actionName => $isAvailable) {
                        $url = match ($actionName) {
                          'edit' => $option['base_path'] . '/' . $data->id.'/edit',
                            default => $option['base_path'] . '/' . $data->id,
                        };
                        $this->preparedTableData[$data->id][$headerName][$actionName]['is_available'] = $isAvailable;
                        $this->preparedTableData[$data->id][$headerName][$actionName]['url'] = $url;

                    }
                }
            }
        }
        return view('components.simple-data-table');
    }

}
