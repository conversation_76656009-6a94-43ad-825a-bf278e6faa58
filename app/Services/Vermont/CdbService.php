<?php

namespace App\Services\Vermont;

use App\Models\Base\Address;
use App\Models\Base\City;
use App\Models\Base\Country;
use App\Models\Base\Currency;
use App\Models\Organisation\Centre;
use App\Models\Organisation\CentreType;
use App\Models\Organisation\Company;
use App\Models\Organisation\Department;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CdbService
{
    private string $service_url;
    private string $service_key;

    public function __construct()
    {
        $this->service_url = config('vermont.cdb.url');
        $this->service_key = config('vermont.cdb.key');
    }

    public function check(): bool
    {
        return (bool) $this->getData('check');
    }

    public function updateCurrencies(): string
    {
        $currencies = $this->getData('currencies');

        if(is_null($currencies)){
            return $this->returnIfIsNull(__METHOD__);
        }

        foreach($currencies as $currency){
            $currency = Currency::updateOrCreate(
                [
                    'cdb_id' => $currency->id,
                ],
                [
                    'code' => $currency->code,
                    'name' => $currency->name,
                    'symbol' => $currency->symbol,
                ]
            );

            if(in_array($currency->code, ['EUR', 'CZK', 'HUF'])) {
                if ($currency->wasRecentlyCreated) {
                    $currency->is_active = true;
                    $currency->save();
                }
            }

        }

        return 'Updated/Created currencies: '.count($currencies);
    }

    public function updateCountries(): string
    {
        $countries = $this->getData('countries');

        if(is_null($countries)){
            return $this->returnIfIsNull(__METHOD__);
        }

        $currencies = Currency::select('id', 'cdb_id')->get()->keyBy('cdb_id');

        foreach($countries as $country){
            Country::updateOrCreate(
                [
                    'cdb_id' => $country->id,
                ],
                [
                    'code' => strtoupper($country->code),
                    'name' => $country->name,
                    'currency_id' => $currencies[$country->currency_id]->id ?? null,
                ]
            );
        }

        return 'Updated/Created counties: '.count($countries);
    }

    public function updateCities(): string
    {
        $cities = $this->getData('cities');

        if(is_null($cities)){
            return $this->returnIfIsNull(__METHOD__);
        }

        $countries = Country::select('id', 'cdb_id')->get()->keyBy('cdb_id');

        foreach($cities as $city){
            City::updateOrCreate(
                [
                    'cdb_id' => $city->id,
                ],
                [
                    'name' => $city->name,
                    'country_id' => $countries[$city->country]->id ?? null,
                ]
            );
        }

        return 'Updated/Created cities: '.count($cities);
    }

    public function updateAddresses(): string
    {
        $addresses = $this->getData('addresses');

        if(is_null($addresses)){
            return $this->returnIfIsNull(__METHOD__);
        }

        $countries = Country::select('id', 'cdb_id')->get()->keyBy('cdb_id');
        $cities = City::select('id', 'cdb_id')->get()->keyBy('cdb_id');

        foreach($addresses as $address){
            Address::updateOrCreate(
                [
                    'cdb_id' => $address->id,
                ],
                [
                    'country_id' => $countries[$address->country]->id ?? null,
                    'city_id' => $cities[$address->city]->id ?? null,
                    'city_section' => $address->city_section,
                    'shopping_center' => $address->shopping_centrum,
                    'street' => $address->street,
                    'number' => $address->number,
                    'zip' => $address->zip,
                ]
            );
        }

        return 'Updated/Created addresses: '.count($addresses);
    }

    public function updateCompanies(): string
    {
        $companies = $this->getData('companies');

        if(is_null($companies)){
            return $this->returnIfIsNull(__METHOD__);
        }

        $addresses = Address::select('id', 'cdb_id')->get()->keyBy('cdb_id');

        foreach($companies as $company){
            Company::updateOrCreate(
                [
                    'cdb_id' => $company->id,
                ],
                [
                    'address_id' => $addresses[$company?->address?->id]->id ?? null,
                    'code' => strtoupper($company->code),
                    'name' => $company->name,
                ]
            );
        }

        return 'Updated/Created companies: '.count($companies);
    }

    public function updateCentreTypes(): string
    {
        $centreTypes = $this->getData('centretypes');

        if(is_null($centreTypes)){
            return $this->returnIfIsNull(__METHOD__);
        }

        foreach($centreTypes as $centreType){
            CentreType::updateOrCreate(
                [
                    'cdb_id' => $centreType->id,
                ],
                [
                    'code' => $centreType->name,
                    'name' => $centreType->name,
                ]
            );
        }

        return 'Updated/Created centre types: '.count($centreTypes);
    }

    public function updateCentres(): string
    {
        $centres = $this->getData('centres');

        if(is_null($centres)){
            return $this->returnIfIsNull(__METHOD__);
        }

        $addresses = Address::select('id', 'cdb_id')->get()->keyBy('cdb_id');
        $centreTypes = CentreType::select('id', 'cdb_id')->get()->keyBy('cdb_id');
        $companies = Company::select('id', 'cdb_id')->get()->keyBy('cdb_id');

        foreach($centres as $centre){
            if($centre->code[1] === 'W') {
                continue;
            }

            $createdUpdatedCentre = Centre::updateOrCreate(
                [
                    'cdb_id' => $centre->id,
                ],
                [
                    'address_id' => $addresses[$centre->address->id]->id ?? null,
                    'centre_type_id' => $centreTypes[$centre->centretype]->id ?? null,
                    'company_id' => $companies[$centre->company]->id ?? null,
                    'code' => strtoupper($centre->code),
                    'name' => $centre->name,
                    'email' => $centre->contacts->email,
                    'phone' => $centre->contacts->mobile,
                ]
            );

            if($createdUpdatedCentre->wasRecentlyCreated){
                $createdUpdatedCentre->update(['is_active' => $centre->active]);
            }
        }

        $dbCentres = Centre::select('id', 'cdb_id')->get()->keyBy('cdb_id');

        foreach($centres as $centre){
            if(empty($centre->parent)) {
                continue;
            }

            Centre::updateOrCreate(
                [
                    'cdb_id' => $centre->id,
                ],
                [
                    'parent_id' => $dbCentres[$centre->parent]->id ?? null,
                ]
            );
        }

        return 'Updated/Created centres: '.count($centres);
    }

    public function updateDepartments(): string
    {
        $departments = $this->getData('departments');

        if(is_null($departments)){
            return $this->returnIfIsNull(__METHOD__);
        }

        foreach($departments as $department){
            $createdUpdatedDepartment = Department::updateOrCreate(
                [
                    'cdb_id' => $department->id,
                ],
                [
                    'code' => strtoupper($department->code),
                    'name' => $department->name,
                ]
            );

            if($createdUpdatedDepartment->wasRecentlyCreated){
                $createdUpdatedDepartment->update(['is_active' => $department->active]);
            }
        }

        $dbDepartments = Department::select('id', 'cdb_id')->get()->keyBy('cdb_id');

        foreach($departments as $department){
            if(empty($department->parent)) {
                continue;
            }

            Department::updateOrCreate(
                [
                    'cdb_id' => $department->id,
                ],
                [
                    'parent_id' => $dbDepartments[$department->parent]->id ?? null,
                ]
            );
        }

        return 'Updated/Created departments: '.count($departments);
    }

    private function getData($type)
    {
        $errorStart = 'CdbService, method getData('.$type.') - ';

        $result = Http::withoutVerifying()
            ->get(
                $this->service_url . $type,
                [
                    'key' => $this->service_key
                ]
            );

        if($result->failed()) {
            Log::error($errorStart.$result->reason());

            return null;
        }

        $returnObject = $result->object();

        if(is_null($returnObject)) {
            Log::error($errorStart.'Object is null');

            return null;
        }

        if($returnObject->result !== 200) {
            Log::error($errorStart.implode(', ', $returnObject->errors ?? ''));
            return null;
        }

        return $returnObject->{$type} ?? null;
    }

    private function returnIfIsNull($method): string
    {
        $text = 'CdbService, method '.$method.' - no data from API';
        Log::warning($text);

        return $text;
    }
}
