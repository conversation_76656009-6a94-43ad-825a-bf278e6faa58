<?php

namespace App\Services\Vermont;

use App\Enums\Person\Assigment\AssigmentStatusEnum;
use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Enums\Person\Contract\ContractStatusEnum;
use App\Enums\Person\Contract\ContractTypeEnum;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Company;
use App\Models\Organisation\Position;
use App\Models\Organisation\Section;
use App\Models\Person\Assigment;
use App\Models\Person\Contract;
use App\Models\Person\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Hrms1Service
{
    private string $service_url;
    private string $service_key;
    private \Illuminate\Support\Collection|null $companies = null;
    private \Illuminate\Support\Collection|null $positions = null;
    private \Illuminate\Support\Collection|null $centres = null;

    public function __construct()
    {
        $this->service_url = config('vermont.hrms1.url');
        $this->service_key = config('vermont.hrms1.key');
    }

    public function check(): bool
    {
        return (bool) $this->getData('check');
    }

    public function updateSections(): string
    {
        $sections = $this->getData('sections');

        if(is_null($sections)){
            return $this->returnIfIsNull(__METHOD__);
        }

        foreach($sections as $section){
            Section::updateOrCreate(
                [
                    'hrms_id' => $section->id,
                ],
                [
                    'code' => $section->code,
                    'name' => $section->name,
                ]
            );
        }

        return 'Updated/Created sections: '.count($sections);
    }

    public function updatePositions(): string
    {
        $positions = $this->getData('positions');

        if(is_null($positions)){
            return $this->returnIfIsNull(__METHOD__);
        }

        foreach($positions as $position){
            $createdUpdatedPosition = Position::updateOrCreate(
                [
                    'hrms_id' => $position->id,
                ],
                [
                    'code' => trim($position->code),
                    'name' => trim($position->name),
                    'name_hr' => trim($position->name_hr),
                    'is_active' => $position->active,
                ]
            );
        }

        return 'Updated/Created positions: '.count($positions);
    }

    private function getData($type, $params = [])
    {
        $errorStart = 'Hrms1Service, method getData('.$type.') - ';

        $result = Http::withoutVerifying()
            ->get(
                $this->service_url . $type,
                [
                    'key' => $this->service_key,
                    ...$params
                ]
            );

        if($result->failed()) {
            Log::error($errorStart.$result->reason());

            return null;
        }

        $returnObject = $result->object();

        if(is_null($returnObject)) {
            Log::error($errorStart.'Object is null');

            return null;
        }

        if($returnObject->result !== 200) {
            Log::error($errorStart.implode(', ', $returnObject->errors ?? ''));
            return null;
        }

        return $returnObject->{$type} ?? null;
    }

    private function returnIfIsNull($method): string
    {
        $text = 'Hrms1Service, method '.$method.' - no data from API';
        Log::warning($text);

        return $text;
    }
}
