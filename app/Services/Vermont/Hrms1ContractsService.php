<?php

namespace App\Services\Vermont;

use App\Enums\Person\Assigment\AssigmentStatusEnum;
use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Enums\Person\Contract\ContractStatusEnum;
use App\Enums\Person\Contract\ContractTypeEnum;
use App\Models\Base\Currency;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Company;
use App\Models\Organisation\Position;
use App\Models\Person\Assigment;
use App\Models\Person\Contract;
use App\Models\Person\User;
use App\Models\Remuneration\Salary;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Hrms1ContractsService
{
    private string $service_url;
    private string $service_key;
    private \Illuminate\Support\Collection|null $companies = null;
    private \Illuminate\Support\Collection|null $positions = null;
    private \Illuminate\Support\Collection|null $centres = null;

    public function __construct()
    {
        $this->service_url = config('vermont.hrms1_contracts.url');
        $this->service_key = config('vermont.hrms1_contracts.key');
    }

    public function check(): bool
    {
        return (bool) $this->getData('check');
    }

    public function updateContracts(int $page = 1): string
    {
        $contracts = $this->getData('contracts', ['page' => $page]);

        if(is_null($contracts)){
            return $this->returnIfIsNull(__METHOD__);
        }

        $this->companies = is_null($this->companies) ? Company::with('address:id,country_id', 'address.country:id,code')->select('id', 'cdb_id', 'address_id')->get()->keyBy('cdb_id') : $this->companies;
        $this->positions = is_null($this->positions) ? Position::select('id', 'hrms_id')->get()->keyBy('hrms_id') : $this->positions;
        $this->centres = is_null($this->centres) ? Centre::select('id', 'cdb_id')->get()->keyBy('cdb_id') : $this->centres;

        foreach($contracts as $contract){
            try {
                $this->updateContract($contract);
            }catch (\Throwable $t) {
                Log::warning('Hrms1ContractsService - updateContracts - contract can not be updated - '.$t->getMessage());
                Log::warning($t);
            }
        }

        if(count($contracts) === 100) {
            $lines = $this->updateContracts($page + 1);

            return 'Updated/Created contracts: '.((($page - 1) * 100) + count($contracts)).', '.$lines;
        }

        return 'Updated/Created contracts: '.((($page - 1) * 100) + count($contracts));
    }

    public function createTemps(): string
    {
        $temps = $this->getData('temps');

        if(is_null($temps)){
            return $this->returnIfIsNull(__METHOD__);
        }

        $contracts = Contract::with('company:id,name')->select('id', 'company_id', 'hrms_id')->get()->keyBy('hrms_id');
        $currencies = Currency::active()->get()->keyBy('code');
        $creator = User::where('login', 'u1392')->first();

        foreach($temps as $temp){
            $contract = $contracts[$temp->contract_id] ?? null;

            if(is_null($contract)){
                continue;
            }

            if(str_contains($contract->company->name, 'Hungary')){
                $tempCurrencyId = $currencies[Currency::CODE_HUF]->id;
            }elseif(str_contains($contract->company->name, 'Slovakia')){
                $tempCurrencyId = $currencies[Currency::CODE_EUR]->id;
            }else{
                $tempCurrencyId = $currencies[Currency::CODE_CZK]->id;
            }

            Salary::create([
                'contract_id' => $contract->id,
                'validity' => $temp->validity,
                'base' => $temp->base,
                'addition' => $temp->addition,
                'currency_id' => $tempCurrencyId,
                'creator_id' => $creator->id,
            ]);
        }

        return 'Updated/Created retail temps: '.count($temps);
    }

    private function updateContract($contract): void
    {
        $contractCompany = $this->companies[$contract->company];

        $saveRealData = app()->isProduction() || config('vermont.hrms1_contracts.forced_real_data_from_service');

        $userUpdateArray = [
            'is_active' => 1,
            'first_name' => $contract->staff->name,
            'last_name' => $saveRealData ? $contract->staff->surname : fake('sk_sk')->lastName(),
            'email' => $saveRealData ? $contract->staff?->email : fake('sk_sk')->email(),
            'phone' => $saveRealData ? $contract->staff?->phone : fake('sk_sk')->phoneNumber(),
        ];

        if($contract->primary) {
            $userUpdateArray['lang'] = strtolower($contractCompany->address->country->code);
            $userUpdateArray['lang'] = ($userUpdateArray['lang'] === 'cz') ? 'cs' : $userUpdateArray['lang'];
        }

        $user = User::updateOrCreate([
            'hrms_id' => $contract->staff->id,
            'login' => $contract->employee->uid,
        ], $userUpdateArray);

        $user->workData()->updateOrCreate([
            'user_id' => $user->id,
        ], [
            'email' => empty($contract?->employee?->email) ? null : ($saveRealData ? $contract->employee->email : fake('sk_sk')->email()),
            'mobile' => empty($contract?->employee?->mobile) ? null : ($saveRealData ? $contract->employee->mobile : fake('sk_sk')->phoneNumber()),
            'phone' => empty($contract?->employee?->phone) ? null : ($saveRealData ? $contract->employee->phone : fake('sk_sk')->phoneNumber()),
        ]);

        $dbContract = Contract::updateOrCreate([
            'user_id' => $user->id,
            'hrms_id' => $contract->id,
        ], [
            'company_id' => $contractCompany->id,
            'type' => ContractTypeEnum::fromHrms1Code($contract->jobtype)->value,
            'status' => ContractStatusEnum::fromContractDates($contract->start, $contract->end)->value,
            'is_primary' => (bool) $contract->primary,
            'percentage' => $contract->load * 100,
            'sign_date' => $contract->signing ?? null,
            'start_date' => $contract->start ?? null,
            'trial_date' => $contract->trial ?? null,
            'definite_date' => $contract->definite ?? null,
            'end_notice_date' => $contract->endnotice ?? null,
            'end_date' => $contract->end ?? null,
            'payslip_code' => $contract->number ?? null,
            'is_discount' => (bool) $contract->discount,
            'is_archived' => (bool) $contract->signed_archived,
        ]);

        if($dbContract->is_primary) {
            $primaryAssigment = Assigment::updateOrCreate([
                'user_id' => $user->id,
                'country_id' => $contractCompany->address->country_id,
                'position_id' => $this->positions[$contract->staff->position]->id ?? null,
                'centre_id' => $this->centres[$contract->staff->store]->id ?? null,
            ], [
                'valid_from_date' => $dbContract->start,
                'status' => ($status = AssigmentStatusEnum::fromHrmsId($contract->staff->status))->value,
                'type' => $status->isEmployeeStatus() ? AssigmentTypeEnum::EMPLOYEE->value : AssigmentTypeEnum::FINISHED->value,
                'is_from_hrms1' => true,
                'is_primary' => true,
            ]);

            Assigment::where('user_id', $user->id)->where('id', '!=', $primaryAssigment->id)->update(['is_primary' => false]);

            if($saveRealData) {
                $user->personalData()->updateOrCreate([
                    'user_id' => $user->id,
                ], [
                    'birth_name' => $contract->personal->birth_name ?? null,
                    'birth_date' => $contract->personal->birth_date ?? null,
                    'birth_place' => $contract->personal->birth_place ?? null,
                    'birth_number' => $contract->personal->birth_number ?? null,
                    'identity_card' => $contract->personal->identity_card ?? null,
                    'residence_country' => $contract->personal->residence_country ?? null,
                    'residence_city' => $contract->personal->residence_city ?? null,
                    'residence_street' => $contract->personal->residence_street ?? null,
                    'residence_zip' => $contract->personal->residence_zip ?? null,
                    'mothers_name' => $contract->personal->mothers_name ?? null,
                    'tax_number' => $contract->personal->tax_number ?? null,
                    'social_security_number' => $contract->personal->social_security_number ?? null,
                ]);
            }
        }
    }

    public function validateAssigmentTypes(): string
    {
        $userWithoutValidContracts = User::whereDoesntHave('activeOrFutureContracts')->pluck('id');

        Assigment::whereIn('user_id', $userWithoutValidContracts)
            ->where('type', AssigmentTypeEnum::EMPLOYEE)
            ->update(['valid_to_date' => date('Y-m-d'), 'type' => AssigmentTypeEnum::FINISHED]);

        return 'Setting former employee assigments for users count: '.$userWithoutValidContracts->count();
    }

    private function getData($type, $params = []): array|null|bool
    {
        $errorStart = 'Hrms1ContractsService, method getData('.$type.') - ';

        $result = Http::withoutVerifying()
            ->get(
                $this->service_url . $type,
                [
                    'key' => $this->service_key,
                    ...$params
                ]
            );

        if($result->failed()) {
            Log::error($errorStart.$result->reason());

            return null;
        }

        $returnObject = $result->object();

        if(is_null($returnObject)) {
            Log::error($errorStart.'Object is null');

            return null;
        }

        if($returnObject->result !== 200) {
            Log::error($errorStart.implode(', ', $returnObject->errors ?? ''));
            return null;
        }

        return $returnObject->{$type} ?? null;
    }

    private function returnIfIsNull($method): string
    {
        $text = 'Hrms1ContractsService, method '.$method.' - no data from API';
        Log::warning($text);

        return $text;
    }
}
