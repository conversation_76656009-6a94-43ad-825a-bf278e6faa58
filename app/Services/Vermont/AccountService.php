<?php

namespace App\Services\Vermont;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AccountService
{
    private string $service_url;
    private string $service_user;
    private string $service_pass;

    public function __construct()
    {
        $this->service_url = config('vermont.account.url');
        $this->service_user = config('vermont.account.user');
        $this->service_pass = config('vermont.account.pass');
    }

    public function sendSms($recipient, $message): void
    {
        $response = Http::withBasicAuth(
                $this->service_user,
                $this->service_pass
            )
            ->post(
                $this->service_url.'sms',
                [
                    'recipient' => $recipient,
                    'message' => $message,
                ]
            );

        if($response->failed()) {
            throw new \Exception('Account request not successfull!');
        }

        $responseObject = $response->object();

        Log::info(json_encode($responseObject));

        if(!property_exists($responseObject, 'success')) {
            throw new \Exception('Response in unknown format!');
        }

        if(!$responseObject?->success) {
            throw new \Exception('Sending SMS not successful!');
        }
    }
}
