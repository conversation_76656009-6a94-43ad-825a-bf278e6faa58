<?php

namespace App\Services;

use App\Enums\Remuneration\ProposalSubType;
use App\Enums\Remuneration\ProposalType;
use App\Services\Employee\Remuneration\Proposal\AttributeService;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\Response;

class HelperService
{
    public function prepareEnumByName(string $enumName, ?string $validity = null): array
    {
        $attributeService = new AttributeService();
        $enumClass = "App\\Enums\\".$enumName;
        if (!class_exists($enumClass)) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'message' => "Enum {$enumName} not found",
            ];
        }
        $data = [
            'status' => Response::HTTP_OK,
            'message' => "Enum found",
            'cases' => [],
        ];
        foreach ($enumClass::cases() as $case) {
            $validityMinDate = $attributeService->validityMinDate($case->value);
            $data['cases'][$case->value] = [
                'value' => $case->value,
                'name' => $case->name,
                'translation' => $case->translation() ?? $case->name,
                'enable_paid_via_agreement' => in_array($case->value, config('vermont.enum_settings.proposal_type.enable_is_paid_via_agreement')),
                'enable_paid_via_another_way' => in_array($case->value, config('vermont.enum_settings.proposal_type.enable_is_paid_via_agreement')),
                'enable_without_notify' => in_array($case->value, config('vermont.enum_settings.proposal_type.enable_without_notify')),
                'icon' => method_exists($enumClass, 'getIcon') ? $case->getIcon() : '',
                'disabled_by_validity' => !Carbon::parse($validity)->greaterThanOrEqualTo($validityMinDate)
            ];


            if ($enumClass === ProposalType::class) {
                /* Check if proposal type should contain addition value */
                $data['cases'][$case->value]['addition_enabled'] = in_array(
                    $case->value,
                    config('vermont.enum_settings.proposal_type.addition_allowed')
                );

                /*  Check if proposal type should contain subtypes  */
                $containSubTypes = config('vermont.enum_settings.proposal_type.contain_subtypes');
                if (array_key_exists($case->value, $containSubTypes)) {
                    $method = $containSubTypes[$case->value];
                    $oneOfKindPerMonth = config('vermont.enum_settings.proposal_subtype.one_of_kind_per_month');
                    $reasonRequired = config('vermont.enum_settings.proposal_subtype.reason_required');
                    if (method_exists(ProposalSubType::class, $method)) {
                        foreach (ProposalSubType::$method() as $subType) {
                            $belongsToOneOfKindPerMonth = [];
                            foreach ($oneOfKindPerMonth as $key => $oneOfKindPerMonthGroup) {
                                if (in_array($subType->value, $oneOfKindPerMonthGroup)) {
                                    $belongsToOneOfKindPerMonth[] = $key;
                                }
                            }

                            $data['cases'][$case->value]['subcases'][$subType->value] = [
                                'value' => $subType->value,
                                'name' => $subType->name,
                                'translation' => $subType->translation() ?? $subType->name,
                                'one_of_kind_per_month_groups' => $belongsToOneOfKindPerMonth,
                                'reason_required' => in_array($subType->value, $reasonRequired),
                                'show_retail_competition_link' => in_array(
                                    $subType->value,
                                    config('vermont.enum_settings.proposal_subtype.show_retail_competition_link')
                                )
                            ];
                        };
                    }
                }
            }
        }

        return $data;
    }
}
