<?php

namespace App\Services;

use Illuminate\Support\Facades\Session;


class FilterService
{
    public function processAllFilterDataForUniversalFilterComponent(
        ?array $filterRequest = [],
        ?string $filterName = null,
        ?string $filterNameForBookmarks = null,
        bool $enableFilterSaving = true
    ): array {
        $filterName = $filterName ?? str_replace('/', '_', request()->path());
        $filterNameForBookmarks = $filterNameForBookmarks ?? $filterName;

        if (empty($filterNameForBookmarks)) {
            $filterNameForBookmarks = $filterName;
        }
        $filter = $filterRequest ?? Session::get($filterName);
        $httpQuery = ['filter' => $filter];
        $this->setSession($filterName, $filter);
        $translations = __('filter');

        $filtering = false;
        if ($filter) {
            foreach ($filter as $type => $filterType) {
                foreach ($filterType as $key => $value) {
                    if (empty($value)) {
                        continue;
                    }
                    $filtering = true;
                }
            }
        }
        return [
            'filterName' => $filterName,
            'filterNameForBookmarks' => $filterNameForBookmarks,
            'filter' => $filter ?? [],
            'enableFilterSaving' => $enableFilterSaving,
            'saveFilterBaseUrl' => route('global.filter.save-filter-bookmark'),
            'filterInJson' => json_encode($filter),
            'actualFilterUrl' => url()->current().'?'.httpBuildQueryWithEmptyValuesPreserved($httpQuery),
            'userSavedPresets' => auth()->user()->filterBookmarks()->where('filter_name', $filterNameForBookmarks)->get(),
            'translations' => $translations,
            'resetFilterRoute' => route('global.filter.reset-filter', [$filterName]),
            'resetFilterAttributeRoute' => route('global.filter.reset-filter-attribute', [$filterName]),
            'isFiltering' => $filtering,
        ];
    }

    public function setSession(string $filterName, ?array $filter): void
    {
        if (is_null($filter)) {
            return;
        }
        Session::put($filterName, $filter);
    }
}