<?php

namespace App\Services\Person;

use App\Models\Person\User;
use App\Models\RaP\Permission;
use Illuminate\Support\Facades\Cache;

class UserService
{
    public static function getMenuItemsCached(?int $userId = null): array
    {
        if(is_null($userId)) {
            $userId = auth()->id();
        }

        if(is_null($userId)) {
            return [];
        }

        return cache()->remember('employee.'.$userId.'.menu.items', config('session.lifetime') - 1, fn() => self::menuItems($userId));
    }

    public static function clearMenuItemsCache(?int $userId = null): void
    {
        Cache::forget('employee.'.$userId.'.menu.items');
    }

    private static function menuItems(?int $userId = null): array
    {
        if(is_null($userId)) {
            return [];
        }

        $items = [];

        $items['dashboard'] = [
            'href' => route('dashboard'),
            'label' => __('employee.navigation.dashboard'),
            'target' => '_self',
            'activeGroup' => 'dashboard',
        ];

        $menuUser = User::find($userId);
        $menuUser->load('validAssigments', 'validAssigments.position', 'validAssigments.position.children');
        $canViewPayMenuItemProposals = $menuUser->validAssigments->pluck('position.children.*.pivot.is_pay_approver')->flatten()->unique()->contains(1);
        if(!$canViewPayMenuItemProposals) {
            $canViewPayMenuItemProposals = $menuUser->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_CREATOR_WITHOUT_HIERARCHY);
        }
        $canViewPayMenuItemSalaries = $menuUser->validAssigments->pluck('position.children.*.pivot.is_pay_viewer')->flatten()->unique()->contains(1);

        if($canViewPayMenuItemProposals || $canViewPayMenuItemSalaries) {
            $items['remunerations'] = [
                'href' => route('employee.remunerations.index'),
                'label' => __('employee.navigation.remunerations'),
                'target' => '_self',
                'activeGroup' => 'employee.remunerations.*',
                'sections' => [
                    'proposals' => $canViewPayMenuItemProposals,
                    'salaries' => $canViewPayMenuItemSalaries,
                ],
            ];
        }

        $items['hrms'] = [
            'href' => 'https://hrms.vermont.eu',
            'label' => __('employee.navigation.hrms').' <i class="hrms hrms-external-link ms-2 opacity-50"></i>',
            'target' => '_blank',
            'activeGroup' => 'hrms',
        ];

        return $items;
    }

    public static function getHrMenuItemsCached(?int $userId = null): array
    {
        if(is_null($userId)) {
            $userId = auth()->id();
        }

        if(is_null($userId)) {
            return [];
        }

        return cache()->remember('hr.'.$userId.'.menu.items', config('session.lifetime') - 1, fn() => self::hrMenuItems($userId));
    }

    public static function clearHrMenuItemsCache(?int $userId = null): void
    {
        Cache::forget('hr.'.$userId.'.menu.items');
    }

    private static function hrMenuItems(?int $userId = null): array
    {
        if(is_null($userId)) {
            return [];
        }

        $menuUser = User::find($userId);

        $items = [];

        $items['dashboard'] = [
            'href' => route('hr.index'),
            'label' => __('hr.users.dashboard'),
            'target' => '_self',
            'activeGroup' => 'dashboard',
        ];

        $items['organisation'] = [
            'href' => route('hr.organisation.index'),
            'label' => __('hr.navigation.organisation'),
            'target' => '_self',
            'activeGroup' => 'hr.organisation.*',
        ];

        $items['users'] = [
            'href' => route('hr.index'),
            'label' => __('hr.navigation.users'),
            'target' => '_self',
            'activeGroup' => 'hr.users.*',
        ];

        if($menuUser->can(Permission::HR_ADMINISTRATION_CAN_VIEW_SALARIES)){
            $items['remunerations'] = [
                'href' => route('hr.remunerations.approved_proposals.index'),
                'label' => __('hr.navigation.approved_proposals'),
                'target' => '_self',
            ];
        }

        return $items;
    }

    public static function getItMenuItemsCached(?int $userId = null): array
    {
        if(is_null($userId)) {
            $userId = auth()->id();
        }

        if(is_null($userId)) {
            return [];
        }

        return cache()->remember('it.'.$userId.'.menu.items', config('session.lifetime') - 1, fn() => self::itMenuItems($userId));
    }

    public static function clearItMenuItemsCache(?int $userId = null): void
    {
        Cache::forget('it.'.$userId.'.menu.items');
    }

    private static function itMenuItems(?int $userId = null): array
    {
        if(is_null($userId)) {
            return [];
        }

        $items['dashboard'] = [
            'href' => route('it.index'),
            'label' => __('it.navigation.dashboard'),
            'target' => '_self',
            'activeGroup' => 'dashboard',
        ];

        $items['persons'] = [
            'href' => route('it.persons.users.index'),
            'label' => __('it.navigation.persons'),
            'target' => '_self',
            'activeGroup' => 'it.persons.*',
        ];

        return $items;
    }
}
