<?php

namespace App\Services\Employee\Remuneration\Proposal;

use App\Enums\Remuneration\ProposalStatus;
use App\Models\Person\User;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\ProposalLog;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateProposalService
{
    public function createMultipleProposals(array $validatedRequestData, bool $isApproved = false, bool $returnCreatedIdForOne = false): null|string|int
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['status'] = ProposalStatus::PENDING->value;
            $validatedRequestData['subtype'] = empty($validatedRequestData['subtype']) ? null : $validatedRequestData['subtype'];
            $userIds = $validatedRequestData['users'];
            unset($validatedRequestData['users']);
            $usersWithProposals = User::whereIn('id', $userIds)->with([
                'remunerationProposals' => function ($q) use ($validatedRequestData) {
                    $q->where('status', '!=', ProposalStatus::REJECTED->value)
                        ->where('type', $validatedRequestData['type'])
                        ->where('subtype', $validatedRequestData['subtype'])
                        ->where('validity', $validatedRequestData['validity'])
                        ->where('centre_id', $validatedRequestData['centre_id'])
                        ->where('department_id', $validatedRequestData['department_id']);
                }
            ])->get()->keyBy('id');

            foreach($userIds as $userId) {
                $matchedUser = $usersWithProposals[$userId] ?? null;
                $hashNumber = $matchedUser ? $matchedUser->remunerationProposals->count() : 0;
                $hash = "{$validatedRequestData['centre_id']}_{$validatedRequestData['department_id']}_{$validatedRequestData['type']}_{$validatedRequestData['subtype']}_$hashNumber";
                $validatedRequestData['pairing_hash'] = $hash;
                $createdProposal = Proposal::create(
                    array_merge($validatedRequestData, ['user_id' => $userId])
                );

                $changes = DB::table(Proposal::getTableName())
                    ->select('subtype', 'base', 'addition', 'currency_id', 'reason', 'validity', 'approver_id', 'centre_id', 'department_id')
                    ->where('id', $createdProposal->id)
                    ->first();
                $changes = array_filter((array) $changes);

                ProposalLog::create([
                    'proposal_id' => $createdProposal->id,
                    'user_id' => $validatedRequestData['proposer_id'],
                    'status' => ProposalStatus::CREATED,
                    'changes' => $changes
                ]);

                if($isApproved) {
                    $createdProposal->update(['status' => ProposalStatus::APPROVED]);

                    ProposalLog::create([
                        'proposal_id' => $createdProposal->id,
                        'user_id' => $validatedRequestData['proposer_id'],
                        'status' => ProposalStatus::APPROVED,
                        'changes' => ['status' => ProposalStatus::APPROVED->value],
                    ]);
                }
            }

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        if(!empty($createdProposal) && $returnCreatedIdForOne) {
            return $createdProposal->id;
        }

        return null;
    }

    public function getUserPreselectedApproverId(Authenticatable $loggedUser, Collection|null $userParentAssigments): int|null
    {
        $userParentAssigments = is_null($userParentAssigments) ? collect() : $userParentAssigments;

        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents'
        );

        $parentPositionsCollection = collect();
        foreach($loggedUser->validAssigments as $va) {
            foreach($va->position->parents as $parent) {
                if(!$parent->pivot->is_pay_approver) {
                    continue;
                }
                $arr1 = $parent->toArray();
                $arr2 = $parent->pivot->toArray();
                $parentPositionsCollection->add(collect(array_merge($arr1, $arr2)));
            }
        }

        $parentPositionsCollection = $parentPositionsCollection->sortBy('hierarchy_order')->groupBy('hierarchy_order');

        $myCentresIds = $this->getUserCentresIds($loggedUser);

        $directUsers = collect();
        foreach($parentPositionsCollection as $hierarchyOrder => $positions) {

            foreach($positions as $position) {
                $thisDirectUsers = $userParentAssigments->where('position_id', $position['id'])
                    ->when($position['is_centre_pay_approver'], function($query) use ($myCentresIds) {
                        $query->whereIn('centre_id', $myCentresIds);
                    });

                if($thisDirectUsers->count()) {
                    $thisDirectUsers->each(function($user) use (&$directUsers) {
                        $directUsers->add($user);
                    });
                }
            }

            if($directUsers->count()) {
                break;
            }
        }

        $directUsers = $directUsers->unique('user_id');

        $selectedApproverId = null;
        if($directUsers->count() === 1) {
            $selectedApproverId = $directUsers->first()->user_id;
        }

        return $selectedApproverId;
    }

    public function getUserSalaryPlanApproverId(Authenticatable $loggedUser, Collection|null $userParentAssigments, int $creatorId = null): int|null
    {
        $userParentAssigments = is_null($userParentAssigments) ? collect() : $userParentAssigments;

        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents'
        );

        $parentPositionsCollection = collect();
        foreach($loggedUser->validAssigments as $va) {
            foreach($va->position->parents as $parent) {
                if(!$parent->pivot->is_pay_approver) {
                    continue;
                }
                $arr1 = $parent->toArray();
                $arr2 = $parent->pivot->toArray();
                $parentPositionsCollection->add(collect(array_merge($arr1, $arr2)));
            }
        }

        $parentPositionsCollection = $parentPositionsCollection->sortBy('hierarchy_order')->groupBy('hierarchy_order');

        $myCentresIds = $this->getUserCentresIds($loggedUser);

        $directUsers = collect();
        foreach($parentPositionsCollection as $hierarchyOrder => $positions) {
            foreach($positions as $position) {
                $thisDirectUsers = $userParentAssigments->where('position_id', $position['id'])
                    ->when($position['is_centre_pay_approver'], function($query) use ($myCentresIds) {
                        $query->whereIn('centre_id', $myCentresIds);
                    });

                if($thisDirectUsers->count()) {
                    $thisDirectUsers->each(function($user) use (&$directUsers) {
                        $directUsers->add($user);
                    });
                }
            }

            if($directUsers->count()) {
                break;
            }
        }

        $directUsers = $directUsers->unique('user_id');

        $selectedApproverId = null;

        if($directUsers->count() > 0) {
            if(!is_null($creatorId) && $directUsers->pluck('user_id')->contains($creatorId)) {
                $selectedApproverId = $creatorId;
            } else {
                $selectedApproverId = $directUsers->first()->user_id;
            }
        }

        return $selectedApproverId;
    }

    public function getUserDepartmentsIds(Authenticatable $loggedUser): array
    {
        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents'
        );
        return $loggedUser->validAssigments->pluck('position.pivotDepartments.*.department_id')->flatten()->unique()->toArray() ?? [];
    }

    public function getUserCentresIds(Authenticatable $loggedUser): array
    {
        $loggedUser->loadMissing(
            'validAssigments',
            'validAssigments.position',
            'validAssigments.position.pivotDepartments',
            'validAssigments.position.parents'
        );
        return $loggedUser->validAssigments->pluck('centre_id')->toArray() ?? [];
    }
}
