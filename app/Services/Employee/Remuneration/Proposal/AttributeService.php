<?php

namespace App\Services\Employee\Remuneration\Proposal;

use App\Enums\Remuneration\ProposalType;
use App\Models\RaP\Permission;
use Illuminate\Support\Facades\Log;

class AttributeService
{
    public function validityMinDate(?int $proposalType = null): string
    {
        if (is_null($proposalType)) {
            Log::warning('AttributeService => validityMinDate proposalType is null. Is this right?');
        }
        $user = auth()->user();
        if ($proposalType !== ProposalType::SALARY->value) {
            $validityMinDate = date(
                'Y-m-01',
                strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? 'last month' : 'this month')
            );
            if ($user->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
                $validityMinDate = date(
                    'Y-m-01',
                    strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? '-2 months' : 'last month')
                );
            }

            return $validityMinDate;
        }
        /*  Salary proposal types   */
        $validityMinDate = date(
            'Y-m-01',
            strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? 'this month' : 'next month')
        );
        if ($user->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)) {
            $validityMinDate = date(
                'Y-m-01',
                strtotime((date('d') <= config('hrms.remunerations.proposals.max_day_for_creating_new_proposals')) ? 'last month' : 'this month')
            );
        }

        return $validityMinDate;
    }
}