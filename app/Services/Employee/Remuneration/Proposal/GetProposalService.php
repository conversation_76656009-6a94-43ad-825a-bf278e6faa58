<?php

namespace App\Services\Employee\Remuneration\Proposal;

use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\ProposalLog;

class GetProposalService
{
    /**
     * Get logs where the creator is a user and older, or where the user is set as an approver and older
     * @param  Proposal  $proposal
     *
     * @return Proposal
     */
    public function filterLogsAndGetDataForStatuses(Proposal $proposal): Proposal
    {
        $loggedUser = auth()->user();
        $logs = ProposalLog::where('proposal_id', $proposal->id)->orderByDesc('created_at')->get();

        $assignedToUser = $logs->where('changes.approver_id', $loggedUser->id)->first();
        $logsAfterAssignedToUser = is_null($assignedToUser) ? collect() : $logs->where('id', '>', $assignedToUser->id);
        $isLogClaimedBack = $logsAfterAssignedToUser->count() === 1 && $logsAfterAssignedToUser->first()->status === ProposalStatus::WITHDRAWED;

        $proposal->is_claimable = $logs->first()?->user_id === $loggedUser->id
            && $logs->first()->status !== ProposalStatus::ROLLBACKED
            && ($proposal->payment_status
                || !$proposal->payment_status);
        $userLog = $logs->firstWhere('user_id', $loggedUser->id);

        if ($userLog) {
            $filteredLogs = $logs->where('created_at', '<=', $userLog->created_at);
        } else {
            $approverLog = $logs->first(function ($log) use ($loggedUser) {
                return isset($log->changes['approver_id']) && $log->changes['approver_id'] == $loggedUser->id;
            });

            $filteredLogs = $approverLog
                ? $logs->where('created_at', '<=', $approverLog->created_at)
                : collect();
        }
//        $proposal->setRelation('logs', $filteredLogs->values());
        $proposal->in_progress_for_this_user = $proposal->approver_id !== $loggedUser->id && ($filteredLogs->count() === 0 || $isLogClaimedBack);

        return $proposal;

}
}
