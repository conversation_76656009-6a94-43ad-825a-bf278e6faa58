<?php

namespace App\Services\Employee\Remuneration\Proposal;

use App\Enums\Remuneration\ProposalContractStatus;
use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalType;
use App\Models\Person\User;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\ProposalLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateProposalService
{
    public function updateProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null, $isApproved = false): null|string
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['status'] = $isApproved ? ProposalStatus::APPROVED->value : ProposalStatus::PENDING->value;

            $originalProposal = $proposal->toArray();

            $note = $validatedRequestData['note'] ?? null;
            unset($validatedRequestData['note'], $validatedRequestData['update_type']);

            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            foreach(['base', 'addition', 'reason'] as $column) {
                if ($proposal->$column === $originalProposal[$column]) {
                    unset($changes[$column]);
                }
            }
            unset($changes['updated_at'], $changes['status']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ($updaterId !== $proposal->approver_id) ? ProposalStatus::FORWARDED : ($validatedRequestData['status'] === ProposalStatus::APPROVED->value ? ProposalStatus::APPROVED->value : ProposalStatus::UPDATED->value),
                'changes' => count($changes) ? $changes : null,
                'note' => $note,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function forwardProposal(Proposal $proposal, int|User $approver, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $proposal->update(['approver_id' => is_int($approver) ? $approver : $approver->id]);

            $changes = $proposal->getChanges();
            unset($changes['updated_at'], $changes['status']);

            $note = $validatedRequestData['note'] ?? null;

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::FORWARDED,
                'changes' => count($changes) ? $changes : null,
                'note' => $note,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function approveProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();
            $note = $validatedRequestData['note'] ?? null;

            $validatedRequestData['status'] = ProposalStatus::APPROVED;
            if(is_null($proposal->payment_status)) {
                $validatedRequestData['payment_status'] = ($proposal->type === ProposalType::NON_FINANCIAL_REWARD)
                    ? ProposalPaymentStatus::WITHOUT_NOTIFICATION->value
                    : ProposalPaymentStatus::TO_BE_NOTIFIED->value;
            }
            $validatedRequestData['contract_status'] = ProposalContractStatus::TO_BE_ASSIGNED->value;
            $validatedRequestData['contract_id'] = null;
            unset($validatedRequestData['update_type'], $validatedRequestData['note']);
            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at'], $changes['status'], $changes['contract_status'], $changes['contract_id']);


            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::APPROVED,
                'changes' => count($changes) ? $changes : null,
                'note' => $note,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function rejectProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['status'] = ProposalStatus::REJECTED->value;
            $validatedRequestData['payment_status'] = null;

            $originalProposal = $proposal->toArray();

            $note = $validatedRequestData['note'] ?? null;
            unset($validatedRequestData['note'], $validatedRequestData['update_type']);

            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at'], $changes['status']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::REJECTED,
                'changes' => count($changes) ? $changes : null,
                'note' => $note,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function changeIsPaidViaAgreementForProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['is_paid_via_agreement'] = !$proposal->is_paid_via_agreement;
            unset($validatedRequestData['update_type']);
            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::UPDATED,
                'changes' => count($changes) ? $changes : null,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function changeIsPaidViaOtherWayForProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try {
            DB::beginTransaction();

            $validatedRequestData['is_paid_via_another_way'] = !$proposal->is_paid_via_another_way;
            $validatedRequestData['payment_status'] = $validatedRequestData['is_paid_via_another_way'] ? ProposalPaymentStatus::WITHOUT_NOTIFICATION
                : ProposalPaymentStatus::TO_BE_NOTIFIED;
            unset($validatedRequestData['update_type']);
            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::UPDATED,
                'changes' => count($changes) ? $changes : null,
            ]);

            DB::commit();
        } catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function changePaymentStatusForProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['payment_status'] = ($proposal->payment_status === ProposalPaymentStatus::TO_BE_NOTIFIED)
                ? ProposalPaymentStatus::WITHOUT_NOTIFICATION
                : ProposalPaymentStatus::TO_BE_NOTIFIED;
            unset($validatedRequestData['update_type']);
            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::UPDATED,
                'changes' => count($changes) ? $changes : null,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function usurpProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['approver_id'] = $updaterId;
            $note = $validatedRequestData['note'] ?? null;
            unset($validatedRequestData['note'], $validatedRequestData['update_type']);

            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at'], $changes['status']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::USURPED,
                'changes' => count($changes) ? $changes : null,
                'note' => $note,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function withdrawProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['approver_id'] = $updaterId;
            $note = $validatedRequestData['note'] ?? null;
            unset($validatedRequestData['note'], $validatedRequestData['update_type']);
            $validatedRequestData['status'] = ProposalStatus::PENDING;
            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at'], $changes['status']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::WITHDRAWED,
                'changes' => count($changes) ? $changes : null,
                'note' => $note,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function rollbackProposal(Proposal $proposal, array $validatedRequestData, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $validatedRequestData['approver_id'] = $proposal->proposer_id;
            $note = $validatedRequestData['note'] ?? null;
            unset($validatedRequestData['note'], $validatedRequestData['update_type']);

            $proposal->update($validatedRequestData);

            $changes = $proposal->getChanges();
            unset($changes['updated_at'], $changes['status']);

            ProposalLog::create([
                'proposal_id' => $proposal->id,
                'user_id' => $updaterId,
                'status' => ProposalStatus::ROLLBACKED,
                'changes' => count($changes) ? $changes : null,
                'note' => $note,
            ]);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }

    public function changeStatusForMultipleProposals($proposals, ProposalStatus $status, array $validatedRequestData, int $approverId = null, $updaterId = null): null|string
    {
        try{
            DB::beginTransaction();

            $updateArray = [];
            if($status === ProposalStatus::FORWARDED) {
                $updateArray['approver_id'] = $approverId;
            }else{
                $updateArray['status'] = $status;
            }

            if($status === ProposalStatus::APPROVED) {
                $updateArray['contract_status'] = ProposalContractStatus::TO_BE_ASSIGNED->value;
                $updateArray['contract_id'] = null;
                $updateArray['payment_status'] = \DB::raw('IF(
                        ISNULL(payment_status),
                        IF(type != '.ProposalType::NON_FINANCIAL_REWARD->value.', '.ProposalPaymentStatus::TO_BE_NOTIFIED->value.', '.ProposalPaymentStatus::WITHOUT_NOTIFICATION->value.'),
                        payment_status)
                    ');
            }

            $note = $validatedRequestData['note'] ?? null;

            Proposal::whereIn('id', $proposals->pluck('id'))->update($updateArray);

            $now = now();
            $logsArray = [];
            foreach($proposals as $proposal) {
                $logsArray[] = [
                    'proposal_id' => $proposal->id,
                    'user_id' => $updaterId,
                    'status' => $status->value,
                    'changes' => ($status === ProposalStatus::FORWARDED) ? json_encode(['approver_id' => $approverId]) : null,
                    'note' => $note,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            ProposalLog::insert($logsArray);

            DB::commit();
        }catch (\Throwable $t) {
            Log::error($t);
            DB::rollBack();

            return $t->getMessage();
        }

        return null;
    }
}
