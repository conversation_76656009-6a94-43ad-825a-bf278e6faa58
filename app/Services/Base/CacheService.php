<?php

namespace App\Services\Base;

use App\Models\Base\Currency;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Department;
use App\Models\Person\User;
use Illuminate\Support\Facades\Cache;

class CacheService
{
    private int $cacheTtl = 3600;

    public function getActiveCurrencies()
    {
        return Cache::remember('cache_currencies_active', $this->cacheTtl, function () {
            return Currency::active()->get();
        });
    }

    public function getCentres()
    {
        return Cache::remember('cache_centres', $this->cacheTtl, function () {
            return Centre::with('address:id,country_id', 'address.country:id,currency_id')->orderBy('code')->get();
        });
    }

    public function getDepartments()
    {
        return Cache::remember('cache_departments', $this->cacheTtl, function () {
            return Department::orderBy('name')->get();
        });
    }

    public function allUsersWithValidAssigments()
    {
        return Cache::remember('cache_all_users_with_valid_assigments', $this->cacheTtl, function () {
            return User::select('id', 'first_name', 'last_name')
                ->withWhereHas('validAssigments')
                ->with('validAssigments.centre:id,code')
                ->orderBy('last_name')
                ->orderBy('first_name')
                ->get()
                ->map(function ($user) {
                    $selectOption = $user->full_name;
                    $userCentres = $user->validAssigments->pluck('centre.code')->toArray();

                    if(count($userCentres)) {
                        $selectOption .= ' ('.implode(',', $userCentres).')';
                    }
                    $user->select_option = $selectOption;
                    return $user;
                });
        });
    }

    public function allUsersWithValidAssigmentsForProposals()
    {
//        return Cache::remember('cache_all_users_with_valid_assigments_for_proposals', $this->cacheTtl, function () {
            return User::select('id', 'first_name', 'last_name')
                ->withWhereHas('validAssigmentsForProposals')
                ->with('validAssigmentsForProposals.centre:id,code')
                ->orderBy('last_name')
                ->orderBy('first_name')
                ->get()
                ->map(function ($user) {
                    $selectOption = $user->full_name;
                    $userCentres = $user->validAssigmentsForProposals->pluck('centre.code')->toArray();

                    if(count($userCentres)) {
                        $selectOption .= ' ('.implode(',', $userCentres).')';
                    }
                    $user->select_option = $selectOption;
                    return $user;
                });
//        });
    }
}
