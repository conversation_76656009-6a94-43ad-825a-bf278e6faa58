<?php

if (!function_exists('httpBuildQueryWithEmptyValuesPreserved')) {
    function httpBuildQueryWithEmptyValuesPreserved($params, $prefix = null): string
    {
        $queryParts = [];

        foreach ($params as $key => $value) {
            if (is_array($value)) {
                $nestedPrefix = $prefix ? "{$prefix}[{$key}]" : $key;
                $queryParts[] = httpBuildQueryWithEmptyValuesPreserved($value, $nestedPrefix);
            } else {
                $encodedKey = $prefix ? "{$prefix}[{$key}]" : $key;
                $queryParts[] = urlencode($encodedKey).'='.urlencode((string)$value);
            }
        }

        return implode('&', $queryParts);
    }
}

if (! function_exists('formatMoney')) {
    function formatMoney($float): string
    {
        if(!is_numeric($float)) {
            return 'N/A';
        }

        $decimals = ((string) floor($float) !== (string) $float) ? 2 : 0;
        return number_format($float, $decimals, ',', ' ');
    }
}

if (! function_exists('formatDate')) {
    function formatDate($date, $format = null): string
    {
        if (empty($date)) {
            return '';
        }

        if(is_null($format)) {
            $format = match ((auth()->user()->lang ?? null)) {
                'cs' => 'd.m.Y',
                'sk' => 'd. m. Y',
                'hu' => 'Y.m.d.',
                default => 'Y-m-d',
            };
        }

        if (is_a($date, \Carbon\Carbon::class) || is_a($date, \Carbon\CarbonImmutable::class)) {
            return $date->format($format);
        }

        if (is_string($date)) {
            return date($format, strtotime($date));
        }

        if (is_numeric($date)) {
            return date($format, $date);
        }

        return 'Unknown input $date type';
    }
}

if (! function_exists('formatDateTime')) {
    function formatDateTime($dateTime, $format = null): string
    {
        if (empty($dateTime)) {
            return '';
        }

        if(is_null($format)) {
            $format = match ((auth()->user()->lang ?? null)) {
                'cs' => 'd.m.Y',
                'sk' => 'd. m. Y',
                'hu' => 'Y.M.d.',
                default => 'Y-m-d',
            };
        }

        $format .= ' H:i';

        if (is_a($dateTime, \Carbon\Carbon::class) || is_a($dateTime, \Carbon\CarbonImmutable::class)) {
            return $dateTime->format($format);
        }

        if (is_string($dateTime)) {
            return date($format, strtotime($dateTime));
        }

        if (is_numeric($dateTime)) {
            return date($format, $dateTime);
        }

        return 'Unknown input $dateTime type';
    }
}
