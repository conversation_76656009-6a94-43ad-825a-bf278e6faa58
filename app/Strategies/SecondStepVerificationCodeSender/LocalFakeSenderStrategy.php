<?php

namespace App\Strategies\SecondStepVerificationCodeSender;

use App\Interfaces\SecondStepVerificationCodeSenderInterface;
use App\Models\Person\User;
use App\Models\Person\UserAuthCode;

class LocalFakeSenderStrategy implements SecondStepVerificationCodeSenderInterface
{
    public function sendCode(User $user, UserAuthCode $code): void
    {
        flash()->warning('Simulácia odosielania sms s kódom: '.$code->code);
    }
}
