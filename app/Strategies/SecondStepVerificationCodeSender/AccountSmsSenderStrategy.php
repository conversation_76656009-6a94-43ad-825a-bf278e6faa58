<?php

namespace App\Strategies\SecondStepVerificationCodeSender;

use App\Interfaces\SecondStepVerificationCodeSenderInterface;
use App\Models\Person\User;
use App\Models\Person\UserAuthCode;
use App\Services\Vermont\AccountService;
use Illuminate\Support\Facades\Log;

class AccountSmsSenderStrategy implements SecondStepVerificationCodeSenderInterface
{
    public function sendCode(User $user, UserAuthCode $code):void
    {
        $user->loadMissing('workData');

        if(!$user?->workData?->mobile) {
            flash()->error(__('auth.second_step_verification.sms_can_not_be_sent'));
            return;
        }

        try{
            $accountService = new AccountService();
            $accountService->sendSms($user->workData->mobile, __('auth.second_step_verification.sms_text', ['code' => $code->code]));
        } catch (\Throwable $t) {
            Log::error($t->getMessage());
            flash()->error(__('auth.second_step_verification.sms_not_sent'));
            return;
        }

        flash()->warning(__('auth.second_step_verification.sms_sent'));
    }
}
