<?php

namespace App\Traits\Enums;

use Illuminate\Support\Collection;

trait EnumCollectionForFormSelectTrait
{
    public static function collectionForFormSelect(): Collection
    {
        $collection = collect();

        foreach(self::cases() as $type) {
            $collection->push((object) ['value' => $type->value, 'translation' => $type->translation()]);
        }

        return $collection->forFormSelect(valueColumn: 'value', optionColumn: 'translation');
    }
}
