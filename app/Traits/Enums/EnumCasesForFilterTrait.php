<?php

namespace App\Traits\Enums;

use Illuminate\Support\Collection;

trait EnumCasesForFilterTrait
{
    public static function casesForFilter($withName = false): Collection
    {
        $cases = collect();

        foreach(self::cases() as $case) {
            $cases->add(['value' => $case->value, 'translation' => $withName ? ($case->name.' ('.$case->translation().')') : $case->translation()]);
        }

//        $cases->prepend([
//            'value' => '',
//            'translation' => __('wms.general.all')
//        ]);

        return $cases;
    }
}
