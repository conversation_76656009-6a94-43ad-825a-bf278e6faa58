<?php

namespace App\Traits\Controllers\Employee\Remuneration;

use App\Models\Person\Assigment;
use App\Models\Person\User;
use Illuminate\Database\Eloquent\Collection;

trait PermissionTrait
{
    public function getValidAssigmentsWithChildren($includeCurrentUser = false, ?User $currentUser = null): ?Collection
    {
        return $this->getValidAssigments('children', $includeCurrentUser, $currentUser);
    }

    public function getValidAssigmentsWithChildrenForRemunerationProposals($includeCurrentUser = false, ?User $currentUser = null): ?Collection
    {
        return $this->getValidAssigments('children', $includeCurrentUser, $currentUser, 'forRemunerationProposals');
    }

    public function getValidAssigmentsWithChildrenForRemunerationSalaries($includeCurrentUser = false, ?User $currentUser = null): ?Collection
    {
        return $this->getValidAssigments('children', $includeCurrentUser, $currentUser, 'forRemunerationSalaries');
    }

    public function getValidAssigmentsWithParents($includeCurrentUser = false, ?User $currentUser = null): ?Collection
    {
        return $this->getValidAssigments('parents', $includeCurrentUser, $currentUser);
    }

    private function getValidAssigments(string $type = 'children', $includeCurrentUser = false, ?User $currentUser = null, ?string $forSpecificType = null): ?Collection
    {
        $loggedUser = is_null($currentUser) ? auth()->user() : $currentUser;
        $loggedUser->loadMissing('validAssigments', 'validAssigments.position', 'validAssigments.position.'.$type);

        $validAssigments = Assigment::query()
            ->valid()
            ->with('position', 'position.'.$type)
            ->select('user_id', 'type', 'centre_id', 'position_id')
            ->when(!is_null($forSpecificType), function($query) use($forSpecificType) {
                switch($forSpecificType) {
                    case 'forRemunerationProposals':
                        $query->forRemunerationProposals();
                        break;
                    case 'forRemunerationSalaries':
                        $query->forRemunerationSalaries();
                        break;
                }
            })
            ->where(function($query) use ($loggedUser, $includeCurrentUser, $type, $forSpecificType) {
                $query->where(function($query) use ($loggedUser, $type, $forSpecificType) {
                    $hasAtLeastOne = false;
                    foreach($loggedUser->validAssigments as $assigment) {
                        if(!$assigment->position) {
                            continue;
                        }

                        foreach($assigment->position->{$type} as $childOrParent) {
                            $isWhat = ($forSpecificType === 'forRemunerationSalaries') ? 'is_pay_viewer' : 'is_pay_approver';

                            if(!$childOrParent->pivot->{$isWhat}) {
                                continue;
                            }
                            if(request()->input('hierarchy_order')) {
                                if($childOrParent->pivot->hierarchy_order !== (int) request()->input('hierarchy_order')) {
                                    continue;
                                }
                            }
                            $query->orWhere(function ($query) use ($assigment, $childOrParent, &$hasAtLeastOne) {
                                $query->where('position_id', $childOrParent->id);
                                $query->when($childOrParent->pivot->is_centre_pay_approver, function ($query) use ($assigment) {
                                    $query->where('centre_id', $assigment->centre_id);
                                });
                                $hasAtLeastOne = true;
                            });
                        }
                    }
                    if(!$hasAtLeastOne) {
                        $query->orWhere('id', 0);
                    }
                })->when($includeCurrentUser, function($query) use($loggedUser) {
                    $query->orWhere('user_id', $loggedUser->id);
                });
            })
            ->get();

        $usersOfValidAssigments = $validAssigments->pluck('user_id')->unique();
        if(
            $usersOfValidAssigments->count() === 0 ||
            (
                ($usersOfValidAssigments->count() === 1) &&
                ($usersOfValidAssigments->first() === $loggedUser->id)
            )
        ) {
            return null;
        }

        return $validAssigments;
    }
}
