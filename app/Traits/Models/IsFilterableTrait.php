<?php

namespace App\Traits\Models;

use Illuminate\Database\Eloquent\Builder;

trait IsFilterableTrait
{
    public function scopeFilter(Builder $builder, array $filter): Builder
    {
        $signs = [
            'equal' => '=',
            'in' => '=',
            'notEqual' => '!=',
            'notIn' => '!=',
            'lessThanOrEqual' => '<=',
            'lessThan' => '<',
            'greaterThan' => '>',
            'greaterThanOrEqual' => '>=',
            'like' => 'LIKE',
            'notLike' => 'NOT LIKE',
            'hasNull' => 'null',
            'isNull' => 'null',
            'isNotNull' => 'null',
            'custom' => 'custom',
            'having' => 'having'
        ];
        foreach ($filter as $sign => $columnData) {
            if (!array_key_exists($sign, $signs)) {
                continue;
            }
            $selectedSign = $signs[$sign];
            foreach ($columnData as $columnName => $value) {
                if (is_null($value) || $sign == 'custom') {
                    continue;
                }
                if ($sign == 'having') {
                    foreach ($value as $cName => $v) {
                        $selectedSign = $signs[$columnName];
                        if (strpos($cName, '.')) {
                            continue;
                        }
                        if (is_null($v)) {
                            continue;
                        }
                        $builder = $builder->having(function ($subQ) use ($cName, $sign, $signs, $selectedSign, $v) {
                            $subQ->having(
                                $cName,
                                $selectedSign,
                                in_array($selectedSign, ['LIKE', 'NOT LIKE']) ? (str_contains(
                                    $v,
                                    '%'
                                ) ? "$v" : "%{$v}%") : $v
                            );
                            if ($sign == 'greaterThan' && $v < 0 || $sign == 'greaterThanOrEqual' && $v <= 0 || $sign == 'lessThan' && $v > 0
                                || $sign == 'lessThanOrEqual' && $v >= 0
                            ) {
                                $subQ->orHavingNull($cName);
                            }
                        });
                    }

                    continue;
                }
                if (strpos($columnName, '.')) {
                    $explodedColumn = explode('.', $columnName);
                    $columnName = array_pop($explodedColumn);
                    $relationPath = implode('.', $explodedColumn);

                    $builder = $builder->whereHas(
                        $relationPath,
                        function ($subQuery) use ($selectedSign, $relationPath, $value, $columnName, $sign) {
                            if (is_array($value) && $selectedSign === '=') {
                                if (in_array('null', $value)) {
                                    unset($value[array_search('null', $value)]);
                                    $subQuery->where(function ($subQ) use ($columnName, $value) {
                                        $subQ->whereIn($columnName, $value)->orWhereNull($columnName);
                                    });
                                } else {
                                    $subQuery->whereIn($columnName, $value);
                                }
                            } elseif (is_array($value) && $selectedSign === '!=') {
                                $subQuery->whereNotIn($columnName, $value);
                            } elseif (is_array($value) && $selectedSign !== '=' && $selectedSign !== '!=') {
                                abort(
                                    400,
                                    "You can't use the " . $selectedSign . " in filter with multiple select. Problematic input is " . $relationPath . '.'
                                    . $columnName
                                );
                            } elseif ($sign == 'isNull' && $value == '1') {
                                $subQuery->where(function ($subSubQuery) use ($columnName) {
                                    $subSubQuery->whereNull($columnName)->orWhere($columnName, '=', '');
                                });
                            } elseif ($sign == 'isNotNull' && $value == '1') {
                                $subQuery->whereNotNull($columnName)->where($columnName, '!=', '');
                            } else {
                                $subQuery->where(
                                    $columnName,
                                    $selectedSign,
                                    in_array($selectedSign, ['LIKE', 'NOT LIKE']) ? (str_contains(
                                        $value,
                                        '%'
                                    ) ? "$value" : "%{$value}%") : $value
                                );
                            }
                        }
                    );
                    continue;
                }

                if (is_array($value) && $selectedSign === '=') {
                    if (in_array('null', $value)) {
                        unset($value[array_search('null', $value)]);
                        $builder->where(function ($subQ) use ($columnName, $value) {
                            $subQ->whereIn($columnName, $value)->orWhereNull($columnName);
                        });
                    } else {
                        $builder = $builder->whereIn($columnName, $value);
                    }
                } elseif (is_array($value) && $selectedSign === '!=') {
                    $builder = $builder->whereNotIn($columnName, $value);
                } elseif (is_array($value) && $selectedSign !== '=') {
                    abort(
                        400,
                        "You can't use the " . $selectedSign . " in filter with multiple select. Problematic input is " . $columnName
                    );
                } elseif ($sign == 'isNull' && $value == '1') {
                    $builder = $builder->where(function ($q) use ($columnName) {
                        $q->whereNull($columnName)->orWhere($columnName, '=', '');
                    });
                } elseif ($sign == 'isNotNull' && $value == '1') {
                    $builder = $builder->whereNotNull($columnName)->where($columnName, '!=', '');
                } elseif ($sign == 'hasNull') {
                    if ($value == '1') {
                        $builder = $builder->where($columnName, null);
                    } elseif ($value == '2') {
                        $builder = $builder->where($columnName, '!=', null);
                    }
                } elseif (($sign == 'isNull' || $sign == 'isNotNull') && $value == 0) {
                    continue;
                } else {
                    $builder = $builder->where(function ($subQ) use ($columnName, $sign, $signs, $selectedSign, $value) {
                        $subQ->where(
                            $columnName,
                            $selectedSign,
                            in_array($selectedSign, ['LIKE', 'NOT LIKE']) ? (str_contains(
                                $value,
                                '%'
                            ) ? "$value" : "%{$value}%") : $value
                        );
                        if ($sign == 'greaterThan' && $value < 0 || $sign == 'greaterThanOrEqual' && $value <= 0 || $sign == 'lessThan' && $value > 0
                            || $sign == 'lessThanOrEqual' && $value >= 0
                        ) {
                            $subQ->orWhereNull($columnName);
                        }
                    });
                }
            }
        }
        return $builder;
    }
}
