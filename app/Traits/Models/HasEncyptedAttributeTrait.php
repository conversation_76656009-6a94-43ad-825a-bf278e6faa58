<?php

namespace App\Traits\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Encryption\Encrypter;

trait HasEncyptedAttributeTrait
{
    private function encryptedAttribute($type = 'text'): Attribute
    {
        $version = $this?->crypt_version;

        return Attribute::make(
            get: static function (string|null $value) use ($type, $version) {
                $key = config('hrms.secret.crypt_key');
                $encrypter = new Encrypter(key: $key, cipher: 'aes-256-cbc');

                try{
                    $getValue = is_null($value) ? null : $encrypter->decrypt($value);
                } catch(\Throwable $t) {
                    $getValue = ($type === 'number') ? 999999999999 : '!!! Decryption of attribute value failed !!!';
                }
                return $getValue;
            },
            set: static function (string|null $value) use ($version) {
                $key = config('hrms.secret.crypt_key');

                $encrypter = new Encrypter(key: $key, cipher: 'aes-256-cbc');

                return is_null($value) ? null : $encrypter->encrypt($value);
            },
        );
    }

    public function decryptEncryptedValue(string $type, ?string $value): mixed
    {
        if (is_null($value)) {
            return null;
        }

        $key = config('hrms.secret.crypt_key');
        $encrypter = new Encrypter(key: $key, cipher: 'aes-256-cbc');

        try {
            $decrypted = $encrypter->decrypt($value);
        } catch (\Throwable $t) {
            return $type === 'number' ? 999999999999 : '!!! Decryption failed !!!';
        }

        return $decrypted;
    }
}
