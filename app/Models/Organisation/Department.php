<?php

namespace App\Models\Organisation;

use App\Models\Base\Country;
use App\Models\Person\User;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\IsFilterableTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use GetTableNameTrait, IsFilterableTrait;

    protected $table = 'organisation_departments';
    protected $guarded = [];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    public function boardLeader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'board_user_id', 'id');
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id', 'id');
    }

    public function sections(): BelongsToMany
    {
        return $this->belongsToMany(Section::class, 'organisation_department_section', 'department_id', 'section_id');
    }

    public function pivotSections(): HasMany
    {
        return $this->hasMany(DepartmentSection::class, 'department_id');
    }

    public function centres(): BelongsToMany
    {
        return $this->belongsToMany(Centre::class, 'organisation_centre_department', 'department_id', 'centre_id');
    }

    public function positions(): BelongsToMany
    {
        return $this->belongsToMany(Position::class, 'organisation_department_position', 'department_id', 'position_id');
    }

    public function departmentCountryId(): ?int
    {
        return match ($this->code) {
            'RET-SK' => Country::ID_SLOVAKIA,
            'RET-CZ' => Country::ID_CZECHIA,
            'RET-HU' => Country::ID_HUNGARY,
            default => null,
        };
    }

    public function departmentCentreTypesIds(): ?array
    {
        return match ($this->code) {
            'RET-SK',
            'RET-CZ',
            'RET-HU' => [
                CentreType::ID_STORE,
                CentreType::ID_STORE_OUTLET,
            ],
            default => null,
        };
    }
}
