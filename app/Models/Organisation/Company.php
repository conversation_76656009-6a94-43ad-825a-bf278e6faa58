<?php

namespace App\Models\Organisation;

use App\Models\Base\Address;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\IsFilterableTrait;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Company extends Model
{
    use GetTableNameTrait, IsFilterableTrait;

    protected $table = 'organisation_companies';
    protected $guarded = [];

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'address_id');
    }

    #[Scope]
    public function active(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
}
