<?php

namespace App\Models\Organisation;

use App\Models\Base\Address;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\IsFilterableTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use phpDocumentor\Reflection\Types\Boolean;

class Centre extends Model
{
    use GetTableNameTrait, IsFilterableTrait;

    protected $table = 'organisation_centres';
    protected $guarded = [];

    protected $appends = ['full_name'];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Centre::class, 'parent_id');
    }

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'address_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function sections(): BelongsToMany
    {
        return $this->belongsToMany(Section::class, 'organisation_centre_section', 'centre_id', 'section_id');
    }

    public function pivotSections(): HasMany
    {
        return $this->hasMany(CentreSection::class, 'centre_id');
    }

    public function departments(): BelongsToMany
    {
        return $this->belongsToMany(Department::class, 'organisation_centre_department', 'centre_id', 'department_id');
    }

    public function pivotDepartments(): HasMany
    {
        return $this->hasMany(CentreDepartment::class, 'centre_id');
    }

    public function fullName(): Attribute
    {
        return new Attribute(
            get: fn () => $this->code.' ('.$this->name.')'
        );
    }

    public function isRetailCentre(): bool
    {
        return (($this->centre_type_id === CentreType::ID_STORE) || ($this->centre_type_id === CentreType::ID_STORE_OUTLET));
    }

    public function isWarehouseCentre(): bool
    {
        return $this->centre_type_id === CentreType::ID_WAREHOUSE;
    }

    public function isOfficeCentre(): bool
    {
        return $this->centre_type_id === CentreType::ID_OFFICE;
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
}
