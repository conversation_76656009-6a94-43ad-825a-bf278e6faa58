<?php

namespace App\Models\Organisation;

use App\Models\Person\Assigment;
use App\Models\Remuneration\ProposalSubTypeBudgetExcludedPosition;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\IsFilterableTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Position extends Model
{
    use GetTableNameTrait, IsFilterableTrait;

    protected $table = 'organisation_positions';
    protected $guarded = [];

    const ID_BRIG = 6;
    const ID_AgBrig = 7;
    const ID_VerBrig = 134;

    public function sections(): BelongsToMany
    {
        return $this->belongsToMany(Section::class, 'organisation_position_section', 'position_id', 'section_id');
    }

    public function pivotSections(): HasMany
    {
        return $this->hasMany(PositionSection::class, 'position_id');
    }

    public function departments(): BelongsToMany
    {
        return $this->belongsToMany(Department::class, 'organisation_department_position', 'position_id', 'department_id');
    }

    public function pivotDepartments(): HasMany
    {
        return $this->hasMany(DepartmentPosition::class, 'position_id');
    }

    public function parents(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'organisation_position_hierarchy', 'position_id', 'parent_id')
            ->withPivot(['is_pay_approver', 'is_centre_pay_approver', 'is_pay_viewer', 'hierarchy_order'])
            ->orderBy('pivot_hierarchy_order');
    }

    public function children(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'organisation_position_hierarchy', 'parent_id', 'position_id')
            ->withPivot(['is_pay_approver', 'is_centre_pay_approver', 'is_pay_viewer', 'hierarchy_order'])
            ->orderBy('pivot_hierarchy_order');
    }

    public function validAssigments(): HasMany
    {
        return $this->hasMany(Assigment::class, 'position_id');
    }

    public function excludedForBudgets(): HasMany
    {
        return $this->hasMany(ProposalSubTypeBudgetExcludedPosition::class, 'position_id');
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
}
