<?php

namespace App\Models\Organisation;

use App\Traits\Models\GetTableNameTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Section extends Model
{
    use GetTableNameTrait;

    protected $table = 'organisation_sections';
    protected $guarded = [];

    public function departments(): BelongsToMany
    {
        return $this->belongsToMany(Department::class, 'organisation_department_section', 'section_id', 'department_id');
    }
}
