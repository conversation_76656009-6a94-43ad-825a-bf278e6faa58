<?php

namespace App\Models\Remuneration;

use App\Models\Base\Currency;
use App\Models\Person\Contract;
use App\Models\Person\User;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\HasEncyptedAttributeTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Salary extends Model
{
    use GetTableNameTrait, HasEncyptedAttributeTrait;

    protected $table = 'remuneration_salaries';
    protected $guarded = [];

    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'contract_id');
    }

    public function creator(): BelongsT<PERSON>
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id');
    }

    protected function base(): Attribute
    {
        return $this->encryptedAttribute('number');
    }

    protected function addition(): Attribute
    {
        return $this->encryptedAttribute('number');
    }
}
