<?php

namespace App\Models\Remuneration;

use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalSubType;
use App\Models\Base\Currency;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Department;
use App\Models\Person\User;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\HasEncyptedAttributeTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProposalLog extends Model
{
    use GetTableNameTrait, HasEncyptedAttributeTrait;

    protected $table = 'remuneration_proposal_logs';

    protected $guarded = [];
    protected $casts
        = [
            'changes' => 'array',
            'status' => ProposalStatus::class,
        ];
    protected array $encryptedJsonAttributes
        = [
            'base' => 'number',
            'addition' => 'number',
            'reason' => 'text',
        ];

    protected array $booleanJsonAttributes
        = [
            'is_paid_via_another_way',
            'is_paid_via_agreement',
        ];

    protected array $relationJsonAttributes
        = [
            'approver_id' => ['class' => User::class, 'attributeToShow' => 'full_name'],
            'proposer_id' => ['class' => User::class, 'attributeToShow' => 'full_name'],
            'centre_id' => ['class' => Centre::class, 'attributeToShow' => 'code'],
            'currency_id' => ['class' => Currency::class, 'attributeToShow' => 'code'],
            'department_id' => ['class' => Department::class, 'attributeToShow' => 'code'],
        ];

    protected array $enumJsonAttributes
        = [
            'payment_status' => ProposalPaymentStatus::class,
            'subtype' => ProposalSubType::class,
        ];

    public function proposal(): BelongsTo
    {
        return $this->belongsTo(Proposal::class, 'proposal_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    protected static array $relationCache = [];

    public function getChangesAttribute($value)
    {
        $original = json_decode($value, true) ?? [];

        foreach ($original as $name => $originalValue) {
            if (array_key_exists($name, $this->encryptedJsonAttributes)) {
                $original[$name] = $this->decryptEncryptedValue($this->encryptedJsonAttributes[$name], $originalValue);
            }

            if (in_array($name, $this->booleanJsonAttributes)) {
                $original[$name] = mb_ucfirst($originalValue ? __('main.yes') : __('main.no'));
            }

            if (array_key_exists($name, $this->relationJsonAttributes)) {
                $relationData = $this->relationJsonAttributes[$name];
                $class = $relationData['class'];
                $attribute = $relationData['attributeToShow'];

                // Init cache if not exists
                if (!isset(self::$relationCache[$class])) {
                    self::$relationCache[$class] = $class::get()->keyBy('id');
                }
                // Fetch from cache
                $original[$name] = self::$relationCache[$class][$originalValue][$attribute] ?? null;
            }

            if (array_key_exists($name, $this->enumJsonAttributes)) {
                $enum = $this->enumJsonAttributes[$name]::tryFrom($originalValue);
                $original[$name] = $enum?->translation() ?? null;
            }
        }

        return $original;
    }
}
