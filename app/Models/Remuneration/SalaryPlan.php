<?php

namespace App\Models\Remuneration;

use App\Enums\Remuneration\SalaryPlanStatus;
use App\Models\Base\Currency;
use App\Models\Person\User;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\HasEncyptedAttributeTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class SalaryPlan extends Model
{
    use GetTableNameTrait, HasEncyptedAttributeTrait;

    protected $table = 'remuneration_salary_plans';
    protected $guarded = [];

    protected $casts = [
        'status' => SalaryPlanStatus::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id');
    }

    protected function base(): Attribute
    {
        return $this->encryptedAttribute('number');
    }

    protected function addition(): Attribute
    {
        return $this->encryptedAttribute('number');
    }

    protected function note(): Attribute
    {
        return $this->encryptedAttribute('note');
    }
}
