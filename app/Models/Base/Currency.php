<?php

namespace App\Models\Base;

use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\IsFilterableTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Currency extends Model
{
    use GetTableNameTrait, IsFilterableTrait;

    protected $table = 'base_currencies';
    protected $guarded = [];

    public const CODE_CZK = 'CZK';
    public const CODE_EUR = 'EUR';
    public const CODE_HUF = 'HUF';

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
}
