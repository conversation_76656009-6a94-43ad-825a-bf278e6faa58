<?php

namespace App\Models\Person;

use App\Enums\Person\Contract\ContractStatusEnum;
use App\Enums\Person\Contract\ContractTypeEnum;
use App\Enums\Remuneration\ProposalStatus;
use App\Models\Base\Country;
use App\Models\Organisation\Company;
use App\Models\RaP\Permission;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\Salary;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\IsFilterableTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Contract extends Model
{
    use GetTableNameTrait, IsFilterableTrait;

    protected $table = 'person_contracts';

    protected $guarded = [];

    protected $casts = [
        'type' => ContractTypeEnum::class,
        'status' => ContractStatusEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function salaries(): HasMany
    {
        return $this->hasMany(Salary::class, 'contract_id')->orderBy('validity', 'desc')->orderBy('id', 'desc');
    }

    public function currentSalary()
    {
        return $this->hasOne(Salary::class, 'contract_id')->where('validity', '<=', date('Y-m-d'))->orderBy('validity', 'desc')->orderBy('id', 'desc');
    }

    public function remunerationProposals(): HasMany
    {
        return $this->hasMany(Proposal::class, 'contract_id');
    }

    public function scopeFromAllowedCountries(Builder $query): Builder
    {
        $allowedCountries = [];

        if(auth()->user()->can(Permission::HR_ADMINISTRATION_COUNTRY_CZ)){
            $allowedCountries[] = Country::ID_CZECHIA;
        }
        if(auth()->user()->can(Permission::HR_ADMINISTRATION_COUNTRY_SK)){
            $allowedCountries[] = Country::ID_SLOVAKIA;
        }
        if(auth()->user()->can(Permission::HR_ADMINISTRATION_COUNTRY_HU)){
            $allowedCountries[] = Country::ID_HUNGARY;
        }

        return $query->whereHas('company', function($q) use($allowedCountries) {
            $q->whereHas('address', function($q) use($allowedCountries) {
                $q->whereIn('country_id', $allowedCountries);
            });
        });
    }
}
