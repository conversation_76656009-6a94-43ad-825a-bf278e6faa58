<?php

namespace App\Models\Person;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserFilterBookmark extends Model
{
    protected $table = 'person_user_filter_bookmarks';
    protected $guarded
        = [
        ];

    public $timestamps = false;
    protected $appends = ['url', 'deleteUrl'];

    protected $casts
        = [
            'settings' => 'array',
        ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getUrlAttribute(): string
    {
        $httpQuery = ['filter' => (array)json_decode($this->getAttribute('settings'), true)];
        return url()->current().'?'.httpBuildQueryWithEmptyValuesPreserved($httpQuery);
    }

    public function getDeleteUrlAttribute(): string
    {
        return route('global.filter.delete-filter-bookmark', $this);
    }

}
