<?php

namespace App\Models\Person;

use App\Traits\Models\GetTableNameTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAuthCode extends Model
{
    use GetTableNameTrait;

    protected $table = 'person_user_auth_codes';
    protected $guarded = [];

    protected $casts = [
        'expired_at' => 'date',
        'validated_at' => 'date',
    ];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
