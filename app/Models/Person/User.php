<?php

namespace App\Models\Person;

use App\Enums\Person\Contract\ContractStatusEnum;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\ProposalAdditionalApprover;
use App\Traits\Models\GetTableNameTrait;
use App\Traits\Models\IsFilterableTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, HasRoles, HasPermissions, GetTableNameTrait, IsFilterableTrait, HasApiTokens;

    protected $table = 'person_users';
    protected $guarded = [];

    protected $hidden
        = [
            'password',
            'remember_token',
        ];

    protected $appends = ['full_name'];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
        ];
    }

    public function username(): string
    {
        return 'login';
    }

    public function fullName(): Attribute
    {
        return new Attribute(
            get: fn() => $this->last_name.' '.$this->first_name
        );
    }

    #[Scope]
    public function scopeHasValidContractForDate(Builder $builder, string $dateForCheck): Builder
    {
        try {
            $date = Carbon::parse($dateForCheck)->toDateString();
        } catch (\Exception $e) {
            // return null when date format is not valid
            return $builder->whereRaw('0 = 1');
        }
        return $builder->whereHas('contracts', function ($query) use ($date) {
            $query->where('start_date', '<=', $date)->where(function (Builder $query) use ($date) {
                $query->where('end_date', '>=', $date)->orWhereNull('end_date');
            });
        });
    }

    public function secondStepSmsCodes(): HasMany
    {
        return $this->hasMany(UserAuthCode::class);
    }

    public function availableSecondStepSmsCodes(): HasMany
    {
        return $this->hasMany(UserAuthCode::class)
            ->where('expired_at', '>=', now())
            ->whereNull('validated_at');
    }

    public function validSecondStepSmsCodes(): HasMany
    {
        return $this->hasMany(UserAuthCode::class)
            ->where('session_id', session()->getId())
            ->whereNotNull('validated_at');
    }

    public function workData(): HasOne
    {
        return $this->hasOne(UserWorkData::class, 'user_id');
    }

    public function personalData(): HasOne
    {
        return $this->hasOne(UserPersonalData::class, 'user_id');
    }

    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'user_id')->orderBy('start_date', 'desc');
    }

    public function activeContracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'user_id')->where('status', ContractStatusEnum::VALID);
    }

    public function activeOrFutureContracts(): HasMany
    {
        return $this->hasMany(Contract::class, 'user_id')
            ->whereIn('status', [ContractStatusEnum::VALID->value, ContractStatusEnum::FUTURE->value]);
    }

    public function assigments(): HasMany
    {
        return $this->hasMany(Assigment::class, 'user_id')->orderBy('type')->orderBy('is_primary', 'desc')->orderBy('valid_from_date', 'desc');
    }

    public function validAssigments(): HasMany
    {
        return $this->assigments()->valid();
    }

    public function validAssigmentsForProposals(): HasMany
    {
        return $this->assigments()->valid(date('Y-m-01', strtotime('last month')));
    }

    public function filterBookmarks(): HasMany
    {
        return $this->hasMany(UserFilterBookmark::class, 'user_id');
    }

    public function remunerationProposals(): HasMany
    {
        return $this->hasMany(Proposal::class, 'user_id');
    }

    public function approverRemunerationProposals(): HasMany
    {
        return $this->hasMany(Proposal::class, 'approver_id');
    }

    public function remunerationProposalAdditionalApprovers(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'remuneration_proposal_additional_approvers', 'user_id', 'approver_id');
    }

    public function remunerationProposalAdditionalProposers(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'remuneration_proposal_additional_approvers', 'approver_id', 'user_id');
    }

    public function remunerationProposalAdditionalApproversPivot(): HasMany
    {
        return $this->hasMany(ProposalAdditionalApprover::class, 'user_id');
    }

    public function remunerationProposalAdditionalProposersPivot(): HasMany
    {
        return $this->hasMany(ProposalAdditionalApprover::class, 'approver_id');
    }
}
