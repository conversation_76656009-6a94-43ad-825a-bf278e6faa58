<?php

namespace App\Models\Person;

use App\Enums\Person\Assigment\AssigmentStatusEnum;
use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Models\Base\Country;
use App\Models\Organisation\Centre;
use App\Models\Organisation\Position;
use App\Models\RaP\Permission;
use App\Traits\Models\GetTableNameTrait;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Assigment extends Model
{
    use GetTableNameTrait;

    protected $table = 'person_assigments';

    protected $guarded = [];

    protected $casts = [
        'type' => AssigmentTypeEnum::class,
        'status' => AssigmentStatusEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class, 'position_id');
    }

    public function centre(): BelongsTo
    {
        return $this->belongsTo(Centre::class, 'centre_id');
    }

    #[Scope]
    public function valid(Builder $query, ?string $forDate = null): Builder
    {
        $todayDate = is_null($forDate) ? date('Y-m-d') : $forDate;

        return $query->where(function($query) use ($todayDate) {
            return $query->whereIn('type', [AssigmentTypeEnum::FUTURE_EMPLOYEE, AssigmentTypeEnum::EMPLOYEE])
                ->where(function($query) use ($todayDate) {
                    $query->whereNull('valid_from_date')
                        ->orWhere('valid_from_date', '<=', $todayDate);
                })
                ->where(function($query) use ($todayDate) {
                    $query->whereNull('valid_to_date')
                        ->orWhere('valid_to_date', '>=', $todayDate);
                });
        })->orWhere(function($query) use ($todayDate) {
            return $query->whereIn('type', [AssigmentTypeEnum::FINISHED])
                ->where('valid_to_date', '>=', $todayDate);
        });
    }

    #[Scope]
    public function primary(Builder $query): Builder
    {
        return $query->where('is_primary', true);
    }

    #[Scope]
    public function fromAllowedCountries(Builder $query): Builder
    {
        $allowedCountries = [];

        if(auth()->user()->can(Permission::HR_ADMINISTRATION_COUNTRY_CZ)){
            $allowedCountries[] = Country::ID_CZECHIA;
        }
        if(auth()->user()->can(Permission::HR_ADMINISTRATION_COUNTRY_SK)){
            $allowedCountries[] = Country::ID_SLOVAKIA;
        }
        if(auth()->user()->can(Permission::HR_ADMINISTRATION_COUNTRY_HU)){
            $allowedCountries[] = Country::ID_HUNGARY;
        }

        return $query->whereIn('country_id', $allowedCountries);
    }

    #[Scope]
    public function forRemunerationProposals(Builder $query): Builder
    {
        $todayDate = date('Y-m-d');

        return $query->whereNotIn('status', [AssigmentStatusEnum::MATERNITY->value, AssigmentStatusEnum::PN->value])
            ->whereNotIn('position_id', [Position::ID_BRIG, Position::ID_VerBrig])
            ->where('type', AssigmentTypeEnum::EMPLOYEE->value)
            ->where(function($query) use ($todayDate) {
                $query->whereNull('valid_from_date')
                    ->orWhere('valid_from_date', '<=', $todayDate);
            })
            ->where(function($query) use ($todayDate) {
                $query->whereNull('valid_to_date')
                    ->orWhere('valid_to_date', '>=', $todayDate);
            });
    }

    #[Scope]
    public function forRemunerationSalaries(Builder $query): Builder
    {
        $todayDate = date('Y-m-d');

        return $query->where('type', AssigmentTypeEnum::EMPLOYEE->value)
            ->where(function($query) use ($todayDate) {
                $query->whereNull('valid_from_date')
                    ->orWhere('valid_from_date', '<=', $todayDate);
            })
            ->where(function($query) use ($todayDate) {
                $query->whereNull('valid_to_date')
                    ->orWhere('valid_to_date', '>=', $todayDate);
            });
    }

    #[Scope]
    public function withoutMaternityAndPN(Builder $query): Builder
    {
        return $query->whereNotIn('status', [AssigmentStatusEnum::MATERNITY->value, AssigmentStatusEnum::PN->value]);
    }
}
