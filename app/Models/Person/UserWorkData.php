<?php

namespace App\Models\Person;

use App\Traits\Models\GetTableNameTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserWorkData extends Model
{
    use GetTableNameTrait;

    protected $table = 'person_user_work_datas';

    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
