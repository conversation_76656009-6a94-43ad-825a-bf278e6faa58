<?php

namespace App\Console\Commands\Unused;

use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\ProposalSubTypeBudget;
use App\Models\Remuneration\Salary;
use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RecryptCommand extends Command
{
    protected $signature = 'app:recrypt-command';

    protected $description = 'Command description';

    public function handle()
    {
        die;

        if(1){
            $this->addVersion();
        }

        $this->processProposals();
        $this->processSalaries();
        $this->processBudgets();

        if(1){
            $this->removeVersion();
        }
    }

    private function addVersion()
    {
        Schema::table('remuneration_proposals', function (Blueprint $table) {
            $table->unsignedTinyInteger('crypt_version')->default(1)->after('id');
        });

        Schema::table('remuneration_salaries', function (Blueprint $table) {
            $table->unsignedTinyInteger('crypt_version')->default(1)->after('id');
        });

        Schema::table('remuneration_proposal_subtype_budgets', function (Blueprint $table) {
            $table->unsignedTinyInteger('crypt_version')->default(1)->after('id');
        });

        $this->info('version column added');
    }

    private function removeVersion()
    {
        Schema::table('remuneration_proposals', function (Blueprint $table) {
            $table->dropColumn('crypt_version');
        });

        Schema::table('remuneration_salaries', function (Blueprint $table) {
            $table->dropColumn('crypt_version');
        });

        Schema::table('remuneration_proposal_subtype_budgets', function (Blueprint $table) {
            $table->dropColumn('crypt_version');
        });

        $this->info('version column removed');
    }

    private function processProposals()
    {
        Proposal::where('crypt_version', 1)
            ->chunkById(100, function ($proposals) {
                foreach($proposals as $proposal) {
                    $proposal->update([
                        'crypt_version' => 2,
                        'base' => $proposal->base,
                        'addition' => $proposal->addition,
                        'reason' => $proposal->reason,
                    ]);
                }
            });
    }

    private function processSalaries()
    {
        Salary::where('crypt_version', 1)
            ->chunkById(100, function ($salaries) {
                foreach($salaries as $salary) {
                    $salary->update([
                        'crypt_version' => 2,
                        'base' => $salary->base,
                        'addition' => $salary->addition,
                    ]);
                }
            });
    }

    private function processBudgets()
    {
        ProposalSubTypeBudget::where('crypt_version', 1)
            ->chunkById(100, function ($budgets) {
                foreach($budgets as $budget) {
                    $budget->update([
                        'crypt_version' => 2,
                        'budget' => $budget->budget,
                    ]);
                }
            });
    }
}
