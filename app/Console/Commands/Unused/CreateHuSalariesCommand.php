<?php

namespace App\Console\Commands\Unused;

use App\Models\Base\Currency;
use App\Models\Person\Contract;
use App\Models\Person\User;
use App\Models\Remuneration\Salary;
use Illuminate\Console\Command;

class CreateHuSalariesCommand extends Command
{
    protected $signature = 'app:create-hu-salaries-command';

    protected $description = 'Command description';

    public function handle()
    {
        $salaryRecords = [
        ];

        $creator = User::where('login', 'u1392')->first();
        $currency = Currency::where('code', Currency::CODE_HUF)->first();

        foreach($salaryRecords as $record) {
            $contract = Contract::where('hrms_id', $record['hrms_id'])->first();

            if(!$contract) {
                $this->info('No contract by HRMS ID: '.$record['hrms_id']);
                continue;
            }

            Salary::create([
                'contract_id' => $contract->id,
                'validity' => $record['validity'],
                'base' => $record['base'],
                'addition' => null,
                'currency_id' => $currency->id,
                'creator_id' => $creator->id,
            ]);
        }
    }
}
