<?php

namespace App\Console\Commands\Unused;

use App\Enums\Remuneration\ProposalStatus;
use App\Models\Organisation\Department;
use App\Models\Person\Assigment;
use App\Models\Person\User;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\ProposalLog;
use Illuminate\Console\Command;

class TempCommand extends Command
{
    protected $signature = 'app:temp-command';

    protected $description = 'Command description';

    public function handle()
    {
        $array = [

        ];

        foreach($array as $item) {
            $userId = $item['user_id'];

            $assigment = Assigment::valid()
                ->where('user_id', $userId)
                ->with('centre', 'centre.departments', 'position', 'position.departments')
                ->first();

            $item['centre_id'] = $assigment->centre->id;

            if($assigment->centre->departments->count() === 1) {
                $item['department_id'] = $assigment->centre->departments->first()->id;
            } else {
                if($assigment->position->departments->count() === 1) {
                    $item['department_id'] = $assigment->position->departments->first()->id;
                } else {
                    $item['department_id'] = Department::where('code', 'OFFICE-SK')->first()->id;
                }
            }

            $forwardedArray = $item['forwardedArray'];
            unset($item['forwardedArray']);
            $proposal = Proposal::create($item);

            foreach($forwardedArray as $fa) {
                ProposalLog::create([
                    'proposal_id' => $proposal->id,
                    'user_id' => User::where('login', $fa)->first()->id,
                    'status' => ProposalStatus::FORWARDED->value,
                    'created_at' => $item['updated_at'],
                    'updated_at' => $item['updated_at'],
                ]);
            }
        }
    }
}
