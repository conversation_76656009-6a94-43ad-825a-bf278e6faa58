<?php

namespace App\Console\Commands\HR\Remuneration;

use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalType;
use App\Mail\HR\Remuneration\ApprovedProposalsMail;
use App\Models\Base\Country;
use App\Models\Person\User;
use App\Models\RaP\Permission;
use App\Models\Remuneration\Proposal;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ApprovedProposalsNotificationCommand extends Command
{
    protected $signature = 'approved-proposals:notification {--without-mail}';

    protected $description = 'Command description';

    public function handle()
    {
        $countryInfoResult = '';

        $proposals = Proposal::select('id', 'centre_id')
            ->where('status', ProposalStatus::APPROVED)
            ->where('payment_status', ProposalPaymentStatus::TO_BE_NOTIFIED)
            ->where('type', '!=', ProposalType::NON_FINANCIAL_REWARD)
            ->where('is_paid_via_another_way', false)
            ->with('centre:id,address_id', 'centre.address:id,country_id')
            ->get();

        if(!$proposals->count()) {
            $this->warn('No Approved Proposals with payment status to be notified');
            Log::channel('scheduler')->info(__CLASS__.' - No Approved Proposals with payment status to be notified');
            return;
        }

        if($this->option('without-mail')) {
            $this->warn('Running without sending notification emails');
            goto WITHOUT_MAIL;
        }

        $countries = Country::get();
        $permissionClass = new \ReflectionClass(Permission::class);

        foreach($countries as $country) {
            $countryInfoResult .= $country->code.': ';

            $permissionName = 'HR_ADMINISTRATION_COUNTRY_'.$country->code;
            $payrolls = User::select('id', 'first_name', 'last_name', 'login')
                ->with('workData:user_id,email')
                ->permission(Permission::HR_ADMINISTRATION_CAN_VIEW_SALARIES)
                ->permission($permissionClass->getConstant($permissionName))
                ->get();

            if(!$payrolls->count()) {
                $countryInfoResult .= 'no payroll person;';
                $this->warn($country->code.' - no payroll person');
                continue;
            }

            $count = $proposals->where('centre.address.country_id', $country->id)->count();

            if(!$count) {
                $countryInfoResult .= 'no proposals for this country;';
                $this->warn($country->code.' - no proposals for this country');
                continue;
            }

            Mail::to($payrolls->pluck('workData.email')->toArray())
                ->locale($country->code === 'CZ' ? 'cs' : strtolower($country->code))
                ->send(new ApprovedProposalsMail($count));

            $countryInfoResult .= 'sent info about '.$count.' proposals;';
            $this->info($country->code.' - sent info about '.$count.' proposals;');
        }

        WITHOUT_MAIL:
        Proposal::whereIn('id', $proposals->pluck('id'))->update(['payment_status' => ProposalPaymentStatus::NOTIFIED]);

        $paymentStatusResult = 'Payment status "notified" set for '.$proposals->count().' proposals';
        $this->info($paymentStatusResult);

        Log::channel('scheduler')->info(__CLASS__.' - '.$countryInfoResult.' '.$paymentStatusResult);
    }
}
