<?php

namespace App\Console\Commands\HR\Remuneration;

use App\Enums\Person\Contract\ContractTypeEnum;
use App\Enums\Remuneration\ProposalContractStatus;
use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalType;
use App\Models\Person\Contract;
use App\Models\Remuneration\Proposal;
use App\Models\Remuneration\Salary;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ApprovedProposalContractAssignmentCommand extends Command
{
    protected $signature = 'remuneration-proposals:approved-contract-assignment';

    protected $description = 'Try to assign contract ID for approved remuneration proposals';

    public function handle()
    {
        $proposalsCount = Proposal::query()
            ->where('status', ProposalStatus::APPROVED)
            ->where('payment_status', '!=', ProposalPaymentStatus::WITHOUT_NOTIFICATION)
            ->where('contract_status', ProposalContractStatus::TO_BE_ASSIGNED)
            ->where('updated_at', '<', now()->subHours(2)->toDateTimeString())
            ->count();

        $this->info('Proposals without assigned contract_id: '.$proposalsCount);
        Log::channel('scheduler')->info(__CLASS__.' - Proposals without assigned contract_id: '.$proposalsCount);

        if(!$proposalsCount) {
            return;
        }

        $statusesCounts = [
            'yes' => 0,
            'no' => 0,
        ];

        Proposal::query()
            ->where('status', ProposalStatus::APPROVED)
            ->where('payment_status', '!=', ProposalPaymentStatus::WITHOUT_NOTIFICATION)
            ->where('contract_status', ProposalContractStatus::TO_BE_ASSIGNED)
            ->where('updated_at', '<', now()->subHours(2)->toDateTimeString())
            ->chunkById(50, function ($proposals) use (&$statusesCounts) {
                $minValidity = $proposals->min('validity');
                $maxValidity = $proposals->max('validity');

                $proposals->loadMissing([
                    'user',
                    'user.contracts' => function($query) use ($minValidity, $maxValidity) {
                        $query->where(function ($query) use ($maxValidity) {
                            $query->where('start_date', '<=', $maxValidity)
                                ->orWhereNull('start_date');
                        });
                        $query->where(function ($query) use ($minValidity) {
                            $query->where('end_date', '>=', $minValidity)
                                ->orWhereNull('end_date');
                        });
                    },
                    'user.contracts.company.address.country',
                ]);

                foreach($proposals as $proposal) {
                    $statusesCounts[$this->tryAssignContractId($proposal) ? 'yes' : 'no']++;
                }
            });

        $this->table(['Assigned', 'Not Assigned'], [$statusesCounts]);

        Log::channel('scheduler')->info(__CLASS__.' - Assigned: '.$statusesCounts['yes'].' / Not Assigned: '.$statusesCounts['no']);
    }

    private function tryAssignContractId(Proposal $proposal): bool
    {
        $contract = $proposal->is_paid_via_agreement ? $this->tryToFindAgremment($proposal) : $this->tryToFindContract($proposal);

        if(is_null($contract)) {
            $proposal->update(['contract_status' => ProposalContractStatus::ASSIGNMENT_UNSUCCESSFUL]);
            return false;
        }

        $proposal->update(['contract_status' => ProposalContractStatus::ASSIGNED, 'contract_id' => $contract->id]);

        if($proposal->type !== ProposalType::SALARY) {
            return true;
        }

        Salary::create([
            'contract_id' => $contract->id,
            'validity' => $proposal->validity,
            'base' => $proposal->base,
            'addition' => $proposal->addition,
            'currency_id' => $proposal->currency_id,
            'creator_id' => $proposal->approver_id,
        ]);

        return true;
    }

    private function tryToFindAgremment(Proposal $proposal): ?Contract
    {
        $contracts = $proposal->user->contracts->whereNotIn('type', [ContractTypeEnum::TPP->value, ContractTypeEnum::EXT->value])->where('is_primary', false);

        if($contracts->isEmpty()) {
            return null;
        }

        if($contracts->count() === 1) {
            $contract = $contracts->first();

            $currencyId = $contract->company->address->country->currency_id ?? null;

            return ($currencyId === $proposal->currency_id) ? $contract : null;
        }

        $contracts = $contracts->filter(function ($contract) use ($proposal) {
            return $contract->company->address->country->currency_id === $proposal->currency_id;
        });

        if($contracts->count() !== 1) {
            return null;
        }

        return $contracts->first();
    }

    private function tryToFindContract(Proposal $proposal): ?Contract
    {
        $contracts = $proposal->user->contracts->whereNotIn('type', [ContractTypeEnum::EXT->value])->where('is_primary', true);

        if($contracts->isEmpty()) {
            return null;
        }

        if($contracts->count() === 1) {
            $contract = $contracts->first();

            $currencyId = $contract->company->address->country->currency_id ?? null;

            return ($currencyId === $proposal->currency_id) ? $contract : null;
        }

        return null;
    }
}
