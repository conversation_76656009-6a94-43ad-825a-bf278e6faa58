<?php

namespace App\Console\Commands\Employee\Remuneration;

use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalType;
use App\Enums\Remuneration\SalaryPlanStatus;
use App\Mail\RawMail;
use App\Models\Organisation\CentreDepartment;
use App\Models\Organisation\DepartmentPosition;
use App\Models\Person\Assigment;
use App\Models\Person\User;
use App\Models\Remuneration\SalaryPlan;
use App\Services\Employee\Remuneration\Proposal\CreateProposalService;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class CreateSalaryProposalsFromPlansCommand extends Command
{
    use PermissionTrait;

    protected $signature = 'remuneration-proposals:create-from-plans {--month=}';

    protected $description = 'Creating of proposals from salary plans';

    public function handle()
    {
        $month = date('Y-m-01', strtotime($this->option('month') ?? '+ 1 month'));

        if (!($plansCount = SalaryPlan::where('validity', $month)->where('status', SalaryPlanStatus::PLANNED->value)->count())) {
            $resultText = 'No planned salary changes for month ' . $month;
            Log::channel('scheduler')->info(__CLASS__.' - '.$resultText);
            $this->warn($resultText);
            return;
        }

        $resultText = 'Nr. of plans for month ' . $month.' is '.$plansCount;
        Log::channel('scheduler')->info(__CLASS__.' - '.$resultText);
        $this->info($resultText);

        $createProposalService = new CreateProposalService();

        SalaryPlan::with([
                'user',
                'user.validAssigments',
                'user.validAssigments.position',
                'user.validAssigments.position.pivotDepartments',
                'user.validAssigments.position.parents',
            ])
            ->where('validity', $month)
            ->where('status', SalaryPlanStatus::PLANNED->value)
            ->chunkById(10, function ($salaryPlansChunk) use ($createProposalService) {
                foreach ($salaryPlansChunk as $salaryPlan) {
                    $userParentAssigments = $this->getValidAssigmentsWithParents(currentUser: $salaryPlan->user);
                    $selectedApproverId = $createProposalService->getUserSalaryPlanApproverId($salaryPlan->user, $userParentAssigments, $salaryPlan->creator_id);

                    if(is_null($selectedApproverId)) {
                        $resultText = 'No Approver for plan with ID '.$salaryPlan->id;
                        Mail::to(config('vermont.developer_notification_emails'))
                            ->send(new RawMail(
                                __CLASS__.' - '.$resultText,
                            ));
                        $this->warn($resultText);
                        continue;
                    }

                    $firstAssigment = $salaryPlan->user->validAssigments->whereNotNull('centre_id')->first();
                    $centreId = $firstAssigment?->centre_id;
                    $positionId = $firstAssigment->position_id;

                    $approver = User::find($selectedApproverId);

                    $centreDepartments = CentreDepartment::where('centre_id', $centreId)->pluck('department_id');
                    $positionDepartments = DepartmentPosition::where('position_id', $positionId)->pluck('department_id');

                    $departments = $centreDepartments->intersect($positionDepartments);
                    $departmentId = $departments?->first();

                    $proposalData = [
                        'users' => [$salaryPlan->user_id],
                        'status' => ProposalStatus::PENDING->value,
                        'type' => ProposalType::SALARY->value,
                        'base' => $salaryPlan->base,
                        'addition' => $salaryPlan->addition,
                        'currency_id' => $salaryPlan->currency_id,
                        'reason' => $salaryPlan->note.' ('.__('employee.remunerations.salaries.plans.created_via_automat', [], $approver->lang ?? 'sk').')',
                        'validity' => $salaryPlan->validity,
                        'proposer_id' => $selectedApproverId,
                        'approver_id' => $selectedApproverId,
                        'centre_id' => $centreId,
                        'department_id' => $departmentId,
                    ];

                    $returnFromService = $createProposalService->createMultipleProposals($proposalData, returnCreatedIdForOne: true);
                    if(!is_int($returnFromService)) {
                        $resultText = 'Error in creating proposal for plan with ID '.$salaryPlan->id;
                        Mail::to(config('vermont.developer_notification_emails'))
                            ->send(new RawMail(
                                __CLASS__.' - '.$resultText,
                                $returnFromService,
                            ));
                        $this->warn($resultText);
                        continue;
                    }

                    $salaryPlan->update([
                        'status' => SalaryPlanStatus::CREATED,
                        'proposal_id' => $returnFromService,
                    ]);

                    $resultText = 'Plan id: ' . $salaryPlan->id.' - proposal created '.$returnFromService;
                    Log::channel('scheduler')->info(__CLASS__.' - '.$resultText);
                }
        });
    }
}
