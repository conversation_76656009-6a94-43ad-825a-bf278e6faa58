<?php

namespace App\Console\Commands\Local;

use App\Console\Commands\DataSync\CdbDataSyncCommand;
use App\Console\Commands\DataSync\Hrms1DataSyncCommand;
use App\Models\Person\User;
use App\Models\RaP\Permission;
use App\Models\RaP\Role;
use Database\Seeders\Hrms2InitSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class InitializeHrms2Command extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hrms2:init';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Basic initilization of project';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        if(!app()->isLocal()) {
            if(!$this->confirm('Are you sure you want to wipe NOT LOCAL database?')) {
                $this->error('Stopping init!');
                return;
            }
        }

        $this->info('Wiping database');

        Schema::disableForeignKeyConstraints();

        Artisan::call('db:wipe', ['--force' => true]);

        Schema::enableForeignKeyConstraints();

        $this->info('Migrating database');
        Artisan::call('migrate', ['--force' => true]);

        $this->info('Loading data from external services');
        $this->call(CdbDataSyncCommand::class); //'cdb:data-sync'
        $this->call(Hrms1DataSyncCommand::class); //'hrms1:data-sync'

        $this->info('Seeding data');
        Artisan::call('db:seed', ['--force' => true, '--class' => Hrms2InitSeeder::class]);

        $this->createAdminUser();
    }

    private function createAdminUser(): void
    {
        $this->info('Creating admin user');
        $login = strtolower(trim($this->ask('Login (uXXXX) for admin user?', 'u1182')));

        $adminUser = User::where('login', $login)->first() ?? User::factory()->create(['login' => $login]);

        $adminUser->assignRole(Role::all());
        $adminUser->givePermissionTo(Permission::IT_CAN_ASSIGN_ROLES);
        $adminUser->givePermissionTo(Permission::DEVELOPER_CAN_ACCEESS_LOGVIEWER);
        $this->info('Admin user "'.$login.'" successfully created, you can log in.');
    }
}
