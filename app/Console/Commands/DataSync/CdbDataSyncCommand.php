<?php

namespace App\Console\Commands\DataSync;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CdbDataSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cdb:data-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle():void
    {
        $ser = new \App\Services\Vermont\CdbService();

        if(!$ser->check()) {
            $result = 'Connection to CDB not successful, check ENV key for CDB!';

            $this->error($result);
            Log::channel('scheduler')->error(__CLASS__.' - '.$result);
            return;
        }

        $this->line('Start of CDB sync');
        $this->line($ser->updateCurrencies());
        $this->line($ser->updateCountries());
        $this->line($ser->updateCities());
        $this->line($ser->updateAddresses());
        $this->line($ser->updateCompanies());
        $this->line($ser->updateCentreTypes());
        $this->line($ser->updateCentres());
        $this->line($ser->updateDepartments());
        $this->line('End of CDB sync');

        Log::channel('scheduler')->info(__CLASS__.' - Cdb sync finished');
    }
}
