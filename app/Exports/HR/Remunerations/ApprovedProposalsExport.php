<?php

namespace App\Exports\HR\Remunerations;

use App\Enums\Remuneration\ProposalPaymentStatus;
use App\Enums\Remuneration\ProposalStatus;
use App\Enums\Remuneration\ProposalType;
use App\Models\Organisation\CentreDepartment;
use App\Models\Organisation\DepartmentPosition;
use App\Models\Person\User;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ApprovedProposalsExport implements FromView, ShouldAutoSize, WithStyles, WithColumnWidths
{
    public function __construct(public string|null $month, public array $filterData){}

    private function getData(): Collection
    {
        $month = CarbonImmutable::parse((empty($this->month) ? strtotime(date('j') < 20 ? '-1 month' : 'this month') : $this->month))->startOfMonth();

        $fullNameFilter = $this->filterData['filter']['custom']['like']['full_name'] ?? null;
        $assigmentDepartmentFilter = $this->filterData['filter']['custom']['equal']['assigments_department'] ?? null;
        $proposalContractFilter = $this->filterData['filter']['custom']['equal']['proposal_contract'] ?? null;
        $proposalTypeFilter = $this->filterData['filter']['custom']['equal']['proposal_type'] ?? null;

        return User::withWhereHas('assigments', function ($query) {
                $query->fromAllowedCountries()
                    ->with('position:id,code,name,name_hr', 'centre:id,code,name');
            })
            ->withWhereHas('remunerationProposals', function ($query) use ($month, $proposalContractFilter, $proposalTypeFilter) {
                $query
                    ->with('currency', 'contract', 'contract.company')
                    ->where('status', ProposalStatus::APPROVED)
                    ->where('type', '!=', ProposalType::NON_FINANCIAL_REWARD)
                    ->where(function($query) {
                        $query->where('payment_status', '!=', ProposalPaymentStatus::WITHOUT_NOTIFICATION)
                            ->orWhereNull('payment_status');
                    })
                    ->where('validity', $month->toDateString())
                    ->when($proposalContractFilter === 'yes', function ($query) {
                        $query->whereNotNull('contract_id');
                    })
                    ->when($proposalContractFilter === 'no', function ($query) {
                        $query->whereNull('contract_id');
                    })
                    ->when($proposalTypeFilter, function ($query) use ($proposalTypeFilter) {
                        $query->where('type', $proposalTypeFilter);
                    });
            })
            ->with(['contracts' => function($query) use ($month){
                $query->where(function ($query) use ($month) {
                    $query->whereNull('start_date')
                        ->orWhere('start_date', '<=', $month->endOfMonth()->toDateString());
                })->where(function ($query) use ($month) {
                    $query->whereNull('end_date')
                        ->orWhere('end_date', '>=', $month->startOfMonth()->toDateString());
                });
            }, 'contracts.company'])
            ->when(!is_null($fullNameFilter), function ($query) use ($fullNameFilter){
                $query->where(DB::raw('CONCAT(last_name, " ", first_name)'), 'like', '%'.$fullNameFilter.'%')
                    ->orWhere('login', 'like', $fullNameFilter);
            })
            ->when(!is_null($assigmentDepartmentFilter), function ($query) use ($assigmentDepartmentFilter){
                $pd = DepartmentPosition::where('department_id', $assigmentDepartmentFilter)->pluck('position_id');
                $cd = CentreDepartment::where('department_id', $assigmentDepartmentFilter)->pluck('centre_id');
                $query->whereHas('assigments', function($query) use($pd, $cd){
                    $query->whereIn('position_id', $pd)
                        ->whereIn('centre_id', $cd);
                });
            })
            ->filter($this->filterData['filter'])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->get();
    }

    public function view(): View
    {
        return view('hr.remunerations.approved_proposals.export', ['users' => $this->getData()]);
    }

    public function columnWidths(): array
    {
        return [
            'G' => 55,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            'G' => ['alignment' => ['wrapText' => true], 'width' => 25],
        ];
    }
}
