<?php

namespace App\Exports\Employee\Remunerations;

use App\Enums\Remuneration\ProposalStatus;
use App\Models\Remuneration\Proposal;
use App\Traits\Controllers\Employee\Remuneration\PermissionTrait;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProposalsExport implements FromView, ShouldAutoSize, WithStyles, WithColumnWidths
{
    use PermissionTrait;

    public function __construct(public string|null $tab, public array $filterData){}

    private function getData(): Collection
    {
        $tab = $this->tab;

        $validAssigments = $this->getValidAssigmentsWithChildren(true);

        $loggedUser = auth()->user();
        $loggedUser->loadMissing('validAssigments');

        if($validAssigments === null && !auth()->user()->can(Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_CREATOR_WITHOUT_HIERARCHY)) {
            return new Collection();
        }

        $allowedTabs = ['my', 'up', 'down', 'completed'];
        $tab = in_array($tab, $allowedTabs, false) ? $tab : 'my';

        $fullNameSearchValue = $this->filterData['filter']['custom']['like']['full_name'] ?? null;
        $validityFromValue = $this->filterData['filter']['custom']['equal']['validity_from'] ?? null;
        $validityToValue = $this->filterData['filter']['custom']['equal']['validity_to'] ?? null;

        return Proposal::with([
            'currency',
            'user:id,login,first_name,last_name',
            'user.validAssigments.position:id,code,name',
            'user.validAssigments.centre:id,code,name',
            'centre:id,code,name',
            'department:id,code,name',
            'approver:id,login,first_name,last_name',
            'lastLog:id,remuneration_proposal_logs.proposal_id,user_id,note',
        ])
            ->filter($this->filterData['filter'])
            ->when(!is_null($fullNameSearchValue), function ($query) use ($fullNameSearchValue){
                $query->whereHas('user', function ($query) use ($fullNameSearchValue){
                    $query->where(DB::raw('CONCAT(last_name, " ", first_name)'), 'like', '%'.$fullNameSearchValue.'%')
                        ->orWhere('login', 'like', $fullNameSearchValue);
                });
            })
            ->when(!is_null($validityFromValue), function ($query) use ($validityFromValue){
                $query->where('validity', '>=', $validityFromValue);
            })
            ->when(!is_null($validityToValue), function ($query) use ($validityToValue){
                $query->where('validity', '<=', $validityToValue);
            })
            ->where(function ($query) use ($tab, $validAssigments){
                $showOnlyValue = $tab;
                if(in_array($showOnlyValue, ['my', 'up', 'down'], false)){
                    $query->where('status', ProposalStatus::PENDING->value);

                    if($showOnlyValue === 'up') {
                        $downUsersIds = $validAssigments->pluck('user_id')
                            ->unique()
                            ->filter(fn($userId) => $userId !== auth()->id());

                        $query->where('approver_id', '!=', auth()->id());
                        $query->whereNotIn('approver_id', $downUsersIds);
                        $query->whereHas('logs', function ($query) {
                            $query->where('user_id', auth()->id());
                        });
                    } elseif($showOnlyValue === 'down') {
                        $downUsersIds = $validAssigments->pluck('user_id')
                            ->unique()
                            ->filter(fn($userId) => $userId !== auth()->id());

                        $query->whereIn('approver_id', $downUsersIds);
                    } else {
                        $query->where('approver_id', auth()->id());
                    }
                } else {
                    $query->whereIn('status', [ProposalStatus::APPROVED->value, ProposalStatus::REJECTED->value]);
                    $query->whereHas('logs', function ($query){
                        $query->where('user_id', auth()->id());
                    });
                }
            })
            ->orderBy('id', 'desc')
            ->get();
    }

    public function view(): View
    {
        return view('employee.remunerations.proposals.export', ['proposals' => $this->getData()]);
    }

    public function columnWidths(): array
    {
        return [
            'G' => 55,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            'G' => ['alignment' => ['wrapText' => true], 'width' => 25],
        ];
    }
}
