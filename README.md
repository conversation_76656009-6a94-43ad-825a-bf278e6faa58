<div align="center">
  <h1>HRMS 2</h1>
  <p>Modern Human Resource Management System built with Laravel</p>
</div>

##  Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/hrms2.git
cd hrms2
```

2. Copy the example env file and make the required configuration changes
```bash
cp .env.example .env
```

3. Install PHP dependencies
```bash
composer install
```

4. Generate application key
```bash
php artisan key:generate
```

5. Initialize HRMS2
```bash
php artisan hrms2:init
```

6. Install JavaScript dependencies
```bash
npm install
```

7. Build assets
```bash
npm run build
```

##  Docker Installation

This project includes Docker configuration using Laravel Sail. To get started:

1. Install using Docker
```bash
docker compose up -d
```

2. Run the installation commands inside the container
```bash
docker compose exec laravel.test composer install
docker compose exec laravel.test php artisan key:generate
docker compose exec laravel.test php artisan hrms2:init
docker compose exec laravel.test npm install
docker compose exec laravel.test npm run build
```

## Development

To start the development server:

```bash
php artisan serve
```

For hot-reloading of assets:
```bash
npm run dev
```

