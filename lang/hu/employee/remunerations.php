<?php

return [
    'title' => '<PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON> csökkentése',
    'text_number' => 'Az alattad lévő alkalmazottak aktuális száma: :count',
    'text_1' => 'A "<PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON><PERSON>kkentések" részben módosíthatod a béreket, javasol<PERSON>sz bónuszokat, bónuszcsökkentéseket vagy nem pénzbeli bónuszo<PERSON>. ',
    'text_2' => 'Az egyes javaslatokat kétféleképpen dolgozhatod fel és nyújthatod be:',
    'text_ul_1_li_1' => 'A <strong>táblázatos nézeten</strong> keresztül egy adott részlegre vonatkozóan egyszerre láthatod az összes feldolgozásra váró javaslattípust. Ebben az áttekintésben ezeket a javaslatokat gyorsan és tömegesen továbbíthatod a felettesednek, elutasíthatod őket, vagy új javaslatokat hozhatsz létre.',
    'text_ul_1_li_2' => 'Az oldalakra bontott <strong>javaslatok listáján</strong> keresztül egyenként vagy tömeges kijelöléssel dolgozhatod fel (továbbíthatod a felettesednek, elutasíthatod) az összes feldolgozásra váró javaslatot. A javaslatokat nem kell munkaterületenként feldolgoznod, a rendelkezésre álló szűrőn keresztül igény szerint szűrheted őket.
    <br>Ezen kívül külön listákban megtekintheted, hogy milyen állapotban vannak a felettesedhez továbbított javaslataid, mely javaslatok juthatnak el még hozzád (ugyanis közvetlen beosztottjaid még dolgoznak rajtuk), valamint visszamenőleg az általad jóváhagyott és elutasított javaslatokat, amelyeket már feldolgoztál.',

    'text_3' => 'Ugrás ide:',

    'navigation' => [
        'index' => 'Kezdőlap',
        'proposals' => 'Javaslatok bérekre, bónuszokra, bónuszok csökkentésére',
        'overview' => 'Bérek áttekintése',
    ],

    'proposals' => [
        'title' => 'Javaslatok bérekre, bónuszokra, bónuszok csökkentésére',

        'manual' => 'Használati utasítás',

        'currencies' => [
            'EUR' => 'EUR',
            'CZK' => 'CZK',
            'HUF' => 'HUF',
        ],

        'statuses' => [
            'CREATED' => 'Létrehozva',
            'PENDING' => 'Feldolgozásra vár',
            'APPROVED' => 'Jóváhagyva',
            'REJECTED' => 'Elutasítva',
            'FORWARDED' => 'Felettesnek továbbítva',
            'USURPED' => 'Felettes által átvéve',
            'WITHDRAWED' => 'Visszavonva',
            'ROLLBACKED' => 'Visszaküldve javításra',
            'UPDATED' => 'Módosítva',
        ],

        'payment_statuses' => [
            'TO_BE_NOTIFIED' => 'Bérszámfejtő értesítésére vár',
            'WITHOUT_NOTIFICATION' => 'Bérszámfejtő értesítése nélkül',
            'NOTIFIED' => 'Bérszámfejtés értesítve',
        ],

        'types' => [
            'SALARY' => 'Bér',
            'REWARD' => 'Bónusz',
            'NON_FINANCIAL_REWARD' => 'Nem pénzbeli bónusz',
            'CUT' => 'Bónusz csökkentése',

            'descriptions' => [
                'SALARY' => 'Bérmódosítási javaslat, amely a kiválasztott hónaptól érvényes.',
                'REWARD' => 'Bónusz javaslat, amely a kiválasztott hónap bérével együtt kerül kifizetésre.',
                'NON_FINANCIAL_REWARD' => 'Nem pénzügyi jutalom javaslat, pl. utalványok formájában.',
                'CUT' => 'Bónusz csökkentésére vonatkozó javaslat a kiválasztott hónapra.',
            ],
        ],

        'sub_types' => [
            'F_ORDERS' => 'Megrendelések',
            'F_EXTRA' => 'Rendkívüli',
            'F_FULFILLMENT_OF_MONTH' => 'Havi terv teljesítése',
            'F_FULFILLMENT_OF_HALF_YEAR' => 'Terv teljesítése - félév',
            'F_FULFILLMENT_OF_FULL_YEAR' => 'Terv teljesítése - egész év',
            'F_MONTH_EXTRA_WAREHOUSE' => 'Havi bónusz - raktár',
            'F_ATTENDANCE' => 'Jelenléti bónusz',
            'F_ACTIVATION' => 'Ativitási bónusz',
            'F_TRANSLATIONS' => 'Fordítások',
            'F_OTHER' => 'Más - pénzbeli bónusz',

            'N_VOUCHER_VERMONT' => 'Vermont utalványok (Vermont márkákon belül)',
            'N_EMPLOYEE_DISCOUNT' => 'Munkavállalói kedvezmény',
            'N_VOUCHER_OTHER' => 'Utalványok (Fann és egyéb)',
            'N_RECREATION' => 'Üdülés (wellness, hétvége, partner kísérése üzleti útra)',
            'N_COURSE' => 'Tanfolyam/tréning/coaching',
            'N_FREE_DAY' => 'Plusz szabadnap (a szabadság és szabadnapokon felül)',
            'N_OTHER' => 'Más - nem pénzbeli',
        ],

        'list' => [
            'title' => 'Javaslatok listája',
            'checkbox' => 'x',
            'month' => 'Hónap',
            'centre_position' => 'Részleg',
            'user' => 'Alkalmazott',
            'type' => 'Típus',
            'subtype' => 'Típus - indoklás',
            'reason' => 'Javaslat indoklása',
            'value' => 'Összeg',
            'status' => 'Státusz',
            'payment_status' => 'Kifizetés',
            'is_paid_via_agreement' => 'Kifizetés bónuszként',
            'actions' => 'Műveletek',
            'final_actions' => 'Végső műveletek',
            'current_approver' => 'Jóváhagyó',
            'created_by' => 'Létrehozva',
            'created_by_centre' => 'Létrehozva - részleg',
            'created_by_department' => 'Létrehozva - osztály',
            'no_proposals' => 'Nincsenek javaslatok (amelyek megfelelnek a szűrőnek)',
            'validity_from' => 'Hónap - tól',
            'validity_to' => 'Hónap - ig',

            'show_only' => 'Csak ezeket mutasd',
            'show_only_my' => 'Feldolgozásra várók',
            'show_only_up' => 'Megerősítésre várók',
            'show_only_down' => 'Alkalmazottra várók',
            'show_only_completed' => 'Jóváhagyott és elutasított',

            'usurp' => 'Javaslat átvétele feldolgozásra',
            'withdraw' => 'Javaslat visszavétele módosításra',
            'approver_not_selected' => 'A javaslatok jóváhagyásakor meg kell határozni, melyik felettesednek lesznek továbbítva a jóváhagyott javaslatok. A rendszer nem tudta automatikusan meghatározni ezt a személyt ezért kérlek, válaszd ki!',
            'select_approver' => 'Felettes, akinek az általam jóváhagyott javaslatok továbbítva lesznek:',
            'save_approver' => 'Mentés',
            'change_is_paid_via_agreement' => 'A "Kifizetés bónuszként" megjelölés módosítása',
            'change_payment_status' => 'A "Bérszámfejtő értesítése nélkül" megjelölés módosítása',

            'add_proposal' => 'Javaslat hozzáadása',

            'multiple' => [
                'status' => 'Művelet a kijelölt javaslatokkal',
                'note' => 'Megjegyzés a felettesnek',
                'approve' => 'jóváhagyottként való beállítás',
                'reject' => 'elutasítottként való beállítás',
                'forward' => 'továbbítás felettesnek (:approver)',
                'forward_disabled' => 'továbbítás felettesnek - ELŐSZÖR VÁLASSZ FELETTEST',
            ],
        ],

        'table' => [
            'title' => 'Táblázatos nézet',
            'month' => 'Hónap',
            'centre_position' => 'Részleg',
            'employee' => 'Alkalmazott',
            'type' => 'Típus',
            'reason' => 'Javaslat megindoklása',
            'value' => 'Összeg',
            'status' => 'Státusz',
            'actions' => 'Műveletek',

            'info_no_assigments' => 'Nincs megfelelő hierarchikus jogosultságod a javaslatok feldolgozásához a táblázatos nézetben!',
            'info_select_centre' => 'A javaslatok táblázatos nézetben történő feldolgozásához ki kell választanod egy konkrét részleget!',

            'validation_date' => 'A kiválasztott dátumnak a hónap első napjának kell lennie!',
        ],

        'create' => [
            'title' => 'Javaslat hozzáadása',

            'selector' => 'Válaszd ki, milyen típusú javaslatot szeretnél létrehozni',

            'title_seleced' => 'Javaslat hozzáadása - :type',
            'users' => 'Alkalmazott / Alkalmazottak',
            'subtype' => 'Bónusz típusa',
            'f_subtype' => 'Bónusz típusa (rendkívüli és egyéb bónusz esetén kérjük, add meg a javaslat okát)',
            'n_subtype' => 'Bónusz típusa',
            'base' => 'Javasolt bér/bónusz/bónusz csökkentésének összege (bruttó)',
            'addition' => 'Javasolt személyi értékelés összege kifizetéshez (bruttó)',
            'currency' => 'A megadott összegek pénzneme',
            'reason' => 'Javaslat indoklása',
            'validity' => 'Érvényesség',
            'approver' => 'Javaslat jóváhagyója',
            'centre' => 'Létrehozva részlegen',
            'department' => 'Létrehozva osztályon',
            '' => '',

            'success_message' => '{1}A javaslat sikeresen létre lett hozva.|[2,Inf]A javaslatok sikeresen létre lettek hozva.',

            'validation' => [
                'attributes' => [
                    'users' => '"Alkalmazott / Alkalmazottak"',
                    'type' => '"Javaslat típusa"',
                    'subtype' => '"Bónusz oka"',
                    'base' => '"Javasolt bér/bónusz/bónusz csökkentésének összege"',
                    'addition' => '"Javasolt személyi értékelés összege kifizetéshez"',
                    'currency_id' => '"A megadott összegek pénzneme"',
                    'reason' => '"Javaslat indoklása"',
                    'validity' => '"Érvényesség"',
                    'approver_id' => '"Javaslat jóváhagyója"',
                    'centre_id' => '"Létrehozva - részleg"',
                    'department_id' => '"Létrehozva - osztály"',
                ],

                'users_required' => 'A javaslat(ok) létrehozásához legalább 1 alkalmazottat ki kell választani!',
                'centre_department_combination_not_valid' => 'A "Létrehozva - részleg" és "Létrehozva -osztály" kiválasztott kombinációja nem engedélyezett!',
                'approver_not_valid_for_centre_department' => 'Ez a jóváhagyó nem választható ki a "Létrehozva -részleg" és "Létrehozva -osztály" megadott kombinációhoz!',
                'existing_subtype_for_some_user' => 'Már van egy elutasított jutalmazási kérelem az alkalmazott/alkalmazottak számára a kiválasztott hónapra vonatkozóan "Ativitási bónusz" vagy "Havi terv teljesítése" típusú jutalomra!',
            ],
        ],

        'show' => [
            'title' => 'Javaslat részletei - :type',
            'users' => 'Alkalmazott',
            'subtype' => 'Bónusz típusa',
            'f_subtype' => 'Bónusz típusa (rendkívüli és egyéb esetén kérjük, add meg a javaslat okát)',
            'n_subtype' => 'Bónusz típusa',
            'base' => 'Javasolt bér/bónusz/bónusz csökkentésének összege (bruttó)',
            'addition' => 'Javasolt személyi értékelés összege kifizetéshez (bruttó)',
            'currency' => 'A megadott összegek pénzneme',
            'reason' => 'Javaslat indoklása',
            'validity' => 'Érvényesség',
            'proposer' => 'Javaslat létrehozója',
            'centre' => 'Létrehozva - részleg',
            'department' => 'Létrehozva - osztály',

            'logs' => [
                'title' => 'Javaslat módosításainak naplója',
                'created_at' => 'Módosítás ideje',
                'user' => 'Módosította',
                'status' => 'Módosítás típusa',
                'note' => 'Megjegyzés',
                'changes' => 'Változások listája',
                'history_changes' => 'Változások előzményei',
            ],
        ],

        'edit' => [
            'title' => 'Javaslat szerkesztése - :type',
            'users' => 'Alkalmazott',
            'subtype' => 'Bónusz típusa',
            'f_subtype' => 'Bónusz típusa (rendkívüli és egyéb esetén kérjük, add meg a javaslat okát)',
            'n_subtype' => 'Bónusz típusa',
            'base' => 'Javasolt bér/bónusz/bónusz csökkentésének összege (bruttó)',
            'addition' => 'Javasolt személyi értékelés összege kifizetéshez (bruttó)',
            'currency' => 'A megadott összegek pénzneme',
            'reason' => 'Javaslat indoklása',
            'validity' => 'Érvényesség',
            'approver' => 'Javaslat jóváhagyója',
            'centre' => 'Létrehozva - részleg',
            'department' => 'Létrehozva - osztály',

            'success_message' => 'A javaslat sikeresen módosítva.',
        ],

        'forward' => [
            'success_message' => 'A javaslat továbbítva a felettesnek jóváhagyásra.',
        ],

        'approve' => [
            'success_message' => 'A javaslat jóváhagyott.',
        ],

        'reject' => [
            'success_message' => 'A javaslat elutasított.',
        ],

        'change_is_paid_via_agreement' => [
            'success_message' => 'A jutalomként történő kifizetés megjelölés módosítva.',
        ],

        'change_payment_status' => [
            'success_message' => 'A kifizetés státusz módosítva.',
        ],

        'usurp' => [
            'success_message' => 'Átvetted a javaslatot feldolgozásra.',
        ],

        'withdraw' => [
            'success_message' => 'Visszavetted a javaslatot módosításra.',
        ],

        'rollback' => [
            'success_message' => 'A javaslat visszaküldve az eredeti javaslónak, javításra.',
        ],

        'save_approver' => [
            'success_message' => 'A felettes, akinek a jóváhagyott javaslataid továbbítva lesznek, beállítva.',
        ],

        'status_multiple' => [
            'validation_incorrect_current_approver' => 'Olyan javaslatokat próbálsz feldolgozni, amelyek nincsenek hozzád rendelve!',
            'validation_required' => 'Legalább egy javaslatot ki kell jelölnöd, amelynek az állapotát módosítani szeretnéd!',
            'validation_status_approve_not_allowed' => 'Nincs jogosultságod a "jóváhagyott" állapot beállítására!',
            'validation_approver_not_exists' => 'Meg kell adnod a felettesed, akinek a javaslatokat továbbítod!',

            'message' => [
                'APPROVED' => 'A kijelölt javaslatok jóváhagyottként beállítva!',
                'FORWARDED' => 'A kijelölt javaslatok továbbítva a felettesnek!',
                'REJECTED' => 'A kijelölt javaslatok elutasítottként beállítva!',
            ],
        ],

        'export' => [
            'button' => 'Szűrt exportálása XLS-be',
            'month' => 'Hónap',
            'department' => 'Osztály',
            'centre' => 'Központ',
            'employee' => 'Alkalmazott',
            'type' => 'Típus',
            'subtype' => 'Alcsoport',
            'base' => 'Alap/Jutalom',
            'addition' => 'Személyes értékelés',
            'currency' => 'Pénznem',
            'status' => 'Állapot',
            'reason' => 'Javaslat oka',
        ],
    ],

    'salaries' => [
        'title' => 'Beosztottak fizetésének áttekintése',
        'list' => [
            'title' => 'Fizetések áttekintése',

            'user' => 'Alkalmazott',
            'contract' => 'Munkaviszony',
            'assigments' => 'Munkavállaló beosztása',
            'salary' => 'Fizetés',
            'validity' => 'Érvényes ettől',
            'centre' => 'Központ',
            'position' => 'Pozíció',
            'department' => 'Osztály',
            'no_salary' => 'Fizetés nincs kitöltve',
            'no_contracts' => 'Nincs kitöltött fizetéssel rendelkező munkaviszony (megfelelő esetleges szűrőnek)',
            'show_salaries' => 'Fizetések automatikus megjelenítése',
            'history_button' => 'Történelem',

            'history' => [
                'title' => 'A ":contract" munkaszerződésű alkalmazott ":employee" béráttekintése',
                'salary' => 'Bér',
                'base' => 'Alapösszeg',
                'addition' => 'Személyes értékelés',
                'validity' => 'Érvényes ettől',
            ],

        ],
        'plans' => [
            'title' => 'Tervezett bérmódosítások',

            'info' => 'Ebben a szekcióban hozzáadhatod a tervezett bérmódosítások értesítéseit a beosztottaid számára. Egy hónappal az új bérmódosítás érvénybe lépése előtt az alkalmazott aktuális felettese emailben értesítést kap, hogy készítse el a módosítási javaslatot.',

            'user' => 'Alkalmazott',
            'assigments' => 'Munkakör',
            'validity' => 'Módosítás érvényes ettől',
            'salary' => 'Módosított bér',
            'note' => 'Megjegyzés',
            'creator' => 'Módosítást készítette',
            'status' => 'Állapot',
            'none' => 'Nincsenek tervezett bérmódosítások a beosztottaid számára!',

            'statuses' => [
                'PLANNED' => 'tervezett',
                'CREATED' => 'létrehozva',
                'CANCELLED' => 'lemondott',
            ],

            'create' => [
                'title' => 'Tervezett bérmódosítás értesítés létrehozása',

                'user_id' => 'Alkalmazott',
                'validity' => 'Módosított bér érvényes ettől',
                'currency' => 'Pénznem',
                'base' => 'Alap összeg',
                'addition' => 'Személyes értékelés összege',
                'note' => 'Megjegyzés',

                'created' => 'A tervezett bérmódosítás sikeresen létrehozva!',
            ],

            'created_via_automat' => 'a rendszer automatikusan létrehozza a tervezett beállításból',
        ],
    ],

    'component' => [
        'months' => [
            '0' => 'Január',
            '1' => 'Február',
            '2' => 'Március',
            '3' => 'Április',
            '4' => 'Május',
            '5' => 'Június',
            '6' => 'Július',
            '7' => 'Augusztus',
            '8' => 'Szeptember',
            '9' => 'Október',
            '10' => 'November',
            '11' => 'December',
        ],
        'general' => [
            'centre' => 'Részleg',
            'type' => 'Típus',
            'subtype' => 'Bónusz típusa',
            'base' => 'Bónusz bér/jutalom/jutalom csökkentésének összege (bruttó)',
            'addition' => 'Javasolt személyi értékelés összegeének fizetéshez (bruttó)',
            'currency' => 'A megadott összegek pénzneme',
            'currency_id' => 'A megadott összegek pénzneme',
            'reason' => 'Javaslat indoklása',
            'validity' => 'Érvényesség',
            'approver' => 'Jóváhagyó',
            'approver_id' => 'Jóváhagyó',
            'create' => 'Kérelem létrehozása',
            'add_next_proposal' => 'További javaslat hozzáadása',
            'next_proposal' => 'Következő javaslat',
            'add_more_people' => 'További alkalmazott hozzáadása',
            'which_type_add' => 'Javaslat típusa',
            'add' => 'Kiválasztás',
            'centre_users' => 'Részleg alkalmazottai',
            'subcases_error' => 'Ki kell választanod a javaslat okát',
            'type_error' => 'Ki kell választanod a javaslat típusát',
            'not_assigned' => 'Nincs hozzárendelve',
            'approve_all' => 'Összes jóváhagyása',
            'reject_all' => 'Összes elutasítása',
            'approve_row' => 'Sor jóváhagyása',
            'approve_column' => 'Oszlop jóváhagyása',
            'approve_cell' => 'Cella jóváhagyása',
            'reject_row' => 'Sor elutasítása',
            'reject_column' => 'Oszlop elutasítása',
            'reject_cell' => 'Cella elutasítása',
            'cancel' => 'Mégsem',
            'confirm' => 'Jóváhagyás',
            'and_add_detailed_description' => 'Megjegyzés a felettesnek',
            'select_approver' => 'Válassz jóváhagyót',
            'final_approver' => 'Te vagy a végső jóváhagyó',
            'budget' => 'Költségkeret',
            'approve' => 'Jóváhagyás',
            'reject' => 'Elutasítás',
            'pay_out_on_aggreement' => 'Kifizetés megegyezés alapján',
            'without_informing_payroll' => 'Bérszámfejtő értesítése nélkül',
            'change_all_for_employee' => 'Összes módosítása az alkalmazottnál',
            'change_all' => 'Minden javaslat módosítása',
            'both_column' => 'Összes módosítása az oszlopban',
            'all_available' => 'Összes elérhető módosítása',
            'send_back_to_repair' => 'Visszaküldés javításra',
            'change' => 'Módosítás',
            'detail' => 'Részletek',
            'created_by' => 'Létrehozta',
            'for' => 'részére',
            'proposal' => 'Kérelem',
            'value' => 'Összeg',
            'note' => 'Megjegyzés',
            'employee' => 'Alkalmazott',
            'send_proposals_to' => 'Kérelmek küldése ide',
            'add_proposal' => 'Kérelem hozzáadása',
            'is_paid_via_another_way' => 'Más formában kifizetve',
            'select' => 'Válassz',
            'select_unselect_all' => 'Jelölés/nem jelölés Minden',
            'select_all' => 'Összes kijelölése',
            'deselect_all' => 'Összes kijelölés megszüntetése',
            'select_opposite' => 'Ellentétes kijelölés',
            'exceeded' => 'Túllépett',
            'exceeded_error_text' => 'Vigyázz, túllépted a költségkeretet',
            'usurp' => 'A javaslat átvétele',
            'usurp_title' => 'A beosztottad javaslatának átvétele',
            'usurp_description' => 'Ez a javaslat nem lett elküldve. Biztosan át szeretnéd venni?',
            'is_paid_via_agreement' => 'Kifizetés bónuszként',
            'history' => 'előzmények',
            'history_full' => 'Összes előzmény megjelenítése',
            'close' => 'Bezárás',
            'changes' => 'Változások',
            'no_recent_changes' => 'Nincsenek friss változások',
            'centre_id' => 'Részleg',
            'department_id' => 'Létrehozva - osztály',
            'payment_status' => 'Kifizetés',
            'user' => 'Alkalmazott',
            'create_disabled_by_validity' => 'Ebben a hónapban már nem lehet az ilyen típusú kéréseket benyújtani',
            'update_disabled_by_validity' => 'Ebben a hónapban már nem lehet az ilyen típusú kéréseket módosítani',
            'no_users' => 'Nincsenek felhasználók',
            'show_competition_report' => 'Versenynapló/értékelés megjelenítése',
        ]
    ]
];
