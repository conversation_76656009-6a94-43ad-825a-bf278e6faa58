<?php

namespace {{ namespace }};

use {{ namespacedModel }};
use {{ rootNamespace }}Http\Controllers\Controller;
use {{ namespacedRequests }}

class {{ class }} extends Controller
{
    public function index()
    {
        //
    }

    public function create()
    {
        //
    }

    public function store({{ storeRequest }} $request)
    {
        //
    }

    public function show({{ model }} ${{ modelVariable }})
    {
        //
    }

    public function edit({{ model }} ${{ modelVariable }})
    {
        //
    }

    public function update({{ updateRequest }} $request, {{ model }} ${{ modelVariable }})
    {
        //
    }

    public function destroy({{ model }} ${{ modelVariable }})
    {
        //
    }
}
