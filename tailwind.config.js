import forms from '@tailwindcss/forms';
import defaultTheme from 'tailwindcss/defaultTheme';

/** @type {import('tailwindcss').Config} */

export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.vue',
    ],
    safelist: [
        'w-[0%]', 'w-[1%]', 'w-[2%]', 'w-[3%]', 'w-[4%]', 'w-[5%]', 'w-[6%]', 'w-[7%]', 'w-[8%]', 'w-[9%]', 'w-[10%]', 'w-[11%]', 'w-[12%]', 'w-[13%]', 'w-[14%]', 'w-[15%]', 'w-[16%]', 'w-[17%]', 'w-[18%]', 'w-[19%]', 'w-[20%]', 'w-[21%]', 'w-[22%]', 'w-[23%]', 'w-[24%]', 'w-[25%]', 'w-[26%]', 'w-[27%]', 'w-[28%]', 'w-[29%]', 'w-[30%]', 'w-[31%]', 'w-[32%]', 'w-[33%]', 'w-[34%]', 'w-[35%]', 'w-[36%]', 'w-[37%]', 'w-[38%]', 'w-[39%]', 'w-[40%]', 'w-[41%]', 'w-[42%]', 'w-[43%]', 'w-[44%]', 'w-[45%]', 'w-[46%]', 'w-[47%]', 'w-[48%]', 'w-[49%]', 'w-[50%]', 'w-[51%]', 'w-[52%]', 'w-[53%]', 'w-[54%]', 'w-[55%]', 'w-[56%]', 'w-[57%]', 'w-[58%]', 'w-[59%]', 'w-[60%]', 'w-[61%]', 'w-[62%]', 'w-[63%]', 'w-[64%]', 'w-[65%]', 'w-[66%]', 'w-[67%]', 'w-[68%]', 'w-[69%]', 'w-[70%]', 'w-[71%]', 'w-[72%]', 'w-[73%]', 'w-[74%]', 'w-[75%]', 'w-[76%]', 'w-[77%]', 'w-[78%]', 'w-[79%]', 'w-[80%]', 'w-[81%]', 'w-[82%]', 'w-[83%]', 'w-[84%]', 'w-[85%]', 'w-[86%]', 'w-[87%]', 'w-[88%]', 'w-[89%]', 'w-[90%]', 'w-[91%]', 'w-[92%]', 'w-[93%]', 'w-[94%]', 'w-[95%]', 'w-[96%]', 'w-[97%]', 'w-[98%]', 'w-[99%]', 'w-[100%]', //for progressbars in VUE components
        'ms-[calc(0%-1.25rem)]', 'ms-[calc(1%-1.25rem)]', 'ms-[calc(2%-1.25rem)]', 'ms-[calc(3%-1.25rem)]', 'ms-[calc(4%-1.25rem)]', 'ms-[calc(5%-1.25rem)]', 'ms-[calc(6%-1.25rem)]', 'ms-[calc(7%-1.25rem)]', 'ms-[calc(8%-1.25rem)]', 'ms-[calc(9%-1.25rem)]', 'ms-[calc(10%-1.25rem)]', 'ms-[calc(11%-1.25rem)]', 'ms-[calc(12%-1.25rem)]', 'ms-[calc(13%-1.25rem)]', 'ms-[calc(14%-1.25rem)]', 'ms-[calc(15%-1.25rem)]', 'ms-[calc(16%-1.25rem)]', 'ms-[calc(17%-1.25rem)]', 'ms-[calc(18%-1.25rem)]', 'ms-[calc(19%-1.25rem)]', 'ms-[calc(20%-1.25rem)]', 'ms-[calc(21%-1.25rem)]', 'ms-[calc(22%-1.25rem)]', 'ms-[calc(23%-1.25rem)]', 'ms-[calc(24%-1.25rem)]', 'ms-[calc(25%-1.25rem)]', 'ms-[calc(26%-1.25rem)]', 'ms-[calc(27%-1.25rem)]', 'ms-[calc(28%-1.25rem)]', 'ms-[calc(29%-1.25rem)]', 'ms-[calc(30%-1.25rem)]', 'ms-[calc(31%-1.25rem)]', 'ms-[calc(32%-1.25rem)]', 'ms-[calc(33%-1.25rem)]', 'ms-[calc(34%-1.25rem)]', 'ms-[calc(35%-1.25rem)]', 'ms-[calc(36%-1.25rem)]', 'ms-[calc(37%-1.25rem)]', 'ms-[calc(38%-1.25rem)]', 'ms-[calc(39%-1.25rem)]', 'ms-[calc(40%-1.25rem)]', 'ms-[calc(41%-1.25rem)]', 'ms-[calc(42%-1.25rem)]', 'ms-[calc(43%-1.25rem)]', 'ms-[calc(44%-1.25rem)]', 'ms-[calc(45%-1.25rem)]', 'ms-[calc(46%-1.25rem)]', 'ms-[calc(47%-1.25rem)]', 'ms-[calc(48%-1.25rem)]', 'ms-[calc(49%-1.25rem)]', 'ms-[calc(50%-1.25rem)]', 'ms-[calc(51%-1.25rem)]', 'ms-[calc(52%-1.25rem)]', 'ms-[calc(53%-1.25rem)]', 'ms-[calc(54%-1.25rem)]', 'ms-[calc(55%-1.25rem)]', 'ms-[calc(56%-1.25rem)]', 'ms-[calc(57%-1.25rem)]', 'ms-[calc(58%-1.25rem)]', 'ms-[calc(59%-1.25rem)]', 'ms-[calc(60%-1.25rem)]', 'ms-[calc(61%-1.25rem)]', 'ms-[calc(62%-1.25rem)]', 'ms-[calc(63%-1.25rem)]', 'ms-[calc(64%-1.25rem)]', 'ms-[calc(65%-1.25rem)]', 'ms-[calc(66%-1.25rem)]', 'ms-[calc(67%-1.25rem)]', 'ms-[calc(68%-1.25rem)]', 'ms-[calc(69%-1.25rem)]', 'ms-[calc(70%-1.25rem)]', 'ms-[calc(71%-1.25rem)]', 'ms-[calc(72%-1.25rem)]', 'ms-[calc(73%-1.25rem)]', 'ms-[calc(74%-1.25rem)]', 'ms-[calc(75%-1.25rem)]', 'ms-[calc(76%-1.25rem)]', 'ms-[calc(77%-1.25rem)]', 'ms-[calc(78%-1.25rem)]', 'ms-[calc(79%-1.25rem)]', 'ms-[calc(80%-1.25rem)]', 'ms-[calc(81%-1.25rem)]', 'ms-[calc(82%-1.25rem)]', 'ms-[calc(83%-1.25rem)]', 'ms-[calc(84%-1.25rem)]', 'ms-[calc(85%-1.25rem)]', 'ms-[calc(86%-1.25rem)]', 'ms-[calc(87%-1.25rem)]', 'ms-[calc(88%-1.25rem)]', 'ms-[calc(89%-1.25rem)]', 'ms-[calc(90%-1.25rem)]', 'ms-[calc(91%-1.25rem)]', 'ms-[calc(92%-1.25rem)]', 'ms-[calc(93%-1.25rem)]', 'ms-[calc(94%-1.25rem)]', 'ms-[calc(95%-1.25rem)]', 'ms-[calc(96%-1.25rem)]', 'ms-[calc(97%-1.25rem)]', 'ms-[calc(98%-1.25rem)]', 'ms-[calc(99%-1.25rem)]', 'ms-[calc(100%-1.25rem)]', //for progressbars in VUE components
    ]
    ,


    plugins: [
        forms,
    ],
    darkMode: 'false',
};
