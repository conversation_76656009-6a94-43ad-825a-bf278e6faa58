<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
//        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->appendToGroup('web', [
            \Laravel\Passport\Http\Middleware\CreateFreshApiToken::class,
            \App\Http\Middleware\LocalizationMiddleware::class,
            \App\Http\Middleware\AddContext::class,
        ]);
        $middleware->appendToGroup('api', [
            \App\Http\Middleware\Api\SetUserLangMiddleware::class,
            \App\Http\Middleware\AddContext::class,
        ]);
        $middleware->alias([
            'client' => \Laravel\Passport\Http\Middleware\CheckClientCredentials::class
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->renderable(function (\Illuminate\Auth\AuthenticationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => __('validation.unauthenticated')
                ], 401);
            }
            return null;
        });

        $exceptions->renderable(function (\Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => __('validation.unauthorized')
                ], 403);
            }
            return null;
        });
    })
    ->create();
