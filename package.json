{"private": true, "type": "module", "scripts": {"build": "vite build", "prod": "vite build", "dev": "vite"}, "devDependencies": {"@iconify-json/tabler": "^1.2.17", "@iconify/tailwind4": "^1.0.6", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/vite": "^4.0.0", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.7.4", "concurrently": "^9.0.1", "flyonui": "^2.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "sass-embedded": "^1.85.1", "tailwindcss": "^4.0", "unplugin-auto-import": "^19.1.2", "vite": "^6.0.11"}, "dependencies": {"@floating-ui/dom": "^1.6.13", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^5.2.1", "sweetalert2": "^11.17.2", "vue": "^3.5.13"}}