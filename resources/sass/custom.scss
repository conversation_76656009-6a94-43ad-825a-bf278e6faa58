/* Len ukazka ze tu maju ist uplne custom CSSka */

.custom-loader {
    color: hotpink;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;

    &:after {
        box-sizing: border-box;
        content: " ";
        display: block;
        border-radius: 50%;
        width: 0;
        height: 0;
        margin: 8px;
        border: 32px solid currentColor;
        border-color: currentColor transparent currentColor transparent;
        animation: custom-loader 1.2s infinite;
    }
}

@keyframes custom-loader {
    0% {
        transform: rotate(0);
        animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }
    50% {
        transform: rotate(900deg);
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }
    100% {
        transform: rotate(1800deg);
    }
}

[v-cloak] {
    display: none;
}