<div class="dropdown [--adaptive:none] [--auto-close:inside] [--strategy:static] md:[--strategy:absolute]">
    <button type="button" class="dropdown-toggle btn btn-text font-normal dropdown-open:bg-base-content/10 dropdown-open:text-base-content max-md:px-3 w-full md:w-auto justify-between" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
        {{ Auth::user()->login }}
        <span class="icon-[tabler--chevron-down] dropdown-open:rotate-180 size-4"></span>
    </button>
    <div class="dropdown-menu dropdown-open:opacity-100 end-0 top-full hidden w-full md:w-auto min-w-60 md:rounded-t-none py-2 opacity-0 transition-[opacity,margin] duration-[0.1ms] before:absolute max-md:border max-md:shadow-none" role="menu" aria-orientation="vertical">
        <ul class="menu md:menu-horizontal rounded-box w-full max-xl:gap-4 p-0">
            <li class="w-full">
                <a href="#" class="cursor-default menu-title">{{ Auth::user()->full_name }}</a>
                <ul class="menu bg-transparent">
                    {{--                                        <li><a href="{{ route('employee.profile.edit') }}">{{ __('employee.navigation.profile') }}</a></li>--}}
                    @can(\App\Models\RaP\Permission::HR_ADMINISTRATION_ACCESS)
                        <li><a class="bg-pink-100" href="{{ route('hr.index') }}">{{ __('it.navigation.hr') }}</a></li>
                    @endcan
                    @can(\App\Models\RaP\Permission::IT_ADMINISTRATION_ACCESS)
                        <li><a class="bg-blue-100" href="{{ route('it.index') }}">{{ __('hr.navigation.it') }}</a></li>
                    @endcan
                    <li>
                        <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logoutForm').submit();">
                            {{ __('employee.navigation.logout') }}
                            <form method="POST" action="{{ route('logout') }}" class="w-full h-full" id="logoutForm">
                                @csrf
                            </form>
                        </a>
                    </li>
                </ul>
            </li>
            @can(\App\Models\RaP\Permission::DEVELOPER_CAN_ACCEESS_LOGVIEWER)
                @include('layouts._same_parts.navigation_items.profile.developer', ['developerTitle' => __('employee.navigation.developer')])
            @endcan
            <li class="w-full">
                <a href="#" class="cursor-default menu-title">{{ __('employee.navigation.language') }}</a>
                @include('layouts._same_parts.navigation_items.profile.language_change')
            </li>
        </ul>
    </div>
</div>
