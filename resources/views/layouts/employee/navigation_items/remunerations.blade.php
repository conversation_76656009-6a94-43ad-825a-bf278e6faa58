<div class="dropdown [--adaptive:none] [--auto-close:inside] [--strategy:static] md:[--strategy:absolute]">
    <button type="button" class="dropdown-toggle btn btn-text font-normal dropdown-open:bg-base-content/10 dropdown-open:text-base-content max-md:px-3 w-full md:w-auto justify-between" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
        {{ __('employee.navigation.remunerations') }}
        <span class="icon-[tabler--chevron-down] dropdown-open:rotate-180 size-4"></span>
    </button>
    <div class="dropdown-menu dropdown-open:opacity-100 start-0 top-full hidden w-full min-w-60 md:rounded-t-none py-2 opacity-0 transition-[opacity,margin] duration-[0.1ms] before:absolute max-md:border max-md:shadow-none  z-20"
         role="menu" aria-orientation="vertical">
        <ul class="menu md:menu-horizontal rounded-box w-full max-xl:gap-4 p-0">
            @if(!empty($menuItem))
                @if($menuItem['sections']['proposals'] ?? false)
                    <li>
                        <a href="{{ route('employee.remunerations.index') }}" class="menu-title">{{ __('employee.remunerations.proposals.title') }}</a>
                        <ul class="menu bg-transparent">
                            <li><a href="{{ route('employee.remunerations.proposals.index.table') }}">{{ __('employee.remunerations.proposals.table.title') }}</a></li>
                            <li><a href="{{ route('employee.remunerations.proposals.index.list') }}">{{ __('employee.remunerations.proposals.list.title') }}</a></li>
                            <li><a href="{{ route('employee.remunerations.proposals.create') }}">{{ __('employee.remunerations.proposals.create.title') }}</a></li>
                            <li><a href="{{ asset('documents/manuals/remuneration_proposals_'.app()->getLocale().'.pdf') }}" target="_blank">{{ __('employee.remunerations.proposals.manual') }}</a></li>
                        </ul>
                    </li>
                @endif
                @if($menuItem['sections']['salaries'] ?? false)
                    <li>
                        <a href="{{ route('employee.remunerations.salaries.index') }}" class="cursor-default menu-title">{{ __('employee.remunerations.salaries.title') }}</a>
                        <ul class="menu bg-transparent">
                            <li><a href="{{ route('employee.remunerations.salaries.index') }}">{{ __('employee.remunerations.salaries.list.title') }}</a></li>
                            <li><a href="{{ route('employee.remunerations.salaries.plans.index') }}">{{ __('employee.remunerations.salaries.plans.title') }}</a></li>
                        </ul>
                    </li>
                @endif
            @endif
        </ul>
    </div>
</div>
