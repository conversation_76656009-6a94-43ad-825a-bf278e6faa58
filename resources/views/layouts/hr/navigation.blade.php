<div class=" bg-base-100 flex w-full flex-wrap py-4 text-sm md:flex-nowrap md:justify-start md:py-0 border-b border-neutral/10">
    <nav class="mx-auto w-full px-4" aria-label="Global">
        <div class="relative md:flex md:items-center">
            <div class="flex items-center justify-between">
                <a class="link text-base-content link-neutral text-xl font-semibold no-underline" href="{{ route('hr.index') }}">
                    <img width="130" src="{{ Vite::asset('resources/images/layout/LogoLight.svg') }}" alt="Logo">
                </a>
                <div class="md:hidden">
                    <button type="button" class="collapse-toggle btn btn-outline btn-secondary btn-sm btn-square" data-collapse="#navbar-mega-menu-click" aria-controls="navbar-mega-menu-click" aria-label="Toggle navigation" >
                        <span class="icon-[tabler--menu-2] collapse-open:hidden size-4"></span>
                        <span class="icon-[tabler--x] collapse-open:block hidden size-4"></span>
                    </button>
                </div>
            </div>
            <div id="navbar-mega-menu-click" class="collapse hidden grow basis-full overflow-hidden rounded-lg transition-all duration-300 md:block">
                <div class="flex flex-col rounded-lg max-md:mt-3 max-md:border max-md:p-2 md:flex-row md:items-center md:justify-end md:ps-5 md:pe-0.5 gap-2" >
                    <ul class="menu md:menu-horizontal text-base px-0 max-md:w-100 max-md:py-0 gap-2">
                        @foreach(['dashboard'] as $simpleItem)
                            @isset($hrTopMenuItems[$simpleItem])
                                <li class="">
                                    <a class="px-3" href="{{ $hrTopMenuItems[$simpleItem]['href'] }}" target="{{ $hrTopMenuItems[$simpleItem]['target'] }}">
                                        {!! $hrTopMenuItems[$simpleItem]['label'] !!}
                                    </a>
                                </li>
                            @endif
                        @endforeach
                    </ul>
                    @foreach(['users', 'organisation'] as $complexItem)
                        @isset($hrTopMenuItems[$complexItem])
                            @include('layouts.hr.navigation_items.'.$complexItem)
                        @endif
                    @endforeach
                    <ul class="menu md:menu-horizontal text-base px-0 max-md:w-100 max-md:py-0 gap-2">
                        @foreach(['remunerations'] as $simpleItem)
                            @isset($hrTopMenuItems[$simpleItem])
                                <li class="">
                                    <a class="px-3" href="{{ $hrTopMenuItems[$simpleItem]['href'] }}" target="{{ $hrTopMenuItems[$simpleItem]['target'] }}">
                                        {!! $hrTopMenuItems[$simpleItem]['label'] !!}
                                    </a>
                                </li>
                            @endif
                        @endforeach
                    </ul>
                    @include('layouts.hr.navigation_items.profile')
                </div>
            </div>
        </div>

    </nav>
    <div class="absolute top-0 w-full h-1 bg-pink-400"></div>
</div>
