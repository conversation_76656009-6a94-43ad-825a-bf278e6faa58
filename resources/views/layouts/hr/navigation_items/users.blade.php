<div class="dropdown [--adaptive:none] [--auto-close:inside] [--strategy:static] md:[--strategy:absolute]">
    <button type="button" class="dropdown-toggle btn btn-text font-normal dropdown-open:bg-base-content/10 dropdown-open:text-base-content max-md:px-3 w-full md:w-auto justify-between" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
        {{ __('hr.navigation.users') }}
        <span class="icon-[tabler--chevron-down] dropdown-open:rotate-180 size-4"></span>
    </button>
    <div class="dropdown-menu dropdown-open:opacity-100 start-0 top-full hidden w-full min-w-60 md:rounded-t-none py-2 opacity-0 transition-[opacity,margin] duration-[0.1ms] before:absolute max-md:border max-md:shadow-none" role="menu" aria-orientation="vertical">
        <ul class="menu md:menu-horizontal rounded-box w-full max-xl:gap-4 p-0">
            @foreach(\App\Enums\Person\Assigment\AssigmentTypeEnum::cases() as $case)
                <li>
                    <a href="{{ route('hr.users.index', $case->urlParameter()) }}" class="dropdown-item">
                        {{ __('hr.users.assigment_types_tab.'.$case->urlParameter()) }}
                    </a>
                </li>
            @endforeach
        </ul>
    </div>
</div>
