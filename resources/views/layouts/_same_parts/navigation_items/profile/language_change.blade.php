<ul class="menu bg-transparent">
    @foreach(config('vermont.available_localizations') as $lang)
        <li>
            <a @class(['flex', 'items-center', 'gap-2', (auth()->user()->lang === $lang) ? 'font-bold' : '']) href="{{ route('global.change_localization', ['lang' => $lang]) }}">
                @php
                    $flag_filename = $lang . '_' . strtoupper($lang) . '.svg';
                    $flag_path = resource_path('images/flags/' . $flag_filename);
                @endphp
                @if(file_exists($flag_path))
                    <span class="inline-block size-4 align-middle me-3">
                        {!! file_get_contents($flag_path) !!}
                    </span>
                @endif
                {{ $lang }}
            </a>
        </li>
    @endforeach
</ul>
