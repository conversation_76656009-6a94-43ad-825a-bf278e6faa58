<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.title')" :link="route('dashboard')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('auth.second_step_verification.title')" :link="route('auth.second_step_verification.show')"></x-breadcrumbs.current>
    </x-slot>

    <div class="py-6">
        <div class="mx-auto">
            <div class="bg-base-100 shadow-sm sm:rounded-lg">
                <div class="p-6 text-base-content">
                    <p class="pb-6">
                        {!! __('auth.second_step_verification.text') !!}
                    </p>

                    <form class="w-full md:w-1/2" method="POST" action="{{ route('auth.second_step_verification.post') }}">
                        @csrf

                        <!-- Email Address -->
                        <div>
                            <x-input-label for="code" :value="__('auth.second_step_verification.sms_code')" />
                            <x-text-input id="code" class="block mt-1 w-full" type="text" name="code" :value="old('code', $currentValidCode)" required autofocus autocomplete="off" />
                            <x-input-error :messages="$errors->get('code')" class="mt-2" />
                        </div>

                        <div class="flex items-center justify-between mt-4">
                            <div>
                                @if($user?->workData?->email)
                                    <a href="{{ route('auth.second_step_verification.email') }}" class="btn btn-outline">
                                        {{ __('auth.second_step_verification.email_force_send') }}
                                    </a>
                                @endif
                            </div>

                            <button class="btn btn-default ms-3">
                                {{ __('auth.second_step_verification.sms_verify') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-employee-layout>
