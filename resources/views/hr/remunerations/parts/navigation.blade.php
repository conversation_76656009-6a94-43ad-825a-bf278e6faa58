<div class="sm:hidden" x-data="{ }">
    <label for="hs-card-nav" class="sr-only">Select a nav</label>
    <select x-on:change="window.location = $event.target.value" name="hs-card-nav" id="hs-card-nav" class="select rounded-b-none block w-full border-t-0 border-x-0 border-gray-300 rounded-t-xl focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none">
        @foreach([
            ['key' => 'approved_proposals', 'translation' => __('hr.remunerations.navigation.approved_proposals'), 'route' => route('hr.remunerations.approved_proposals.index', ['month' => $month->toDateString()])],
            ['key' => 'proposal_subtype_budgets', 'translation' => __('hr.remunerations.navigation.proposal_subtype_budgets'), 'route' => route('hr.remunerations.proposal_subtype_budgets.index', ['month' => $month->toDateString()])],
        ] as $tab)
            <option value="{{ $tab['route'] }}" {{ $currentTab === $tab['key'] ? 'selected' : '' }}>{{ $tab['translation'] }}</option>
        @endforeach
    </select>
</div>
<!-- End Select (Mobile only) -->

<!-- Nav (Device only) -->
<div class="hidden sm:block">
    <nav class="relative z-0 flex border-b divide-x">
        @foreach([
            ['key' => 'approved_proposals', 'translation' => __('hr.remunerations.navigation.approved_proposals'), 'route' => route('hr.remunerations.approved_proposals.index', ['month' => $month->toDateString()])],
            ['key' => 'proposal_subtype_budgets', 'translation' => __('hr.remunerations.navigation.proposal_subtype_budgets'), 'route' => route('hr.remunerations.proposal_subtype_budgets.index', ['month' => $month->toDateString()])],
        ] as $tab)
            <a @class([
                'group',
                'relative',
                'min-w-0',
                'flex-1',
                'bg-base-100',
                'py-4',
                'px-4',
                'border-b-4' => ($currentTab === $tab['key']),
                'border-b-pink-400' => ($currentTab === $tab['key']),
                'text-gray-500' =>  ($currentTab !== $tab['key']),
                'text-base-content' => ($currentTab === $tab['key']),
                'hover:text-gray-700' => ($currentTab !== $tab['key']),
                'rounded-tl-xl' => ($loop->first),
                'rounded-tr-xl' => ($loop->last),
                'text-sm',
                'font-medium',
                'text-center',
                'overflow-hidden',
                'focus:outline-none',
                'focus:bg-purple-50',
                'focus:z-10',
            ]) href="{{ $tab['route'] }}">
                {{ $tab['translation'] }}
            </a>
        @endforeach
    </nav>
</div>
