<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.remunerations.title')"
                              :link="route('hr.remunerations.approved_proposals.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.remunerations.approved_proposals.title', ['month' => $month->format('m/Y')])"
                               :link="route('hr.remunerations.approved_proposals.index', ['month' => $month->toDateString()])"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full flex items-center justify-center">
        <div class="border rounded-md text-center font-bold text-xl inline-flex justify-between items-center p-0.5">
            <a href="{{ route('hr.remunerations.approved_proposals.index', ['month' => $month->subMonth()->toDateString()]) }}"
               class="btn btn-secondary btn-soft py-3 bg-base-200/50 hover:bg-base-300/20">
                <i class="hrms hrms-chevron-big-left text-secondary"></i>
            </a>
            <span class="font-bold flex items-center gap-2 px-3">
                <img src="{{ Vite::asset('resources/images/general/cal.svg') }}" alt="Calendar" class="w-5 h-5">
                {{ $month->format('m/Y') }}
            </span>
            <a href="{{ route('hr.remunerations.approved_proposals.index', ['month' => $month->addMonth()->toDateString()]) }}"
               class="btn btn-secondary btn-soft py-3 bg-base-200/50 hover:bg-base-300/20">
                <i class="hrms hrms-chevron-big-right text-secondary"></i>
            </a>
        </div>
    </div>

    <div>
        @include('hr.remunerations.parts.navigation')

        <div class="bg-base-100 p-3 mb-3 w-full">

            @if($proposalsWithoutContract)
                <x-flyonui.toast color="error" class="mb-4">
                    <a href="{{ route('hr.remunerations.approved_proposals.index', ['month' => $month->toDateString(), 'filter[custom][equal][proposal_contract]' => 'no']) }}" class="w-full text-center">
                        {{ __('hr.remunerations.approved_proposals.proposal_contract_no_alert', ['count' => $proposalsWithoutContract]) }}
                    </a>
                </x-flyonui.toast>
            @endif

            <div class="mb-3">
                <universal-filter
                    :filter-inputs="{{json_encode($filterInputs)}}"
                    :filter-data="{{json_encode($filterData)}}"
                ></universal-filter>
            </div>

            @if($errors->any())
                <x-flyonui.toast color="error" class="mb-4">
                    @foreach($errors->all() as $error)
                        {{ $error }} <br />
                    @endforeach
                </x-flyonui.toast>
            @endif

            <x-data-table.wrapper
                :headers="[
                    __('hr.remunerations.approved_proposals.user_fullname'),
                    __('hr.remunerations.approved_proposals.user_assigments'),
                    __('hr.remunerations.approved_proposals.proposals'),
                    '',
                    '',
                    '',
                    '',
                ]"
                :pagination-links="$users->links()"
                :bordersRounded="''"
                :customClassesForTh="'!px-4 !py-4'"
                :showWithParentDivs="false"
            >
                @forelse($users as $user)
                    @php
                        $proposalsCount = $user->remunerationProposals->count();
                        $firstProposal = $user->remunerationProposals->first();
                    @endphp

                    @foreach($user->remunerationProposals as $proposal)
                        <x-data-table.row
                            :customClassesForTd="'!px-4 !py-4 align-top border-b'"
                            data-id="proposal-{{ $user->id }}"
                            :columns="$loop->first ? [
                                'user',
                                'assigments',
                                'proposal_type',
                                'proposal_reason',
                                'proposal_sum',
                                'proposal_contract',
                                'proposal_print',
                            ] : [
                                'proposal_type',
                                'proposal_reason',
                                'proposal_sum',
                                'proposal_contract',
                                'proposal_print',
                            ]"
                            :cellProps="(($proposalsCount > 1) && $loop->first) ? [
                                0 => ['rowspan' => $proposalsCount],
                                1 => ['rowspan' => $proposalsCount],
                                2 => ['class' => 'border-neutral/10'],
                                3 => ['class' => 'border-neutral/10'],
                                4 => ['class' => 'border-neutral/10 text-right'],
                                5 => ['class' => 'border-neutral/10'],
                                6 => ['class' => 'border-neutral/10'],
                                7 => ['class' => 'border-neutral/10'],
                            ] : [
                                4 => ['class' => $loop->first ? 'text-right' : ''],
                                2 => ['class' => !$loop->first ? 'text-right' : ''],
                            ]"
                        >
                            <x-slot name="user">
                                {{ $user->full_name }}
                                <br><span class="text-gray-400 text-xs">{{ $user->login }}</span>
                                <a class="text-gray-400 text-xs ms-2" href="{{ route('hr.users.show', ['type' => \App\Enums\Person\Assigment\AssigmentTypeEnum::EMPLOYEE->urlParameter(), 'user' => $user->id]) }}" target="_blank">
                                    <i class="hrms hrms-external-link"></i>
                                </a>
                            </x-slot>
                            <x-slot name="assigments">
                                <x-vermont.user-assigments :componentAssigments="$user->assigments" :id="'user-'.$user->id"></x-vermont.user-assigments>
                            </x-slot>
                            <x-slot name="proposal_type">
                                {{ $proposal->type->translation() }}
                                @if($proposal->is_paid_via_agreement)
                                    <span class="badge badge-soft badge-warning font-medium text-xs me-1">
                                        <i class="hrms hrms-deal-outline opacity-50"></i> {{ __('hr.remunerations.approved_proposals.proposal_is_paid_via_agreement') }}
                                    </span>
                                @endif
                                @if($proposal->is_paid_via_another_way)
                                    <span class="badge badge-soft badge-error font-medium text-xs me-1">
                                        <i class="hrms hrms-deal-outline opacity-50"></i> {{ __('hr.remunerations.approved_proposals.is_paid_via_another_way') }}
                                    </span>
                                @endif
                                @if($proposal->subtype)
                                    <br><span class="text-gray-400 text-xs">({{ $proposal->subtype->translation() }})</span>
                                @endif
                            </x-slot>
                            <x-slot name="proposal_reason">
                                <div class="relative" x-data="{ open: false }">
                                    <div class="flex justify-end ml-4">
                                        <button x-on:click="open = ! open" class="btn btn-soft btn-secondary">{{ __('hr.remunerations.approved_proposals.show_reason') }}</button>
                                    </div>
                                    <div x-show="open" id="proposals_reason_{{ $proposal->id }}" class="card absolute top-[100%] right-0 z-100 bg-base-100 w-auto">
                                        <div class="card-body p-2 min-w-60 text-sm">
                                            @if(empty($proposal->reason))
                                                <span class="italic">{{ __('hr.remunerations.approved_proposals.no_reason_filled') }}</span>
                                            @else
                                                {!! nl2br($proposal->reason) !!}
                                            @endif
                                        </div>
                                    </div>
                                </div>

                            </x-slot>
                            <x-slot name="proposal_sum">
                                @php
                                    $addition = $proposal->addition ?? 0;
                                    $total = $proposal->base + $addition;
                                @endphp
                                {{ formatMoney($total) }} <span class="text-gray-400 text-xs">{{ $proposal->currency->code }}</span>
                                @if($addition > 0)
                                    <br><span class="text-gray-400 text-xs">({{ formatMoney($proposal->base) }} + {{ formatMoney($addition) }})</span>
                                @endif
                            </x-slot>
                            <x-slot name="proposal_contract">
                                @if(empty($proposal->contract))
                                    <div class="w-full flex justify-end">
                                        @include('hr.remunerations.approved_proposals.contract_selector', ['proposal' => $proposal, 'userContracts' => $user->contracts])
                                    </div>
                                @else
                                    <div class="w-full flex justify-between">
                                        <div>
                                            {{ $proposal->contract->type->shortTranslation() }} - {{ $proposal->contract->company->code }}
                                            <span class="text-gray-400 text-xs">({{ __('hr.remunerations.approved_proposals.contract_start', ['date' => formatDate($proposal->contract->start_date)]) }})</span>
                                            <br>
                                            <span class="text-gray-400 text-xs">{{ __('hr.remunerations.approved_proposals.contract_code') }}</span> {{ $proposal->contract->payslip_code }}
                                        </div>
                                        @include('hr.remunerations.approved_proposals.contract_selector', ['proposal' => $proposal, 'userContracts' => $user->contracts])
                                    </div>
                                @endif
                            </x-slot>
                            <x-slot name="proposal_print">
                                @if($proposal->type === \App\Enums\Remuneration\ProposalType::SALARY)
                                    <a href="{{ route('hr.remunerations.approved_proposals.print', ['proposal' => $proposal->id]) }}" target="_blank" @class(["btn btn-soft btn-info", 'btn-disabled' => !$proposal->contract_id])>
                                        {{ __('hr.remunerations.approved_proposals.print') }}
                                    </a>
                                @else
                                    <span></span>
                                @endif
                            </x-slot>
                        </x-data-table.row>
                    @endforeach
                @empty
                    <x-data-table.empty-row
                        empty-table-text="{{ __('hr.remunerations.approved_proposals.no_proposals') }}"></x-data-table.empty-row>
                @endforelse
            </x-data-table.wrapper>

            <div>
                <a href="{{ route('hr.remunerations.approved_proposals.export', ['month' => $month]) }}" target="_blank" @class(["btn btn-outline btn-secondary", 'btn-disabled' => !$users->count()])>
                    <img src="{{ Vite::asset('resources/images/files/excel.svg') }}" alt="Excel" class="w-4 h-4 mr-1">
                    {{ __('hr.remunerations.approved_proposals.export.button') }}
                </a>
            </div>
        </div>
    </div>

</x-hr-layout>
