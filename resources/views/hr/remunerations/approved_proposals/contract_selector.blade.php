<div class="relative" x-data="{ open: false }">
    <div class="flex justify-end ml-4">
        @if($proposal->contract_id)
            <button x-on:click="open = ! open" class="btn btn-soft btn-warning"> {{ __('hr.remunerations.approved_proposals.contract_set_update') }}</button>
        @else
            <button x-on:click="open = ! open" class="btn btn-soft btn-error"> {{ __('hr.remunerations.approved_proposals.contract_set_new') }}</button>
        @endif
    </div>
    <div x-show="open" id="proposals_selector_{{ $proposal->id }}" class="card absolute top-[100%] right-0 z-100 bg-base-100">
        <div class="card-body p-2">
            <table class="table">
                @forelse($userContracts as $userContract)
                <tr>
                    <td>
                        {{ $userContract->type->shortTranslation() }} - {{ $userContract->company->code }}
                        <span class="text-gray-400 text-xs">({{ __('hr.remunerations.approved_proposals.contract_start', ['date' => formatDate($userContract->start_date)]) }})</span>
                        <br>
                        <span class="text-gray-400 text-xs">{{ __('hr.remunerations.approved_proposals.contract_code') }}</span> {{ $userContract->payslip_code }}
                    </td>
                    <td>
                        <a href="{{ route('hr.remunerations.approved_proposals.update_proposal_contract', ['proposal' => $proposal->id, 'contract' => $userContract->id]) }}" class="btn btn-soft btn-success">
                            {{ __('hr.remunerations.approved_proposals.contract_set') }}
                        </a>
                    </td>
                </tr>
                @empty
                    <tr>
                        <td>{{ __('hr.remunerations.approved_proposals.contract_none') }}</td>
                    </tr>
                @endforelse
            </table>
        </div>
    </div>
</div>
