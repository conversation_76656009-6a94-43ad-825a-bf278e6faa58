<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.remunerations.title')"
                              :link="route('hr.remunerations.proposal_subtype_budgets.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.remunerations.proposal_subtype_budgets.title', ['month' => $month->format('m/Y')])"
                               :link="route('hr.remunerations.proposal_subtype_budgets.index', ['month' => $month->toDateString()])"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full flex items-center justify-center">
        <div class="border rounded-md text-center font-bold text-xl inline-flex justify-between items-center p-0.5">
            <a href="{{ route('hr.remunerations.proposal_subtype_budgets.index', ['month' => $month->subMonth()->toDateString()]) }}"
               class="btn btn-secondary btn-soft py-3 bg-base-200/50 hover:bg-base-300/20">
                <i class="hrms hrms-chevron-big-left text-secondary"></i>
            </a>
            <span class="font-bold flex items-center gap-2 px-3">
                <img src="{{ Vite::asset('resources/images/general/cal.svg') }}" alt="Calendar" class="w-5 h-5">
                {{ $month->format('m/Y') }}
            </span>
            <a href="{{ route('hr.remunerations.proposal_subtype_budgets.index', ['month' => $month->addMonth()->toDateString()]) }}"
               class="btn btn-secondary btn-soft py-3 bg-base-200/50 hover:bg-base-300/20">
                <i class="hrms hrms-chevron-big-right text-secondary"></i>
            </a>
        </div>
    </div>
    <div>
        @include('hr.remunerations.parts.navigation')

        <div class="bg-base-100 p-3 mb-3 w-full">
            <div>
                <x-data-table.wrapper
                    :headers="[
                    __('hr.remunerations.proposal_subtype_budgets.centre'),
                    __('hr.remunerations.proposal_subtype_budgets.subtype'),
                    __('hr.remunerations.proposal_subtype_budgets.budget'),
                    __('hr.remunerations.proposal_subtype_budgets.currency'),
                    __('hr.remunerations.proposal_subtype_budgets.updated_at'),
                ]"
{{--                    :pagination-links="$budgets->links()"--}}
                    :bordersRounded="''"
                    :customClassesForTh="'!px-4 !py-4'"
                    :showWithParentDivs="false"
                >
                    @forelse($budgets as $budget)
                        <x-data-table.row :customClassesForTd="'!px-4 !py-4 align-top border-b'" data-id="budget-{{ $budget->id }}" :columns="[
                            'centre',
                            $budget->subtype->translation(),
                            formatMoney($budget->budget),
                            $budget->currency->code,
                            formatDateTime($budget->updated_at),
                        ]">
                            <x-slot name="centre">
                                {{ $budget->code }}
                                <br><span class="text-gray-400 text-xs">{{ $budget->name }}</span>
                            </x-slot>
                        </x-data-table.row>
                    @empty
                        <x-data-table.empty-row
                            empty-table-text="{{ __('hr.remunerations.proposal_subtype_budgets.no_budgets') }}"></x-data-table.empty-row>
                    @endforelse
                </x-data-table.wrapper>
            </div>
        </div>
    </div>
</x-hr-layout>
