<h5 class="text-base-content text-lg mb-3 flex justify-between">
    {{ __('hr.users.assigments.title') }}

    <a class="btn btn-success btn-outline p-2 !py-1" href="{{ route('hr.users.assigments.create', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user->id]) }}">
        {{ __('main.add') }}
    </a>
</h5>

<x-data-table.wrapper
    :showWithParentDivs="false"
    :headers="$assigmentsHeaders"
    :pagination-links="$assigments->links()"
    :bordersRounded="''"
    :customClassesForTh="'!px-4 !py-4'"
>
    @forelse($assigments as $assigment)
        <x-data-table.row :customClassesForTd="'!px-4 !py-4'" data-id="assigment-{{ $assigment->id }}" :columns="[
            'assigment',
            $assigment->type->translation(),
            $assigment->status->translation(),
            formatDate($assigment->valid_from_date).(is_null($assigment->valid_to_date) ? '' : ' - '.formatDate($assigment->valid_to_date)),
            'actions',
       ]">
            <x-slot name="assigment">
                {{ $assigment->position->name_hr }}
                @if(!is_null($assigment->centre_id))
                    <br>{{ $assigment->centre->code }} ({{ $assigment->centre->name }})
                @endif
                <br>
                @if($assigment->is_from_hrms1)
                    <span class="badge badge-outline badge-success me-2">HRMS1</span>
                @endif
                @if($assigment->is_primary)
                    <span class="badge badge-outline badge-warning me-2">{{ __('hr.users.assigments.is_primary') }}</span>
                @endif
            </x-slot>
            <x-slot name="is_from_hrms1">
                <span @class(["badge badge-outline", "badge-success" => $assigment->is_from_hrms1, "badge-error" => !$assigment->is_from_hrms1])>
                    {{ __('main.'.($assigment->is_from_hrms1 ? 'yes' : 'no')) }}
                </span>
            </x-slot>
            <x-slot name="actions">
                <a class="btn btn-outline" href="{{ route('hr.users.assigments.edit', ['type' => $currentAssigmentType->urlParameter(), 'user' => $assigment->user_id, 'assigment' => $assigment->id]) }}">
                    <i class="hrms hrms-edit-text-outline"></i>
                </a>
            </x-slot>
        </x-data-table.row>
    @empty
        <x-data-table.empty-row
            empty-table-text="{{ __('hr.users.assigments.no_assigments') }}"></x-data-table.empty-row>
    @endforelse
</x-data-table.wrapper>
