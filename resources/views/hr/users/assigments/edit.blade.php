<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.users.'.$currentAssigmentType->urlParameter().'.title')" :link="route('hr.users.index', ['type' => $currentAssigmentType->urlParameter()])"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="$user->full_name" :link="route('hr.users.show', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user])"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.users.assigments.edit.'.(empty($assigment?->centre) ? 'title_without_centre' : 'title'), ['position' => $assigment->position->name, 'centre' => $assigment->centre->name ?? '?'])" :link="route('hr.users.assigments.edit', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user, 'assigment' => $assigment])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="bg-base-100 shadow-sm sm:rounded-lg">
                <div class="p-6 text-base-content">
                    <form action="{{ route('hr.users.assigments.update', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user, 'assigment' => $assigment]) }}" class="flex flex-col gap-4 p-1" method="POST">

                        <h3 class="text-xl">
                            {{ __('hr.users.assigments.edit.title', ['position' => $assigment->position->name, 'centre' => $assigment->centre->name ?? '?']) }}
                        </h3>

                        @csrf
                        @method('PATCH')

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div class="">
                                <label for="position_id" class="label-text">
                                    {{ __('hr.users.assigments.edit.position') }}
                                </label>
                                <universal-select2
                                    search-property="form_select_option"
                                    input-property="form_select_value"
                                    name="position_id"
                                    :options="{{json_encode($allPositions)}}"
                                    custom-classes="@error('position_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                    :selected="{{ old('position_id', $assigment->position_id) }}"
                                >
                                </universal-select2>
                                @error('position_id')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>
                            <div class="">
                                <div>
                                    <label for="centre_id" class="label-text">
                                        {{ __('hr.users.assigments.edit.centre') }}
                                    </label>
                                    <universal-select2
                                        search-property="form_select_option"
                                        input-property="form_select_value"
                                        name="centre_id"
                                        :options="{{json_encode($allCentres)}}"
                                        custom-classes="@error('centre_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                        :selected="{{ old('centre_id', $assigment->centre_id) }}"
                                    >
                                    </universal-select2>
                                    @error('centre_id')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>
                                <div class="mt-4">
                                    <label for="country_id" class="label-text">
                                        {{ __('hr.users.assigments.edit.country') }}
                                    </label>
                                    <x-flyonui.toast color="warning" class="text-xs p-2 mb-2">
                                        {{ __('hr.users.assigments.edit.country_info') }}
                                    </x-flyonui.toast>
                                    <universal-select2
                                        search-property="form_select_option"
                                        input-property="form_select_value"
                                        name="country_id"
                                        :options="{{json_encode($allCountries)}}"
                                        custom-classes="@error('country_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                        :selected="{{ old('country_id', $assigment->country_id) }}"
                                    >
                                    </universal-select2>
                                    @error('country_id')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">

                            <div class="">
                                <label for="valid_from_date" class="label-text">
                                    {{ __('hr.users.assigments.edit.valid_from_date') }}
                                </label>
                                <input type="date" id="valid_from_date" name="valid_from_date" class="input @error('valid_from_date') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ old('valid_from_date', $assigment->valid_from_date) }}">
                                @error('valid_from_date')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>

                            <div class="">
                                <label for="valid_to_date" class="label-text">
                                    {{ __('hr.users.assigments.edit.valid_to_date') }}
                                </label>
                                <input type="date" id="valid_to_date" name="valid_to_date" class="input @error('valid_to_date') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ old('valid_to_date', $assigment->valid_to_date) }}">
                                @error('valid_to_date')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>

                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div class="">
                                <label for="type" class="label-text">
                                    {{ __('hr.users.assigments.edit.type') }}
                                </label>
                                <universal-select2
                                    search-property="form_select_option"
                                    input-property="form_select_value"
                                    name="type"
                                    :options="{{json_encode($allTypes)}}"
                                    custom-classes="@error('type') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                    :selected="{{ old('type', $assigment->type) }}"
                                >
                                </universal-select2>
                                @error('type')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>
                            <div class="">
                                <label for="status" class="label-text">
                                    {{ __('hr.users.assigments.edit.status') }}
                                </label>
                                <universal-select2
                                    search-property="form_select_option"
                                    input-property="form_select_value"
                                    name="status"
                                    :options="{{json_encode($allStatuses)}}"
                                    custom-classes="@error('status') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                    :selected="{{ old('status', $assigment->status) }}"
                                >
                                </universal-select2>
                                @error('status')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button class="btn btn-outline">{{ __('main.save') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-hr-layout>
