<form action="{{ route('hr.users.contracts.update', ['type' => $currentAssigmentType->urlParameter(), 'user' => $contract->user_id, 'contract' => $contract->id]) }}" class="flex flex-col gap-4 p-1" method="POST">

    <h3 class="text-xl">
        {{ __('hr.users.contracts.edit.title', ['type' => $contract->type->shortTranslation(), 'date' => formatDate($contract->start_date)]) }}
    </h3>

    <x-flyonui.toast color="warning">
        {{ __('hr.users.contracts.edit.temporary_disabled') }}
    </x-flyonui.toast>

    @csrf
    @method('PATCH')

    <div class="">
        <label for="status" class="label-text">
            {{ __('hr.users.contracts.edit.status') }}
        </label>
        <select id="status" name="status" class="input @error('status') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" disabled>
            @foreach(\App\Enums\Person\Contract\ContractStatusEnum::cases() as $status)
                <option value="{{ $status->value }}" @selected($status === $contract->status)>{{ $status->translation() }}</option>
            @endforeach
        </select>
        @error('status')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-status-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="company_id" class="label-text">
            {{ __('hr.users.contracts.edit.company') }}
        </label>
        <select id="company_id" name="company_id" class="input @error('company_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" disabled>
            @foreach($allCompanies as $company)
                <option value="{{ $company->id }}" @selected($company->id === $contract->company_id)>{{ $company->name }}</option>
            @endforeach
        </select>
        @error('code')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-company_id-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="type" class="label-text">
            {{ __('hr.users.contracts.edit.type') }}
        </label>
        <select id="type" name="type" class="input @error('type') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" disabled>
            @foreach(\App\Enums\Person\Contract\ContractTypeEnum::cases() as $type)
                <option value="{{ $type->value }}" @selected($type === $contract->type)>{{ $type->translation() }}</option>
            @endforeach
        </select>
        @error('type')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-type-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="is_primary" class="label-text">
            {{ __('hr.users.contracts.edit.is_primary') }}
        </label>
        <select id="is_primary" name="is_primary" class="input @error('is_primary') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" disabled>
            <option value="1" @selected($contract->is_primary)>{{ __('main.yes') }}</option>
            <option value="0" @selected(!$contract->is_primary)>{{ __('main.no') }}</option>
        </select>
        @error('is_primary')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-is_primary-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="percentage" class="label-text">
            {{ __('hr.users.contracts.edit.percentage') }}
        </label>
        <input type="number" min="0" step="0.01" id="percentage" name="percentage" class="input @error('percentage') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $contract->percentage * 1 }}" disabled>
        @error('percentage')
        <p class="text-sm text-red-600 mt-2" id="hs-percentage-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    @foreach(['sign_date', 'start_date', 'trial_date', 'definite_date', 'end_notice_date', 'end_date'] as $dateInputName)
        <div class="">
            <label for="{{ $dateInputName }}" class="label-text">
                {{ __('hr.users.contracts.edit.'.$dateInputName) }}
            </label>
            <input type="date" id="{{ $dateInputName }}" name="{{ $dateInputName }}" class="input @error($dateInputName) focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $contract->{$dateInputName} }}" disabled>
            @error($dateInputName)
            <p class="text-sm text-red-600 mt-2" id="hs-{{ $dateInputName }}-name-error-helper">
                {{ $message }}
            </p>
            @enderror
        </div>
    @endforeach

    <div class="">
        <label for="payslip_code" class="label-text">
            {{ __('hr.users.contracts.edit.payslip_code') }}
        </label>
        <input type="text" id="payslip_code" name="payslip_code" class="input @error('payslip_code') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $contract->payslip_code }}" disabled>
        @error('payslip_code')
        <p class="text-sm text-red-600 mt-2" id="hs-payslip_code-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>


    <div class="flex justify-end">
        {{--        <button class="btn btn-outline">{{ __('main.save') }}</button>--}}
    </div>
</form>
