<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.users.'.$currentAssigmentType->urlParameter().'.title')" :link="route('hr.users.index', ['type' => $currentAssigmentType->urlParameter()])"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="$user->full_name" :link="route('hr.users.show', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user])"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.users.contracts.edit.title', ['type' => $contract->type->shortTranslation(), 'date' => formatDate($contract->start_date)])" :link="route('hr.users.contracts.edit', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user, 'contract' => $contract])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="bg-base-100 shadow-sm sm:rounded-lg">
                <div class="p-6 text-base-content">
                    <div>
{{--                        <div>--}}
{{--                            <h5 class="text-base-content text-xl">--}}
{{--                                {{ __('hr.users.contracts.edit.title', ['type' => $contract->type->shortTranslation(), 'date' => formatDate($contract->start_date)]) }}--}}
{{--                            </h5>--}}
{{--                        </div>--}}
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="col-span-2 lg:col-span-1">
                                @include('hr.users.contracts.parts.edit.form')
                            </div>
                            <div class="col-span-2 lg:col-span-1">
                                @include('hr.users.contracts.parts.edit.salary')
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-hr-layout>
