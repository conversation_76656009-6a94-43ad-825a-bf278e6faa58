<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.users.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.users.'.$currentAssigmentType->urlParameter().'.title')"
                               :link="route('hr.users.index', ['type' => $currentAssigmentType->urlParameter()])"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full min-h-[86px]">
        <universal-filter
            :filter-inputs="{{json_encode($filterInputs)}}"
            :filter-data="{{json_encode($filterData)}}"
        ></universal-filter>
    </div>

    <div>
        @include('hr.users.index.parts.navigation')
        <x-data-table.wrapper
            :headers="$tableHeaders"
            :pagination-links="$users->links()"
            :bordersRounded="''"
            :customClassesForTh="'!px-4 !py-4'"
        >
            @forelse($users as $user)
                <x-data-table.row :customClassesForTd="'!px-4 !py-4'" data-id="proposal-{{ $user->id }}" :columns="[
                    $user->login,
                    $user->full_name,
                    'assigments',
                    'actions',
               ]">
                    <x-slot name="assigments">
                        <x-vermont.user-assigments :componentAssigments="$user->assigments" :id="'user-'.$user->id" :hr="true"></x-vermont.user-assigments>
                    </x-slot>
                    <x-slot name="actions">
                        <a class="btn btn-outline" href="{{ route('hr.users.show', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user->id]) }}">
                            {{ __('main.show') }}
                        </a>
                    </x-slot>
                </x-data-table.row>
            @empty
                <x-data-table.empty-row
                    empty-table-text="{{ __('employee.remunerations.proposals.list.no_proposals') }}"></x-data-table.empty-row>
            @endforelse
        </x-data-table.wrapper>
    </div>
</x-hr-layout>
