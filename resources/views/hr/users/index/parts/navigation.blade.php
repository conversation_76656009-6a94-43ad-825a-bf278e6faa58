<div class="sm:hidden" x-data="{ }">
    <label for="hs-card-nav" class="sr-only">Select a nav</label>
    <select x-on:change="window.location = $event.target.value" name="hs-card-nav" id="hs-card-nav" class="select rounded-b-none block w-full border-t-0 border-x-0 border-gray-300 rounded-t-xl focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none">
        @foreach(\App\Enums\Person\Assigment\AssigmentTypeEnum::cases() as $assType)
            <option value="{{ route('hr.users.index', ['type' => $assType->urlParameter()]) }}" {{ $currentAssigmentType === $assType ? 'selected' : '' }}>{{ $assType->tabTranslation() }}</option>
        @endforeach
    </select>
</div>
<!-- End Select (Mobile only) -->

<!-- Nav (Device only) -->
<div class="hidden sm:block">
    <nav class="relative z-0 flex border-b divide-x">
        @foreach(\App\Enums\Person\Assigment\AssigmentTypeEnum::cases() as $assType)
            <a @class([
                'group',
                'relative',
                'min-w-0',
                'flex-1',
                'bg-base-100',
                'py-4',
                'px-4',
                'border-b-4' => ($currentAssigmentType === $assType),
                'border-b-pink-400' => ($currentAssigmentType === $assType),
                'text-gray-500' =>  ($currentAssigmentType !== $assType),
                'text-base-content' => ($currentAssigmentType === $assType),
                'hover:text-gray-700' => ($currentAssigmentType !== $assType),
                'rounded-tl-xl' => ($loop->first),
                'rounded-tr-xl' => ($loop->last),
                'text-sm',
                'font-medium',
                'text-center',
                'overflow-hidden',
                'focus:outline-none',
                'focus:bg-purple-50',
                'focus:z-10',
            ]) href="{{ route('hr.users.index', ['type' => $assType->urlParameter()]) }}">
                {{ $assType->tabTranslation() }}
                <span class="badge badge-soft !bg-pink-400 !text-white badge-primary badge-sm">
                    {{ $typesCounts->where('type', $assType->value)->first()?->count ?? 0 }}
                </span>
            </a>
        @endforeach
    </nav>
</div>
