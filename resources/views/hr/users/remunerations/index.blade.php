<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.users.'.$currentAssigmentType->urlParameter().'.title')"
                              :link="route('hr.users.index', ['type' => $currentAssigmentType->urlParameter()])"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.users.'.$currentAssigmentType->urlParameter().'.show.title', ['name' => $user->full_name])" :link="route('hr.users.show', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user])"></x-breadcrumbs.parent>
        <x-breadcrumbs.current
            :title="__('hr.users.renumerations.title')"
            :link="route('hr.users.remunerations.index', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        @include('hr.users.show.parts.navigation')

        <div class="mx-auto">
            <div class="bg-base-100 shadow-sm sm:rounded-b-lg">
                <div class="p-6 text-base-content">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div>
                            @if($approverViaHierarchy || $approverViaPermission)
                                <h3 class="text-xl text-green-600">{{ __('hr.users.renumerations.proposer.title_yes') }}</h3>

                                <ul class="list-inside list-disc mt-4">
                                    @if($approverViaHierarchy)
                                        <li class="mb-2">{{ __('hr.users.renumerations.proposer.proposer_via_hierarchy') }}</li>
                                    @endif
                                    @if($approverViaPermission)
                                        <li class="mb-2">
                                            {{ __('hr.users.renumerations.proposer.proposer_via_permission') }}

                                            <form action="{{ route('hr.users.remunerations.proposer_permission.destroy', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user]) }}" method="post" class="ml-4 mt-2">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-error btn-outline">
                                                    {{ __('hr.users.renumerations.proposer.proposer_permission_remove') }}
                                                </button>
                                            </form>
                                        </li>
                                    @endif
                                </ul>

                                <div class="mt-6">
                                    <h4 class="text-lg">{{ __('hr.users.renumerations.proposer.approvers_via_hierarchy') }}</h4>

                                    <div class="overflow-hidden">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <tbody class="divide-y divide-gray-200">
                                            @foreach($parentUsers as $parentUser)
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ $parentUser->full_name }}</td>
                                                </tr>
                                            @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            @else
                                <div>
                                    <h3 class="text-xl text-red-600 mb-6">{{ __('hr.users.renumerations.proposer.title_no') }}</h3>

                                    <form action="{{ route('hr.users.remunerations.proposer_permission.store', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user]) }}" method="post">
                                        @csrf
                                        <button type="submit" class="btn btn-defualt">
                                            {{ __('hr.users.renumerations.proposer.proposer_permission_add') }}
                                        </button>
                                    </form>
                                </div>
                            @endif

                            <div class="mt-6">
                                <h3 class="text-xl">{{ __('hr.users.renumerations.proposal_additional_approvers.title') }}</h3>

                                <div class="overflow-hidden">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <tbody class="divide-y divide-gray-200">
                                        @forelse($user->remunerationProposalAdditionalApprovers as $approver)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ $approver->full_name }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">
                                                    <form action="{{ route('hr.users.remunerations.proposal_approvers.destroy', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user, 'approver' => $approver->id]) }}" method="post">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-error btn-outline">
                                                            {{ __('hr.users.renumerations.proposal_additional_approvers.remove') }}
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="99" class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('hr.users.renumerations.proposal_additional_approvers.none') }}</td>
                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>
                                </div>
                                @if($approverViaHierarchy || $approverViaPermission)
                                    <div class="mt-4">
                                        <h3 class="text-left">{{ __('hr.users.renumerations.proposal_additional_approvers.add') }}</h3>

                                        <form method="post" action="{{ route('hr.users.remunerations.proposal_approvers.store', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user]) }}" class="mt-6 space-y-6">
                                            @csrf

                                            <universal-select2
                                                search-property="select_option"
                                                input-property="id"
                                                name="approver_id"
                                                all-option
                                                :options="{{json_encode($allUsers)}}"
                                                :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                            ></universal-select2>

                                            <div class="flex items-center gap-4">
                                                <button class="btn btn-default" type="submit">{{ __('hr.users.renumerations.proposal_additional_approvers.add') }}</button>
                                            </div>
                                        </form>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div class="mb-6">
                                <h3 class="text-lg">{{ __('hr.users.renumerations.proposal_additional_approvers.proposers') }}</h3>

                                <div class="overflow-hidden">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <tbody class="divide-y divide-gray-200">
                                        @forelse($user->remunerationProposalAdditionalProposers as $approver)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ $approver->full_name }}</td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="99" class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('hr.users.renumerations.proposal_additional_approvers.proposers_none') }}</td>
                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="mb-6">
                                <h4 class="text-lg">{{ __('hr.users.renumerations.proposer.children_via_hierarchy') }}</h4>

                                <div class="overflow-hidden">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <tbody class="divide-y divide-gray-200">
                                        @forelse($childrenPositionsViaHierarchy as $position)
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ $position }}</td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">
                                                    {{ __('hr.users.renumerations.proposer.children_via_hierarchy_none') }}
                                                </td>
                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-hr-layout>
