<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.users.'.$currentAssigmentType->urlParameter().'.title')" :link="route('hr.users.index', ['type' => $currentAssigmentType->urlParameter()])"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.users.'.$currentAssigmentType->urlParameter().'.show.title', ['name' => $user->full_name])" :link="route('hr.users.show', ['type' => $currentAssigmentType->urlParameter(), 'user' => $user])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        @include('hr.users.show.parts.navigation')

        <div class="mx-auto">
            <div class="bg-base-100 shadow-sm sm:rounded-b-lg">
                <div class="p-6 text-base-content">
                    <div>
                        <h5 class="text-base-content text-xl mb-4">
                            {{ __('hr.users.'.$currentAssigmentType->urlParameter().'.show.title', ['name' => $user->full_name]) }}
                        </h5>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="col-span-2 lg:col-span-1">
                            @include('hr.users.assigments.index')
                        </div>
                        <div class="col-span-2 lg:col-span-1">
                            @include('hr.users.contracts.index')
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-hr-layout>
