<div class="sm:hidden" x-data="{ }">
    <label for="hs-card-nav" class="sr-only">Select a nav</label>
    <select x-on:change="window.location = $event.target.value" name="hs-card-nav" id="hs-card-nav" class="select rounded-b-none block w-full border-t-0 border-x-0 border-gray-300 rounded-t-xl focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none">
        @foreach($currentAssigmentType->hrUserShowTabs() as $tab)
            <option value="{{ $tab->getRoute($currentAssigmentType->urlParameter(), $user->id) }}" {{ $currentShowTab === $tab ? 'selected' : '' }}>{{ $tab->translation() }}</option>
        @endforeach
    </select>
</div>
<!-- End Select (Mobile only) -->

<!-- Nav (Device only) -->
<div class="hidden sm:block">
    <nav class="relative z-0 flex border-b divide-x">
        @foreach($currentAssigmentType->hrUserShowTabs() as $tab)
            <a @class([
                'group',
                'relative',
                'min-w-0',
                'flex-1',
                'bg-base-100',
                'py-4',
                'px-4',
                'border-b-4' => ($currentShowTab === $tab),
                'border-b-pink-400' => ($currentShowTab === $tab),
                'text-gray-500' =>  ($currentShowTab !== $tab),
                'text-base-content' => ($currentShowTab === $tab),
                'hover:text-gray-700' => ($currentShowTab !== $tab),
                'rounded-tl-xl' => ($loop->first),
                'rounded-tr-xl' => ($loop->last),
                'text-sm',
                'font-medium',
                'text-center',
                'overflow-hidden',
                'focus:outline-none',
                'focus:bg-purple-50',
                'focus:z-10',
            ]) href="{{ $tab->getRoute($currentAssigmentType->urlParameter(), $user->id) }}">
                {{ $tab->translation() }}
            </a>
        @endforeach
    </nav>
</div>
