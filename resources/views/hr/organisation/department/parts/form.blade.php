<form class="flex flex-col gap-4 p-1" method="post" action="{{ route('hr.organisation.departments.update', ['department' => $department->id]) }}">

    <h3 class="text-xl">{{ __('hr.organisation.department.edit.title', ['department' => $department->name]) }}</h3>

    <x-flyonui.toast :dismisable="false" color="warning">
        {{ __('hr.organisation.department.edit.disabled') }}
    </x-flyonui.toast>

    @csrf

    <div class="">
        <label for="code" class="label-text">
            {{ __('hr.organisation.department.edit.code') }}
        </label>
        <input type="text" id="code" name="code" class="input @error('code') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $department->code }}" disabled>
        @error('code')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="name" class="label-text">
            {{ __('hr.organisation.department.edit.name') }}
        </label>
        <input type="text" id="name" name="name" class="input @error('name') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $department->name }}" disabled>
        @error('name')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>


    <div class="">
        <label for="is_active" class="label-text">
            {{ __('hr.organisation.department.edit.is_active') }}
        </label>
        <select id="is_active" name="is_active" class="input @error('is_active') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror">
            <option value="1" @selected($department->is_active)>{{ __('main.yes') }}</option>
            <option value="0" @selected(!$department->is_active)>{{ __('main.no') }}</option>
        </select>
        @error('is_active')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="flex justify-end">
        <button class="btn btn-outline">{{ __('main.save') }}</button>
    </div>
</form>
