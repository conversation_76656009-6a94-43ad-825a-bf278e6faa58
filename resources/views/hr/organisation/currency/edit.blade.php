<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.organisation.title')"
                              :link="route('hr.organisation.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.organisation.currency.title')"
                              :link="route('hr.organisation.currencies.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="$currency->name.' ('.$currency->code.')'"
                               :link="route('hr.organisation.currencies.edit', ['currency' => $currency->id])"></x-breadcrumbs.current>
    </x-slot>

    <div class="py-0">
        <div class="mx-auto">
            <div class="overflow-hidden shadow-sm sm:rounded-lg">
                <div class="">
                    <div class="flex flex-col bg-base-100 border shadow-sm rounded-xl">
                        <div class="p-4 md:py-7 md:px-5">
                            <div class="flex flex-col">
                                <div class="-m-1.5 overflow-x-auto">
                                    <div class="p-1.5 min-w-full inline-block align-middle">
                                        <div class="overflow-hidden">
                                            <div class="grid grid-cols-2 gap-4">
                                                <div class="col-span-2 md:col-span-2">
                                                    <form class="flex flex-col gap-4 p-1" method="post" action="{{ route('hr.organisation.currencies.update', ['currency' => $currency->id]) }}">
                                                        <h3 class="text-xl">{{ __('hr.organisation.currency.edit.title', ['currency' => $currency->name]) }}</h3>

                                                        <x-flyonui.toast :dismisable="false" color="warning">
                                                            {{ __('hr.organisation.currency.edit.disabled') }}
                                                        </x-flyonui.toast>

                                                        @csrf

                                                        <div class="">
                                                            <label for="code" class="label-text">
                                                                {{ __('hr.organisation.currency.edit.code') }}
                                                            </label>
                                                            <input type="text" id="code" name="code" class="input @error('code') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $currency->code }}" disabled>
                                                            @error('code')
                                                            <p class="text-sm text-red-600 mt-2" id="hs-validation-code-error-helper">
                                                                {{ $message }}
                                                            </p>
                                                            @enderror
                                                        </div>

                                                        <div class="">
                                                            <label for="name" class="label-text">
                                                                {{ __('hr.organisation.currency.edit.name') }}
                                                            </label>
                                                            <input type="text" id="name" name="name" class="input @error('name') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $currency->name }}" disabled>
                                                            @error('name')
                                                            <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
                                                                {{ $message }}
                                                            </p>
                                                            @enderror
                                                        </div>

                                                        <div class="">
                                                            <label for="symbol" class="label-text">
                                                                {{ __('hr.organisation.currency.edit.symbol') }}
                                                            </label>
                                                            <input type="text" id="symbol" name="symbol" class="input @error('symbol') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $currency->symbol }}" disabled>
                                                            @error('symbol')
                                                            <p class="text-sm text-red-600 mt-2" id="hs-validation-symbol-error-helper">
                                                                {{ $message }}
                                                            </p>
                                                            @enderror
                                                        </div>


                                                        <div class="">
                                                            <label for="is_active" class="label-text">
                                                                {{ __('hr.organisation.currency.edit.is_active') }}
                                                            </label>
                                                            <select id="is_active" name="is_active" class="input @error('is_active') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror">
                                                                <option value="1" @selected($currency->is_active)>{{ __('main.yes') }}</option>
                                                                <option value="0" @selected(!$currency->is_active)>{{ __('main.no') }}</option>
                                                            </select>
                                                            @error('is_active')
                                                            <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
                                                                {{ $message }}
                                                            </p>
                                                            @enderror
                                                        </div>

                                                        <div class="flex justify-end">
                                                            <button class="btn btn-outline">{{ __('main.save') }}</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-hr-layout>
