<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.organisation.title')"
                              :link="route('hr.organisation.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.organisation.currency.title')"
                               :link="route('hr.organisation.currencies.index')"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full">
        <universal-filter
            :filter-inputs="{{json_encode($filterInputs)}}"
            :filter-data="{{json_encode($filterData)}}"
        ></universal-filter>
    </div>

    <x-simple-data-table :data-for-table="$currencies->getCollection()" :col-options="$colOptions" :pagination-links="$currencies->links()" empty-table-text="{{__('hr.organisation.currency.no_currencies')}}"
    ></x-simple-data-table>

</x-hr-layout>
