<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.organisation.title')"
                              :link="route('hr.organisation.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.organisation.centre.title')"
                               :link="route('hr.organisation.centres.index')"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full">
        <universal-filter
            :filter-inputs="{{json_encode($filterInputs)}}"
            :filter-data="{{json_encode($filterData)}}"
        ></universal-filter>
    </div>

    <x-simple-data-table :data-for-table="$centres->getCollection()" :col-options="$colOptions" :pagination-links="$centres->links()" empty-table-text="{{__('hr.organisation.centre.no_centres')}}"
    ></x-simple-data-table>

</x-hr-layout>
