<form class="flex flex-col gap-4 p-1" method="post" action="{{ route('hr.organisation.centres.update', ['centre' => $centre->id]) }}">

    <h3 class="text-xl">{{ __('hr.organisation.centre.edit.title', ['centre' => $centre->name]) }}</h3>

    <x-flyonui.toast :dismisable="false" color="warning">
        {{ __('hr.organisation.centre.edit.disabled') }}
    </x-flyonui.toast>

    @csrf

    <div class="">
        <label for="code" class="label-text">
            {{ __('hr.organisation.centre.edit.code') }}
        </label>
        <input type="text" id="code" name="code" class="input @error('code') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $centre->code }}" disabled>
        @error('code')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-code-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="name" class="label-text">
            {{ __('hr.organisation.centre.edit.name') }}
        </label>
        <input type="text" id="name" name="name" class="input @error('name') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $centre->name }}" disabled>
        @error('name')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>


    <div class="">
        <label for="is_active" class="label-text">
            {{ __('hr.organisation.centre.edit.is_active') }}
        </label>
        <select id="is_active" name="is_active" class="input @error('is_active') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror">
            <option value="1" @selected($centre->is_active)>{{ __('main.yes') }}</option>
            <option value="0" @selected(!$centre->is_active)>{{ __('main.no') }}</option>
        </select>
        @error('is_active')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-is-active-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="flex justify-end">
        <button class="btn btn-outline">{{ __('main.save') }}</button>
    </div>
</form>
