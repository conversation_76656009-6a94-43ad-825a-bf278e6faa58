<div class="flex flex-col gap-4 p-1">
    <h3 class="text-xl">{{ __('hr.organisation.centre.edit.departments.title') }}</h3>

    <x-simple-data-table :showWithParentDivs="false" :data-for-table="$centreDepartments->getCollection()" :col-options="$departmentsColOptions" :pagination-links="$centreDepartments->links()" empty-table-text="{{__('hr.organisation.centre.edit.departments.no_departments')}}"
    ></x-simple-data-table>

    <form action="{{ route('hr.organisation.centres.attach_department', ['centre' => $centre->id]) }}" class="flex flex-col gap-4 p-1" method="POST">

        <h4 class="text-lg">{{ __('hr.organisation.centre.edit.departments.attach') }}</h4>

        @csrf
        @method('PUT')

        <div class="">
            <label for="department" class="label-text">
                {{ __('hr.organisation.centre.edit.departments.name') }}
            </label>
            <select id="department" name="department" class="input @error('department') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                @foreach($allDepartments as $department)
                    <option value="{{ $department->form_select_value }}" @disabled($centreDepartmentsIds->contains($department->form_select_value))>{{ $department->form_select_option }}</option>
                @endforeach
            </select>
            @error('department')
            <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
                {{ $message }}
            </p>
            @enderror
        </div>


        <div class="flex justify-end">
            <button class="btn btn-outline">{{ __('main.save') }}</button>
        </div>
    </form>
</div>
