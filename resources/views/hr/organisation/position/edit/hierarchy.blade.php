<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.organisation.title')"
                              :link="route('hr.organisation.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.organisation.position.title')"
                              :link="route('hr.organisation.positions.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="$position->name.' ('.$position->code.')'"
                               :link="route('hr.organisation.positions.edit', ['position' => $position->id, 'tab' => $tab])"></x-breadcrumbs.current>
    </x-slot>

    <div class="">
        <div class="mx-auto">
            <div class="overflow-hidden shadow-sm sm:rounded-lg">
                <div class="">
                    <div class="flex flex-col bg-base-100 border shadow-sm rounded-xl">

                        @include('hr.organisation.position.edit.parts.navigation')

                        <div class="p-4 md:py-7 md:px-5">

                            <div class="flex flex-col">
                                <div class="-m-1.5 overflow-x-auto">
                                    <div class="p-1.5 min-w-full inline-block align-middle">
                                        <div class="overflow-hidden">
                                            <div class="grid grid-cols-4 gap-4">
                                                <div class="col-span-4 md:col-span-2">
                                                    @include('hr.organisation.position.edit.parts.hierarchy_parents')
                                                    @include('hr.organisation.position.edit.parts.hierarchy_add')
                                                </div>
                                                <div class="col-span-4 md:col-span-2">
                                                    @include('hr.organisation.position.edit.parts.hierarchy_children')
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-hr-layout>
