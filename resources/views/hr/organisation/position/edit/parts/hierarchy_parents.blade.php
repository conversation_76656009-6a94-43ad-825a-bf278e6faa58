<h3 class="text-xl pb-3">{{ __('hr.organisation.position.edit.hierarchy.parents.title') }}</h3>

<x-simple-data-table
    :showWithParentDivs="false"
    :data-for-table="$parents->getCollection()"
    :col-options="[
        __('hr.organisation.position.edit.hierarchy.parents.hierarchy_order') => ['type' => 'value', 'path_in_collection' => 'pivot.hierarchy_order'],
        __('hr.organisation.position.edit.hierarchy.parents.name') => ['type' => 'value', 'path_in_collection' => 'name_hr'],
        __('hr.organisation.position.edit.hierarchy.parents.is_pay_approver') => ['type' => 'yesno', 'path_in_collection' => 'pivot.is_pay_approver'],
        __('hr.organisation.position.edit.hierarchy.parents.is_centre_pay_approver') => ['type' => 'yesno', 'path_in_collection' => 'pivot.is_centre_pay_approver'],
        __('hr.organisation.position.edit.hierarchy.parents.is_pay_viewer') => ['type' => 'yesno', 'path_in_collection' => 'pivot.is_pay_viewer'],
        __('hr.organisation.position.edit.hierarchy.parents.show') => ['type' => 'actions', 'base_path' => route('hr.organisation.positions.index'), 'available_actions' => ['show' => true],],
        __('hr.organisation.position.edit.hierarchy.parents.delete') => ['type' => 'actions', 'base_path' => route('hr.organisation.positions.show', $position).'/detach-parent-position', 'available_actions' => ['delete' => true],],
    ]"
    :pagination-links="$parents->links()"
    empty-table-text="{{__('hr.organisation.position.edit.hierarchy.parents.no_parents')}}"
></x-simple-data-table>
