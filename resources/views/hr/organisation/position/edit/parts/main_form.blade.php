<form action="{{ route('hr.organisation.positions.update', ['position' => $position->id]) }}" class="flex flex-col gap-4 p-1" method="POST">

    <h3 class="text-xl">{{ __('hr.organisation.position.edit.main.title') }}</h3>

    <x-flyonui.toast :dismisable="false" color="warning">
        {{ __('hr.organisation.position.edit.main.temporary_disabled') }}
    </x-flyonui.toast>

    @csrf
    @method('PUT')

    <div class="">
        <label for="code" class="label-text">
            {{ __('hr.organisation.position.edit.main.code') }}
        </label>
        <input type="text" id="code" name="code" class="input @error('code') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $position->code }}" disabled>
        @error('code')
            <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
                {{ $message }}
            </p>
        @enderror
    </div>

    <div class="">
        <label for="name" class="label-text">
            {{ __('hr.organisation.position.edit.main.name') }}
        </label>
        <input type="text" id="name" name="name" class="input @error('name') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $position->name }}" disabled>
        @error('name')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="name" class="label-text">
            {{ __('hr.organisation.position.edit.main.name_hr') }}
        </label>
        <input type="text" id="name_hr" name="name_hr" class="input @error('name_hr') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" value="{{ $position->name_hr }}" disabled>
        @error('name_hr')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>

    <div class="">
        <label for="is_active" class="label-text">
            {{ __('hr.organisation.position.edit.main.is_active') }}
        </label>
        <select id="is_active" name="is_active" class="input @error('is_active') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" disabled>
            <option value="1" @selected($position->is_active)>{{ __('main.yes') }}</option>
            <option value="0" @selected(!$position->is_active)>{{ __('main.no') }}</option>
        </select>
        @error('is_active')
        <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
            {{ $message }}
        </p>
        @enderror
    </div>


    <div class="flex justify-end">
{{--        <button class="btn btn-outline">{{ __('main.save') }}</button>--}}
    </div>
</form>
