<h3 class="text-xl pb-3">{{ __('hr.organisation.position.edit.hierarchy.children.title') }}</h3>

<x-simple-data-table
    :showWithParentDivs="false"
    :data-for-table="$children->getCollection()"
    :col-options="[
        __('hr.organisation.position.edit.hierarchy.children.hierarchy_order') => ['type' => 'value', 'path_in_collection' => 'pivot.hierarchy_order'],
        __('hr.organisation.position.edit.hierarchy.children.name') => ['type' => 'value', 'path_in_collection' => 'name_hr'],
        __('hr.organisation.position.edit.hierarchy.children.is_pay_approver') => ['type' => 'yesno', 'path_in_collection' => 'pivot.is_pay_approver'],
        __('hr.organisation.position.edit.hierarchy.children.is_centre_pay_approver') => ['type' => 'yesno', 'path_in_collection' => 'pivot.is_centre_pay_approver'],
        __('hr.organisation.position.edit.hierarchy.children.is_pay_viewer') => ['type' => 'yesno', 'path_in_collection' => 'pivot.is_pay_viewer'],
        __('hr.organisation.position.edit.hierarchy.children.show') => ['type' => 'actions', 'base_path' => route('hr.organisation.positions.index'), 'available_actions' => ['show' => true],],
    ]"
    :pagination-links="$children->links()"
    empty-table-text="{{__('hr.organisation.position.edit.hierarchy.children.no_children')}}"
></x-simple-data-table>
