<div class="flex flex-col gap-4 p-1">
    <h3 class="text-xl">{{ __('hr.organisation.position.edit.sections.title') }}</h3>

    <x-simple-data-table :showWithParentDivs="false" :data-for-table="$positionSections->getCollection()" :col-options="$sectionsColOptions" :pagination-links="$positionSections->links()" empty-table-text="{{__('hr.organisation.position.edit.sections.no_sections')}}"
    ></x-simple-data-table>

    <form action="{{ route('hr.organisation.positions.attach_section', ['position' => $position->id]) }}" class="flex flex-col gap-4 p-1" method="POST">

        <h4 class="text-lg">{{ __('hr.organisation.position.edit.sections.attach') }}</h4>

        @csrf
        @method('PUT')

        <div class="">
            <label for="section" class="label-text">
                {{ __('hr.organisation.position.edit.sections.name') }}
            </label>
            <select id="section" name="section" class="input @error('section') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                @foreach($allSections as $section)
                    <option value="{{ $section->form_select_value }}" @disabled($positionSectionsIds->contains($section->form_select_value))>{{ $section->form_select_option }}</option>
                @endforeach
            </select>
            @error('section')
            <p class="text-sm text-red-600 mt-2" id="hs-validation-name-error-helper">
                {{ $message }}
            </p>
            @enderror
        </div>


        <div class="flex justify-end">
            <button class="btn btn-outline">{{ __('main.save') }}</button>
        </div>
    </form>
</div>
