<div class="sm:hidden" x-data="{ }">
    <label for="hs-card-nav" class="sr-only">Select a nav</label>
    <select x-on:change="window.location = $event.target.value" name="hs-card-nav" id="hs-card-nav" class="select rounded-b-none block w-full border-t-0 border-x-0 border-gray-300 rounded-t-xl focus:border-pink-500 focus:ring-pink-500 disabled:opacity-50 disabled:pointer-events-none">
        @foreach(['main', 'hierarchy'] as $possibleTab)
            <option value="{{ route('hr.organisation.positions.edit', ['position' => $position->id, 'tab' => $possibleTab]) }}" {{ $tab === $possibleTab ? 'selected' : '' }}>{{ __('hr.organisation.position.edit.navigation.'.$possibleTab) }}</option>
        @endforeach
    </select>
</div>
<!-- End Select (Mobile only) -->

<!-- Nav (Device only) -->
<div class="hidden sm:block">
    <nav class="relative z-0 flex border-b divide-x">
        @foreach(['main', 'hierarchy'] as $possibleTab)
            <a @class([
                'group',
                'relative',
                'min-w-0',
                'flex-1',
                'bg-base-100',
                'py-4',
                'px-4',
                'border-b-4' => ($tab === $possibleTab),
                'border-b-pink-400' => ($tab === $possibleTab),
                'text-gray-500' =>  ($tab !== $possibleTab),
                'text-base-content' => ($tab === $possibleTab),
                'hover:text-gray-700' => ($tab !== $possibleTab),
                'rounded-tl-xl' => ($loop->first),
                'rounded-tr-xl' => ($loop->last),
                'text-sm',
                'font-medium',
                'text-center',
                'overflow-hidden',
                'hover:bg-base-100/50',
                'focus:outline-none',
                'focus:bg-base-100/50',
                'focus:z-10',
            ]) @disabled(in_array($possibleTab, ['settings', 'others'])) href="{{ route('hr.organisation.positions.edit', [$position->id, $possibleTab]) }}">{{ __('hr.organisation.position.edit.navigation.'.$possibleTab) }}</a>
        @endforeach
    </nav>
</div>

<div>
    <h1 class="text-2xl font-semibold text-center w-full py-4">
        {{ __('hr.organisation.position.edit.title', ['position' => $position->name.' ('.$position->code.')']) }}
    </h1>
</div>
