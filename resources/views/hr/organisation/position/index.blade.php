<x-hr-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('hr.title')" :link="route('hr.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('hr.organisation.title')"
                              :link="route('hr.organisation.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('hr.organisation.position.title')"
                               :link="route('hr.organisation.positions.index')"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full">
        <universal-filter
            :filter-inputs="{{json_encode($filterInputs)}}"
            :filter-data="{{json_encode($filterData)}}"
        ></universal-filter>
    </div>

    <x-simple-data-table :data-for-table="$positions->getCollection()" :col-options="$colOptions" :pagination-links="$positions->links()" empty-table-text="{{__('hr.organisation.position.no_positions')}}"
    ></x-simple-data-table>

</x-hr-layout>
