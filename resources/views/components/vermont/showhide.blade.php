@props(['id' => \Illuminate\Support\Str::random('8')])
<div id="show-hide-{{ $id }}" class="collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="show-hide-collapse" >
    {{ $slot }}
</div>
<button type="button" class="collapse-toggle link link-dark inline-flex items-center" id="show-hide-collapse-{{ $id }}" aria-expanded="false" aria-controls="show-hide-{{ $id }}" data-collapse="#show-hide-{{ $id }}">
    <span class="collapse-open:hidden">{{ __('main.show') }}</span>
    <span class="collapse-open:block hidden">{{ __('main.hide') }}</span>
    <span class="icon-[tabler--chevron-down] collapse-open:rotate-180 ms-2 size-4"></span>
</button>
