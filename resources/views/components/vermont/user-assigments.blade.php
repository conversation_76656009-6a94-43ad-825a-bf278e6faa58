@props(['componentAssigments' => collect([]), 'id' => \Illuminate\Support\Str::random(8), 'hr' => false])

@if($componentAssigments->count() < 3)
    @foreach($componentAssigments as $assigment)
        @if($assigment->centre)
            {{ $assigment->centre->code }}
        @endif
        @if($assigment->centre && $assigment->position)
            -
        @endif
        @if($assigment->position)
            @if($hr && !empty($assigment->position->name_hr) && ($assigment->position->name !== $assigment->position->name_hr))
                {{ $assigment->position->name_hr }}
            @else
                {{ $assigment->position->name }}
            @endif
        @endif
        <br>
    @endforeach
@else
    @foreach($componentAssigments->take(1) as $assigment)
        @if($assigment->centre)
            {{ $assigment->centre->code }}
        @endif
        @if($assigment->centre && $assigment->position)
            -
        @endif
        @if($assigment->position)
            @if($hr && !empty($assigment->position->name_hr) && ($assigment->position->name !== $assigment->position->name_hr))
                {{ $assigment->position->name_hr }}
            @else
                {{ $assigment->position->name }}
            @endif
        @endif
        <br>
    @endforeach

    <div id="show-hide-collapse-{{ $id }}" class="collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="show-hide-collapse">
        @foreach($componentAssigments->skip(1) as $assigment)
            @if($assigment->centre)
                {{ $assigment->centre->code }}
            @endif
            @if($assigment->centre && $assigment->position)
                -
            @endif
            @if($assigment->position)
                @if($hr && !empty($assigment->position->name_hr) && ($assigment->position->name !== $assigment->position->name_hr))
                    {{ $assigment->position->name_hr }}
                @else
                    {{ $assigment->position->name }}
                @endif
            @endif
            <br>
        @endforeach
    </div>
    <button type="button" class="collapse-toggle inline-flex link items-center text-gray-400" id="show-hide-collapse-{{ $id }}" aria-expanded="false" aria-controls="show-hide-collapse-{{ $id }}" data-collapse="#show-hide-collapse-{{ $id }}">
        <span class="text-gray-400">{{ trans_choice('main.more_assigments', $componentAssigments->count() - 1, ['count' => $componentAssigments->count() - 1]) }}</span>
        <span class="icon-[tabler--chevron-down] collapse-open:rotate-180 ms-2 size-4"></span>
    </button>
@endif
