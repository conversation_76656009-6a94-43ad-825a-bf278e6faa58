@props([
    'showWithParentDivs' => true,
    'headers' => [],
    'paginationLinks' => null,
    'headersSlot' => null,
    'bordersRounded' => 'sm:rounded-lg',
    'customClassesForTh' => '',
    'tableClass' => 'min-w-full divide-y divide-gray-200',
    'emptyMessage' => null
])

@if($showWithParentDivs)
<div class="mx-auto">
    <div class="bg-base-100 shadow-sm {{ $bordersRounded }}">
        <div class="p-2 lg:p-6 text-base-content ">
            <div class="flex flex-col">
@endif
                <div class="-m-1.5 overflow-y-visible overflow-x-auto">
                    <div class="p-1.5 min-w-full inline-block align-middle">
                        <div class="border rounded-lg divide-y divide-gray-200">
                            <div class="">
                                <table class="{{ $tableClass }}">
                                    <thead class="bg-base-200">
                                    <tr>
                                        @foreach ($headers as $headerKey => $header)
                                            <x-data-table.header-cell
                                                custom-classes="{{$loop->first ? 'rounded-tl-lg' : ''}}{{$loop->last && is_null($headersSlot) ? 'rounded-tr-lg' : ''}} {{ $customClassesForTh }}">
                                                @if(isset(${$header}))
                                                    {{ ${$header} }}
                                                @else
                                                    {!! $header !!}
                                                @endif
                                            </x-data-table.header-cell>
                                        @endforeach

                                        @if($headersSlot)
                                            {{ $headersSlot }}
                                        @endif
                                    </tr>
                                    </thead>
                                    <tbody>
                                        @if(isset($slot) && !$slot->isEmpty())
                                            {{ $slot }}
                                        @elseif($emptyMessage)
                                            <tr>
                                                <td colspan="{{ count($headers) }}" class="px-6 py-4 text-center text-gray-500">
                                                    {{ $emptyMessage }}
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        @if($paginationLinks)
                            <div class="mt-4">
                                {!! $paginationLinks !!}
                            </div>
                        @endif
                    </div>
                </div>
@if($showWithParentDivs)
            </div>
        </div>
    </div>
</div>
@endif
