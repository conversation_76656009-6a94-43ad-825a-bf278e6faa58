@props(['customClasses' => '', 'colspan' => 1, 'rowspan' => 1])

<td {{
    $attributes
        ->merge(['class' => 'px-6 py-4 text-sm font-regular text-base-content
        '.$customClasses])
        ->merge(['colspan' => $colspan])
        ->merge(['rowspan' => $rowspan])
}}>
    @if(isset($slot) && !$slot->isEmpty())
        {{ $slot }}
    @else
        {!! $value ?? '&nbsp;' !!}
    @endif
</td>
