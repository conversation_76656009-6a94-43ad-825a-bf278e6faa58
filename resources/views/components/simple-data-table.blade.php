@props(['showWithParentDivs' => true, 'colOptions', 'sections', 'paginationLinks', 'emptyTableText' => ''])

@if($showWithParentDivs)
<div>
    <div class="mx-auto">
        <div class="bg-base-100  shadow-sm sm:rounded-lg">
            <div class="p-6 text-base-content ">
                <div class="flex flex-col">
                    <div class="-m-1.5 overflow-x-auto">
                        <div class="p-1.5 min-w-full inline-block align-middle">
@endif
                            <div class="border rounded-lg overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-base-200">
                                    <tr>
                                        @foreach($headerNames as $headerName => $options)
                                            <th scope="col"
                                                class="px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase">{{ $headerName }}</th>
                                        @endforeach
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                    @forelse($preparedTableData as $id => $row)
                                        <tr>
                                            @foreach($headerNames as $headerName => $options)
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-base-content {{$row[$headerName]['custom_classes'] ?? null}} {{in_array('actions',$options) ? 'max-w-32':''}}">
                                                    @if(in_array('value', $options))
                                                        {{ $row[$headerName]['value'] ?? null }}
                                                    @endif
                                                        @if(in_array('many_values', $options))
                                                            @foreach($row[$headerName]['value'] as $value)
                                                                {{$value}}
                                                                @if(!$loop->last)
                                                                    <br>
                                                                @endif
                                                            @endforeach
                                                        @endif
                                                    @if(in_array('yesno', $options))
                                                        @if($row[$headerName]['value'] === 1 || $row[$headerName]['value'] === 'yes' || $row[$headerName]['value'] === '1' || $row[$headerName]['value'] === true)
                                                            <span class="badge badge-outline badge-success">{{ __('main.yes') }}</span>
                                                        @elseif($row[$headerName]['value'] === 0 || $row[$headerName]['value'] === 'no' || $row[$headerName]['value'] === '0' || $row[$headerName]['value'] === false)
                                                            <span class="badge badge-outline badge-error">{{ __('main.no') }}</span>
                                                        @else
                                                            <span class="badge badge-outline badge-secondary">N/A</span>
                                                        @endif
                                                    @endif
                                                    @if(in_array('actions', $options))
                                                        <div class="w-full flex flex-wrap">
                                                            @foreach($row[$headerName] as $actionName => $actionData)
                                                                @continue(!$actionData['is_available'])
                                                                <x-action-button :type="$actionName"
                                                                                 :url="$actionData['url']"
                                                                                 custom-classes="mr-2"></x-action-button>
                                                            @endforeach

                                                        </div>

                                                    @endif
                                                </td>
                                            @endforeach
                                        </tr>
                                    @empty
                                        <x-data-table.empty-row empty-table-text="{{$emptyTableText}}"></x-data-table.empty-row>
                                    @endforelse
                                    </tbody>
                                </table>
                                <div class="p-6">
                                    {!! $paginationLinks !!}
                                </div>
                            </div>
@if($showWithParentDivs)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
