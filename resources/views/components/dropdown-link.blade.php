@props(['active'])
@php
    $classes = ($active ?? false)
                ? 'block w-full px-4 py-2 text-start border-l-4 border-indigo-400 text-sm leading-5 text-indigo-700 bg-indigo-50 focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition duration-150 ease-in-out'
                : 'block w-full px-4 py-2 text-start text-sm leading-5 text-gray-700  hover:bg-base-200 focus:outline-none focus:bg-base-200 transition duration-150 ease-in-out'
@endphp
<a {{ $attributes->merge(['class' => $classes]) }}>{{ $slot }}</a>
