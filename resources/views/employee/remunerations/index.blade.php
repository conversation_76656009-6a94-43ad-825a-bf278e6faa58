<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.current :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="bg-base-100  shadow-sm sm:rounded-lg">
                <div class="p-6 text-base-content ">
                    <p class="pb-4">
                        {{ __('employee.remunerations.text_1') }}
                    </p>
                    <p class="pb-4">
                        {!! __('employee.remunerations.text_2') !!}
                    </p>
                    <ul class="pl-4 pb-4">
                        <li class="list-disc">
                            {!! __('employee.remunerations.text_ul_1_li_1') !!}
                        </li>
                        <li class="list-disc">
                            {!! __('employee.remunerations.text_ul_1_li_2') !!}
                        </li>
                    </ul>
                    <p class="pb-4">
                        {!! __('employee.remunerations.text_3') !!}<br>

                        <a href="{{ route('employee.remunerations.proposals.index.table') }}" class="btn btn-outline btn-primary btn-xl my-4 mr-4">
                            {{ __('employee.remunerations.proposals.table.title') }}
                        </a>
                        <a href="{{ route('employee.remunerations.proposals.index.list') }}" class="btn btn-outline btn-primary btn-xl my-4 mr-4">
                            {{ __('employee.remunerations.proposals.list.title') }}
                        </a>
                    </p>


{{--                    {{ __('employee.remunerations.text_number', ['count' => $subordinatesCount]) }}--}}
                </div>
            </div>
        </div>
    </div>
</x-employee-layout>
