<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.proposals.title')" :link="route('employee.remunerations.proposals.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.proposals.list.title')" :link="route('employee.remunerations.proposals.index.list')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.proposals.edit.title', ['type' => $proposal->type->translation()]).' ('.$proposal->user->full_name.')'" :link="route('employee.remunerations.proposals.edit', ['proposal' => $proposal->id])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="bg-base-100  shadow-sm sm:rounded-lg">
                <div class="p-6 text-base-content ">
                    <h2 class="text-base-content text-3xl pb-6">
                        {{ __('employee.remunerations.proposals.edit.title', ['type' => $proposal->type->translation()]) }}
                    </h2>

                    <form action="{{ route('employee.remunerations.proposals.update', $proposal) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="user_id" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.users') }}
                            </label>
                            <input type="text" id="user_id" class="input @error('user_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" value="{{ $proposal->user->full_name }}" disabled>
                        </div>

                        @if($proposal->type === \App\Enums\Remuneration\ProposalType::REWARD || $proposal->type === \App\Enums\Remuneration\ProposalType::NON_FINANCIAL_REWARD)
                            <div class="mb-3">
                                <label for="subtype" class="label-text">
                                    {{ __('employee.remunerations.proposals.edit.subtype') }}
                                </label>
                                <select id="subtype" name="subtype" class="input @error('subtype') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                    <option value="">{{ __('main.form_select') }}</option>

                                    @foreach((
                                       ($proposal->type === \App\Enums\Remuneration\ProposalType::REWARD)
                                       ? \App\Enums\Remuneration\ProposalSubType::financialSubTypes()
                                       : ($proposal->type === \App\Enums\Remuneration\ProposalType::NON_FINANCIAL_REWARD ?
                                        \App\Enums\Remuneration\ProposalSubType::nonFinancialSubTypes()
                                        : [])
                                        )
                                     as $currentSubType)
                                        <option value="{{ $currentSubType->value }}" @selected($proposal->subtype === $currentSubType)>{{ $currentSubType->translation() }}</option>
                                    @endforeach
                                </select>
                                @error('subtype')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>
                        @endif

                        <div class="mb-3">
                            <label for="reason" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.reason') }}
                            </label>
                            <textarea id="reason" name="reason" class="textarea @error('reason') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror">{{ $proposal->reason }}</textarea>
                            @error('reason')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="base" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.base') }}
                            </label>
                            <input type="number" id="base" name="base" class="input @error('base') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" step="1" min="1" required value="{{ $proposal->base }}">
                            @error('base')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        @if($proposal->type === \App\Enums\Remuneration\ProposalType::SALARY)
                            <div class="mb-3">
                                <label for="addition" class="label-text">
                                    {{ __('employee.remunerations.proposals.edit.addition') }}
                                </label>
                                <input type="number" id="addition" name="addition" class="input @error('addition') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" step="1" min="0" required value="{{ $proposal->addition }}">
                                @error('addition')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>
                        @endif

                        <div class="mb-3">
                            <label for="currency_id" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.currency') }}
                            </label>
                            <select id="currency_id" name="currency_id" class="input @error('currency_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                <option value="">{{ __('main.form_select') }}</option>
                                @foreach($currencies as $currency)
                                    <option value="{{ $currency->id }}" @selected($proposal->currency_id === $currency->id)>{{ $currency->code }}</option>
                                @endforeach
                            </select>
                            @error('currency_id')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="validity" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.validity') }}
                            </label>
                            <select id="validity" name="validity" class="input @error('validity') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                @if($validityMinDate > $proposal->validity)
                                    <option value="{{ $proposal->validity }}" selected>{{ date('m / Y', $proposal->validity) }}</option>
                                @endif
                                @for($i = 0; $i < 12; $i++)
                                    <option value="{{ $monthDate = date('Y-m-01', $monthDateTime = strtotime($validityMinDate.' + '.$i.' months')) }}" @selected($proposal->validity->toDateString() === $monthDate)>{{ date('m / Y', $monthDateTime) }}</option>
                                @endfor
                            </select>
                            @error('validity')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="approver_id" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.approver') }}
                            </label>
                            @if($parentUsers->count())
                                <universal-select2
                                    search-property="full_name"
                                    input-property="id"
                                    name="approver_id"
                                    all-option
                                    :options="{{json_encode($parentUsers)}}"
                                    custom-classes="@error('approver_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                    :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                    :selected="{{ old('approver_id', ($selectedApproverId ?? $proposal->approver_id)) }}"
                                >
                                </universal-select2>
                            @else
                                <select id="approver_id" name="approver_id" class="input @error('approver_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                    <option value="">{{ __('main.form_select') }}</option>
                                </select>
                            @endif
                            @error('approver_id')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="centre_id" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.centre') }}
                            </label>
                            <universal-select2
                                search-property="full_name"
                                input-property="id"
                                name="centre_id"
                                all-option
                                :options="{{json_encode($centres)}}"
                                custom-classes="@error('centre_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                :selected="{{ $proposal->centre_id }}"
                            >
                            </universal-select2>
                            @error('centre_id')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="department_id" class="label-text">
                                {{ __('employee.remunerations.proposals.edit.department') }}
                            </label>
                            <universal-select2
                                search-property="name"
                                input-property="id"
                                name="department_id"
                                :options="{{ json_encode($departments) }}"
                                custom-classes="@error('department_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                all-option
                                :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                :selected="{{ $proposal->department_id }}"
                            >
                            </universal-select2>
                            @error('department_id')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="text-end">
                            <a class="btn btn-primary btn-outline ms-4 my-4" href="{{ url()->previous() }}">{{ __('main.back') }}</a>
                            <button type="submit" class="btn btn-primary ms-4 my-4">{{ __('main.save') }}</button>
                        </div>
                        <input type="hidden" name="type" value="{{$proposal->type}}">
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-employee-layout>
