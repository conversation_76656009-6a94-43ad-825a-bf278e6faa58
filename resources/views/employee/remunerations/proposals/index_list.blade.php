<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.proposals.title')" :link="route('employee.remunerations.proposals.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.proposals.list.title')" :link="route('employee.remunerations.proposals.index.list')"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full min-h-[86px]">
        <universal-filter
                :filter-inputs="{{json_encode($filterInputs)}}"
                :filter-data="{{json_encode($filterData)}}"
        ></universal-filter>
    </div>

    @if(($tab === 'my') && !auth()->user()->can(\App\Models\RaP\Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER))
        @if(!$selectedApproverId || $isManuallySelectedApprover)
            <div class="bg-base-100 p-6 mb-3 w-full">
                <form action="{{ route('employee.remunerations.proposals.save_approver') }}" method="post" class="flex gap-2 flex-col lg:flex-row">
                    @csrf
                    <div class="height text-lg/9">
                        {{ __('employee.remunerations.proposals.list.select_approver') }}
                    </div>

                    <div class="w-full lg:w-auto">
                        <select class="select appearance-none pl-5 pr-10" name="approver_id">
                            <option value="">{{ __('main.form_select') }}</option>
                            @foreach($parentUsers as $parentUser)
                                <option value="{{ $parentUser->id }}" @selected($parentUser->id === $selectedApproverId)>{{ $parentUser->full_name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <button class="btn btn-soft btn-primary">{{ __('employee.remunerations.proposals.list.save_approver') }}</button>
                </form>
            </div>
        @endif
    @endif

    <div>
        @include('employee.remunerations.proposals.parts.navigation')
        <x-data-table.wrapper
            :headers="$proposalsTableHeaders"
            :pagination-links="$proposals->links()"
            :bordersRounded="''"
            :customClassesForTh="'!px-4 !py-4'"
        >
            @forelse($proposals as $proposal)
                <x-data-table.row :customClassesForTd="'!px-4 !py-4'" data-id="proposal-{{ $proposal->id }}" :columns="array_merge(($tab === 'my') ? ['checkbox'] : [], [
                    $proposal->validity->format('m/Y'),
                    'created_by',
                    'user',
                    'type_subtype',
                    'value',
                    'reason',
               ], ($tab === 'completed') ? [$proposal->status->translation()] : [], $proposalsTableDynamicColumns)">
                    <x-slot name="checkbox">
                        <div class="flex items-center">
                            <input type="checkbox" name="checked_proposals[]" class="checkbox check_all" form="approve_forward_reject" value="{{ $proposal->id }}"/>
                        </div>
                    </x-slot>
                    <x-slot name="created_by">
                        @if($proposal->centre)
                            {{ $proposal->centre->code }}
                            <br>
                            <span class="text-gray-400 text-xs">{{ $proposal->centre->name }}</span>
                        @endif
                        @if($proposal->centre && $proposal->department)
                            <br>
                        @endif
                        @if($proposal->department)
                            {{ $proposal->department->name }}
                        @endif
                    </x-slot>
                    <x-slot name="user">
                        {{ $proposal->user->full_name }}
                        <br>
                        <span class="text-gray-400 text-xs">
                            <x-vermont.user-assigments :componentAssigments="$proposal->user->validAssigments" :id="'proposal-'.$proposal->id"></x-vermont.user-assigments>
                        </span>
                    </x-slot>
                    <x-slot name="type_subtype">
                        {{ $proposal->type->translation() }}
                        @if($proposal->subtype)
                            <br>
                            <span class="text-gray-400 text-xs">
                                {{  $proposal->subtype->translation() }}
                            </span>
                        @endif
                    </x-slot>
                    <x-slot name="value">
                        <div class="w-full text-right">
                            <span class="whitespace-nowrap">
                                {{ formatMoney((float) $proposal->base + (float) ($proposal->addition ?? 0)) }} <span class="text-gray-400">{{ $proposal->currency->symbol }}</span>
                            </span>
                            @if(($proposal->addition ?? 0) !== 0)
                                <span class="text-gray-400 text-xs">
                                    <br>(<span class="whitespace-nowrap">{{ formatMoney((float) $proposal->base) }}</span> + <span class="whitespace-nowrap">{{ formatMoney((float) ($proposal->addition ?? 0)) }}</span>)
                                </span>
                            @endif
                        </div>
                    </x-slot>
                    <x-slot name="current_approver">
                        {{ $proposal?->approver?->full_name }}
                    </x-slot>
                    <x-slot name="reason">
                        @if(!empty($proposal->reason))
                            @if(\Illuminate\Support\Str::length($proposal->reason) > 50)
                                {{ Illuminate\Support\Str::limit($proposal->reason ?? '', 50, '') }}
                                <div class="relative inline-block" x-data="{ open: false }">
                                    <div class="flex justify-end">
                                        <button x-on:click="open = ! open" class="btn btn-sm btn-soft btn-secondary">...</button>
                                    </div>
                                    <div x-show="open" id="proposals_reason_{{ $proposal->id }}" class="card absolute top-[100%] right-0 z-100 bg-base-100 w-auto">
                                        <div class="card-body p-2 min-w-60 text-sm">
                                            @if(empty($proposal->reason))
                                                <span class="italic">{{ __('hr.remunerations.approved_proposals.no_reason_filled') }}</span>
                                            @else
                                                {!! nl2br($proposal->reason) !!}
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @else
                                {{ $proposal->reason }}
                            @endif
                        @endif
                        @if($tab === 'my' && !empty($proposal->lastLog->note ?? null))
                            <br>
                            @if(\Illuminate\Support\Str::length($proposal->lastLog->note) > 50)
                                {{ Illuminate\Support\Str::limit($proposal->lastLog->note ?? '', 50, '') }}
                                <div class="relative inline-block" x-data="{ open: false }">
                                    <div class="flex justify-end">
                                        <button x-on:click="open = ! open" class="btn btn-sm btn-soft btn-secondary">...</button>
                                    </div>
                                    <div x-show="open" id="proposals_reason_log_{{ $proposal->id }}" class="card absolute top-[100%] right-0 z-100 bg-base-100 w-auto">
                                        <div class="card-body p-2 min-w-60 text-sm">
                                            {!! nl2br($proposal->lastLog->note) !!}
                                        </div>
                                    </div>
                                </div>
                            @else
                                <span class="text-gray-400">
                                    {{ $proposal->lastLog->note }}
                                </span>
                            @endif
                        @endif
                    </x-slot>
                    <x-slot name="payment_status">
                        @if($proposal->status === \App\Enums\Remuneration\ProposalStatus::APPROVED)
                            {{ $proposal?->payment_status?->translation() }}
                            @if($proposal->is_paid_via_agreement)
                                <br>
                                <span class="badge badge-soft badge-accent font-medium text-xxs h-auto py-1">
                                    <i class="hrms hrms-deal-outline opacity-50"></i> {{ __('employee.remunerations.proposals.list.is_paid_via_agreement') }}
                                </span>
                            @endif
                            @if($proposal->is_paid_via_another_way)
                                <br>
                                <span class="badge badge-soft badge-error font-medium text-xxs h-auto">
                                    <i class="hrms hrms-deal-outline opacity-50"></i> {{ __('hr.remunerations.approved_proposals.is_paid_via_another_way') }}
                                </span>
                            @endif
                        @endif
                    </x-slot>
                    <x-slot name="actions">
                        @include('employee.remunerations.proposals.parts.list.actions')
                    </x-slot>
                </x-data-table.row>
            @empty
                <x-data-table.empty-row empty-table-text="{{ __('employee.remunerations.proposals.list.no_proposals') }}"></x-data-table.empty-row>
            @endforelse

            @if($tab === 'my' && $proposals->count())
                    @include('employee.remunerations.proposals.parts.list.multiple')
            @endif
        </x-data-table.wrapper>

        <div class="bg-white p-6 text-right">
            <a href="{{ route('employee.remunerations.proposals.export', ['tab' => $tab]) }}" target="_blank" @class(["btn btn-outline btn-secondary", 'btn-disabled' => !$proposals->count()])>
                <img src="{{ Vite::asset('resources/images/files/excel.svg') }}" alt="Excel" class="w-5 h-5">
                {{ __('employee.remunerations.proposals.export.button') }}
            </a>
        </div>
    </div>

    @push('scripts')
        <script>
            function selectAllCheckboxes() {
                checkboxes = document.getElementsByName('checked_proposals[]');
                [...checkboxes].map((el) => {
                    el.checked = !el.checked;
                })
            }
        </script>
    @endpush
</x-employee-layout>
