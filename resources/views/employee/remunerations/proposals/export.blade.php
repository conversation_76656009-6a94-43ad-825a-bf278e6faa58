<table>
    <thead>
    <tr>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.month') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.department') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.centre') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.employee') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.type') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.subtype') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.base') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.addition') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.currency') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.status') }}</th>
        <th style="font-weight: bold;border-bottom: 1px solid black;">{{ __('employee.remunerations.proposals.export.reason') }}</th>
    </tr>
    </thead>
    <tbody>
        @foreach($proposals as $proposal)
            <tr>
                <td>
                    {{ $proposal->validity->format('m/Y') }}
                </td>
                <td>
                    @if($proposal->department)
                        {{ $proposal->department->name }}
                    @endif
                </td>
                <td>
                    @if($proposal->centre)
                        {{ $proposal->centre->code }} ({{ $proposal->centre->name }})
                    @endif
                </td>
                <td>
                   {{ $proposal->user->full_name }}
                </td>
                <td>
                    {{ $proposal->type->translation() }}
                </td>
                <td>
                    @if($proposal->subtype)
                        {{ $proposal->subtype->translation() }}
                    @endif
                </td>
                <td>
                    {{ $proposal->base }}
                </td>
                <td>
                    {{ $proposal->addition }}
                </td>
                <td>
                    {{ $proposal->currency->code }}
                </td>
                <td>
                    {{ $proposal->status->translation() }}
                </td>
                <td>
                    {{ $proposal->reason }}
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
