<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.proposals.title')" :link="route('employee.remunerations.proposals.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.proposals.create.title')" :link="route('employee.remunerations.proposals.create')"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="bg-base-100  shadow-sm sm:rounded-lg">
                <div class="p-6 text-base-content ">
                    @if(!$selectedType)
                        <p class="text-xl pb-6">
                            {{ __('employee.remunerations.proposals.create.selector') }}
                        </p>

                        <div class="card-group md:flex *:not-last:border-e *:not-last:border-base-content/25 md:max-w-full">
                            @foreach(\App\Enums\Remuneration\ProposalType::cases() as $type)
                                <a href="{{ route('employee.remunerations.proposals.create', ['type' => $type->value]) }}" class="card">
{{--                                    <figure>--}}
{{--                                        <img class="grayscale hover:grayscale-0" src="https://cdn.flyonui.com/fy-assets/components/card/image-4.png" alt="paris" />--}}
{{--                                        <img class="brightness-75 hover:brightness-100" src="https://cdn.flyonui.com/fy-assets/components/card/image-4.png" alt="paris" />--}}
{{--                                    </figure>--}}
                                    <div class="card-body">
                                        <h5 class="card-title mb-2.5">{{ $type->translation() }}</h5>
                                        <p>{{ $type->decription() }}</p>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    @else
                        <h2 class="text-base-content text-3xl pb-6">
                            {{ __('employee.remunerations.proposals.create.title_seleced', ['type' => $selectedType->translation()]) }}
                        </h2>

                        <form action="{{ route('employee.remunerations.proposals.store') }}" method="POST">
                            @csrf

                            <input type="hidden" name="type" value="{{ request()->input('type') }}">
                            <div class="mb-3">
                                <label for="users" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.users') }}
                                </label>
                                <universal-multiple-select2
                                    search-property="select_option"
                                    input-property="id"
                                    name="users"
                                    :options="{{ json_encode($allUsers) }}"
                                    custom-classes="@error('users') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                    :placeholder="{{ json_encode(__('main.form_select')) }}"
                                    :selected="{{ json_encode($allUsers->filter(function($user){return in_array($user->id, old('users', []));})) }}"
                                >
                                </universal-multiple-select2>
                                @error('users')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            @if($selectedType === \App\Enums\Remuneration\ProposalType::REWARD || $selectedType === \App\Enums\Remuneration\ProposalType::NON_FINANCIAL_REWARD)
                                <div class="mb-3">
                                    <label for="subtype" class="label-text">
                                        @if($selectedType === \App\Enums\Remuneration\ProposalType::REWARD)
                                            {{ __('employee.remunerations.proposals.create.f_subtype') }}
                                        @else
                                            {{ __('employee.remunerations.proposals.create.n_subtype') }}
                                        @endif
                                    </label>
                                    <select id="subtype" name="subtype" class="input @error('subtype') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                        <option value="">{{ __('main.form_select') }}</option>

                                        @foreach((
                                           ((int) request()->input('type') === \App\Enums\Remuneration\ProposalType::REWARD->value)
                                           ? \App\Enums\Remuneration\ProposalSubType::financialSubTypes()
                                           : ((int) request()->input('type') === \App\Enums\Remuneration\ProposalType::NON_FINANCIAL_REWARD->value ?
                                            \App\Enums\Remuneration\ProposalSubType::nonFinancialSubTypes()
                                            : [])
                                            )
                                         as $currentSubType)
                                            <option value="{{ $currentSubType->value }}" @selected((int) old('subtype') === $currentSubType->value)>{{ $currentSubType->translation() }}</option>
                                        @endforeach
                                    </select>
                                    @error('subtype')
                                        <p class="text-sm text-red-600 mt-2">
                                            {{ $message }}
                                        </p>
                                    @enderror
                                </div>
                            @endif

                            <div class="mb-3">
                                <label for="reason" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.reason') }}
                                </label>
                                <textarea id="reason" name="reason" class="textarea @error('reason') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="">{{ old('reason') }}</textarea>
                                @error('reason')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="base" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.base') }}
                                </label>
                                <input type="number" id="base" name="base" class="input @error('base') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" step="1" min="1" required value="{{ old('base') }}">
                                @error('base')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            @if((int) request()->input('type') === \App\Enums\Remuneration\ProposalType::SALARY->value)
                                <div class="mb-3">
                                    <label for="addition" class="label-text">
                                        {{ __('employee.remunerations.proposals.create.addition') }}
                                    </label>
                                    <input type="number" id="addition" name="addition" class="input @error('addition') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" placeholder="" step="1" min="0" required value="{{ old('addition') }}">
                                    @error('addition')
                                        <p class="text-sm text-red-600 mt-2">
                                            {{ $message }}
                                        </p>
                                    @enderror
                                </div>
                            @endif

                            <div class="mb-3">
                                <label for="currency_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.currency') }}
                                </label>
                                <select id="currency_id" name="currency_id" class="input @error('currency_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                    <option value="">{{ __('main.form_select') }}</option>
                                    @foreach($currencies as $currency)
                                        <option value="{{ $currency->id }}" @selected((int) old('currency_id', $selectedCurrencyId) === $currency->id)>{{ $currency->code }}</option>
                                    @endforeach
                                </select>
                                @error('currency_id')
                                <p class="text-sm text-red-600 mt-2">
                                    {{ $message }}
                                </p>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="validity" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.validity') }}
                                </label>
                                <select id="validity" name="validity" class="input @error('validity') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                    @for($i = 0; $i < 12; $i++)
                                        <option value="{{ $mothDate = date('Y-m-01', $mothDateTime = strtotime($validityMinDate.' + '.$i.' months')) }}" @selected(old('validity', $validityCurrentDate) === $mothDate)>{{ date('m / Y', $mothDateTime) }}</option>
                                    @endfor
                                </select>
                                @error('validity')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="approver_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.approver') }}
                                </label>
                                @if($parentUsers->count())
                                    <universal-select2
                                        search-property="full_name"
                                        input-property="id"
                                        name="approver_id"
                                        all-option
                                        :options="{{json_encode($parentUsers)}}"
                                        custom-classes="@error('approver_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                        :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                        :selected="{{ old('approver_id', $selectedApproverId) }}"
                                    >
                                    </universal-select2>
                                @else
                                    <select id="approver_id" name="approver_id" class="input @error('approver_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                        <option value="">{{ __('main.form_select') }}</option>
                                    </select>
                                @endif
                                @error('approver_id')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="centre_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.centre') }}
                                </label>
                                <universal-select2
                                    search-property="full_name"
                                    input-property="id"
                                    name="centre_id"
                                    all-option
                                    :options="{{json_encode($centres)}}"
                                    custom-classes="@error('centre_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                    :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                    :selected="{{ old('centre_id', $selectedCentreId) }}"
                                >
                                </universal-select2>
                                @error('centre_id')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="department_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.create.department') }}
                                </label>
                                <universal-select2
                                    search-property="name"
                                    input-property="id"
                                    name="department_id"
                                    :options="{{ json_encode($departments) }}"
                                    custom-classes="@error('department_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                    all-option
                                    :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                    :selected="{{ old('department_id', $selectedDepartmentId) }}"
                                >
                                </universal-select2>
                                @error('department_id')
                                    <p class="text-sm text-red-600 mt-2">
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <div class="text-end">
                                <a class="btn btn-primary btn-outline ms-4 my-4" href="{{ url()->previous() }}">{{ __('main.back') }}</a>
                                <button type="submit" class="btn btn-primary ms-4 my-4">{{ __('main.save') }}</button>
                            </div>

                            @if($errors->any())
                                <x-flyonui.toast :color="'error'" class="mt-4">
                                    @foreach($errors->all() as $error)
                                        {{ $error }} <br>
                                    @endforeach
                                </x-flyonui.toast>
                            @endif
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-employee-layout>
