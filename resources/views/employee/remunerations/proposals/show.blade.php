<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.proposals.title')" :link="route('employee.remunerations.proposals.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.proposals.list.title')" :link="route('employee.remunerations.proposals.index.list')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.proposals.show.title', ['type' => $proposal->type->translation()]).' ('.$proposal->user->full_name.')'" :link="route('employee.remunerations.proposals.show', ['proposal' => $proposal->id])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="bg-base-100 shadow-sm sm:rounded-lg">
                <div class="p-6 text-base-content">
                    <h2 class="text-base-content text-3xl pb-6">
                        {{ __('employee.remunerations.proposals.show.title', ['type' => $proposal->type->translation()]) }} ({{ $proposal->user->full_name }})
                    </h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div>
                            <div class="mb-3">
                                <label for="user_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.users') }}
                                </label>
                                <input type="text" id="user_id" class="input" value="{{ $proposal->user->full_name }}" disabled>
                            </div>

                            @if($proposal->type === \App\Enums\Remuneration\ProposalType::REWARD || $proposal->type === \App\Enums\Remuneration\ProposalType::NON_FINANCIAL_REWARD)
                                <div class="mb-3">
                                    <label for="subtype" class="label-text">
                                        {{ __('employee.remunerations.proposals.show.subtype') }}
                                    </label>
                                    <input type="text" id="subtype" class="input" value="{{ $proposal->subtype->translation() }}" disabled>
                                </div>
                            @endif

                            <div class="mb-3">
                                <label for="reason" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.reason') }}
                                </label>
                                <textarea type="text" id="reason" name="reason" class="textarea" disabled>{{ $proposal->reason }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="base" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.base') }}
                                </label>
                                <input type="number" id="base" name="base" class="input" placeholder="" value="{{ $proposal->base }}" disabled>
                            </div>

                            @if($proposal->type === \App\Enums\Remuneration\ProposalType::SALARY)
                                <div class="mb-3">
                                    <label for="addition" class="label-text">
                                        {{ __('employee.remunerations.proposals.show.addition') }}
                                    </label>
                                    <input type="number" id="addition" name="addition" class="input" value="{{ $proposal->addition }}" disabled>
                                </div>
                            @endif

                            <div class="mb-3">
                                <label for="currency_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.currency') }}
                                </label>
                                <input type="text" id="currency_id" name="currency_id" class="input" value="{{ $proposal->currency->code }}" disabled>
                            </div>

                            <div class="mb-3">
                                <label for="validity" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.validity') }}
                                </label>
                                <input type="text" id="validity" name="validity" class="input" value="{{ $proposal->validity->format('m / Y') }}" disabled>
                            </div>

                            <div class="mb-3">
                                <label for="approver_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.proposer') }}
                                </label>
                                <input type="text" id="proposer_id" name="proposer_id" class="input" value="{{ $proposal->proposer->full_name }}" disabled>
                            </div>

                            <div class="mb-3">
                                <label for="centre_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.centre') }}
                                </label>
                                <input type="text" id="centre_id" name="centre_id" class="input" value="{{ $proposal->centre->code }} ({{ $proposal->centre->name }})" disabled>
                            </div>

                            <div class="mb-3">
                                <label for="department_id" class="label-text">
                                    {{ __('employee.remunerations.proposals.show.department') }}
                                </label>
                                <input type="text" id="department_id" name="department_id" class="input" value="{{ $proposal->department->code }} ({{ $proposal->department->name }})" disabled>
                            </div>

                            <div class="text-end">
                                <a class="btn btn-primary btn-outline ms-4 my-4" href="{{ url()->previous() }}">{{ __('main.back') }}</a>
                            </div>
                        </div>
                        <div>
                            <h2 class="text-base-content text-3xl pb-6">
                                {{ __('employee.remunerations.proposals.show.logs.title') }}
                            </h2>

                            <x-data-table.wrapper
                                :showWithParentDivs="false"
                                :headers="$logsTableHeaders"
                                :pagination-links="$logs->links()"
                                :bordersRounded="''"
                                :customClassesForTh="'!px-4 !py-4'"
                            >
                                @forelse($logs as $log)
                                    <x-data-table.row :customClassesForTd="'!px-4 !py-4'" data-id="proposal-{{ $log->id }}" :columns="[
                                        formatDateTime($log->created_at),
                                        $log->user->full_name,
                                        $log->status->translation(),
                                        ($childUserIds->contains($log->user_id) || ($log->status === \App\Enums\Remuneration\ProposalStatus::ROLLBACKED)) ? $log->note : '',
                                    ]">

                                    </x-data-table.row>
                                @empty
                                    <x-data-table.empty-row empty-table-text="{{ __('employee.remunerations.proposals.list.no_proposals') }}"></x-data-table.empty-row>
                                @endforelse
                            </x-data-table.wrapper>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-employee-layout>
