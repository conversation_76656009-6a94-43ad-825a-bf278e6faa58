<x-data-table.row class="border-t">
    <x-data-table.cell class="!px-4 !py-4">
        <div x-data="{}">
            <span class="cursor-pointer text-3xl" x-on:click="selectAllCheckboxes()">
                <i class="hrms hrms-selectall"></i>
            </span>
        </div>
    </x-data-table.cell>
    <x-data-table.cell colspan="99">
        <form action="{{ route('employee.remunerations.proposals.change_status_for_multiple') }}" method="POST" class="flex gap-2 flex-col lg:flex-row" id="approve_forward_reject">
            @csrf
            <input type="hidden" name="approver_id" value="{{ $selectedApproverId }}">

            <div class="w-full lg:w-auto lg:flex-none">
                <label class="label-text" for="status">{{ __('employee.remunerations.proposals.list.multiple.status') }}</label>
                <select class="select appearance-none pl-5 pr-10" name="status" id="status" required>
                    @can(\App\Models\RaP\Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)
                        <option value="{{ \App\Enums\Remuneration\ProposalStatus::APPROVED->value }}">{{ __('employee.remunerations.proposals.list.multiple.approve') }}</option>
                    @else
                        @if($selectedApproverId)
                            <option value="{{ \App\Enums\Remuneration\ProposalStatus::FORWARDED->value }}">{{ __('employee.remunerations.proposals.list.multiple.forward', ['approver' => $parentUsers->where('id', $selectedApproverId)->first()?->full_name]) }}</option>
                        @else
                            <option value="" disabled>{{ __('employee.remunerations.proposals.list.multiple.forward_disabled') }}</option>
                        @endif
                    @endcan
                    <option value="{{ \App\Enums\Remuneration\ProposalStatus::REJECTED->value }}">{{ __('employee.remunerations.proposals.list.multiple.reject') }}</option>
                </select>
            </div>

            @cannot(\App\Models\RaP\Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)
                <div class="w-full lg:w-auto lg:flex-grow">
                    <label class="label-text" for="note">{{ __('employee.remunerations.proposals.list.multiple.note') }}</label>
                    <input type="text" placeholder="" class="input" id="note" name="note" />
                </div>
            @endcan

            <div class="w-full lg:w-auto lg:flex-none">
                <label for="" class="label-text hidden lg:block">&nbsp;</label>
                <button class="btn btn-soft btn-primary lg:min-w-60">{{ __('employee.remunerations.proposals.list.save_approver') }}</button>
            </div>
        </form>
        @if($errors->any())
            <x-flyonui.toast :color="'error'" class="mt-3">
                @foreach($errors->all() as $error)
                    {{ $error }}<br>
                @endforeach
            </x-flyonui.toast>
        @endif
    </x-data-table.cell>
</x-data-table.row>
