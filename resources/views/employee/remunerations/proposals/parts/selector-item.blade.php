@props(['color' => 'blue', 'active' => false])

<a href="{{ $link }}" @class([
    'flex flex-col bg-base-100 border border-neutral/20 border-t-4 shadow-2xs rounded-xl',
    'border-t-blue-600' => ($active && $color === 'blue'),
    'border-t-red-600' => ($active && $color === 'red'),
    'border-t-green-600' => ($active && $color === 'green'),
    'border-t-gray-200' => !$active,
])>
    <div class="p-4 md:p-5">
        <h3 class="text-lg font-bold text-secondary">
            {{ $title }}
        </h3>
        <span class="text-gray-400">{{ $subtitle ?? '' }}</span>
    </div>
</a>
