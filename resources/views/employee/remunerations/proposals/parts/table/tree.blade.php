<ul class="menu w-full space-y-0.5">
    <div class="border rounded-md text-center font-bold text-xl inline-flex justify-between items-center p-0.5">
        @php
            $requestParams = request()->all();
            $requestParams['validity'] = \Carbon\Carbon::createFromDate($validity)->subMonth()->format('Y-m-d');
        @endphp
        <a href="{{ route('employee.remunerations.proposals.index.table', $requestParams) }}"
           class="btn btn-secondary btn-soft py-3 bg-base-200/50 5 hover:bg-base-300/20"><i class="hrms hrms-chevron-big-left text-secondary"></i>
        </a>
        <span class="font-bold flex items-center gap-2">
            <img src="{{ Vite::asset('resources/images/general/cal.svg') }}" alt="Calendar" class="w-5 h-5">
            {{__('employee/remunerations.component.months.'.\Carbon\Carbon::createFromDate($validity)->format('m') -1)}} {{\Carbon\Carbon::createFromDate($validity)->format('Y')}}
        </span>
        @php
            $requestParams['validity'] = \Carbon\Carbon::createFromDate($validity)->addMonth()->format('Y-m-d');
        @endphp
        <a href="{{ route('employee.remunerations.proposals.index.table', $requestParams) }}"
           class="btn btn-secondary btn-soft py-3 bg-base-200/50 5 hover:bg-base-300/20"><i class="hrms hrms-chevron-big-right text-secondary"></i>
        </a>
    </div>
    @foreach($allSections as $menuSection)
        @continue(!$assigmentSections->contains($menuSection))
        <li class="space-y-0.5">
            <a class="font-bold collapse-toggle {{ $selectedSection?->id === $menuSection?->id ? 'open' : '' }} px-2 border border-transparent hover:!bg-base-200/80 hover:border-secondary/30 rounded" id="menu-section-{{ $menuSection->id }}" data-collapse="#menu-collapse-section-{{ $menuSection->id }}">
                {{ $menuSection->name }}
                <span class="icon-[tabler--chevron-down] collapse-open:rotate-180 size-4 transition-all duration-300"></span>
                @if($currentSectionCount = $activeProposalsCount->whereIn('department_id', $menuSection->departments->pluck('id'))->sum('count'))
                    <div class="flex items-center">
                        <span class="badge badge-outline badge-secondary border-neutral/15">
                            {{ $currentSectionCount }}
                        </span>
                    </div>
                @endif
            </a>
            <ul id="menu-collapse-section-{{ $menuSection->id }}" class="{{ $selectedSection?->id === $menuSection?->id ? 'open' : 'hidden' }} collapse w-auto space-y-0.5 overflow-hidden transition-[height] duration-300" aria-labelledby="menu-section-{{ $menuSection->id }}">
                @foreach($menuSection->departments as $menuDepartment)
                    @continue(!$assigmentDepartments->contains($menuDepartment))
                    <li class="space-y-0.5">
                        <a class="collapse-toggle {{ $selectedDepartment?->id === $menuDepartment?->id ? 'open' : '' }} px-2 border border-transparent hover:!bg-base-200/80 hover:border-secondary/30 rounded" id="sub-menu-section-{{ $menuSection->id }}-department-{{ $menuDepartment->id }}" data-collapse="#sub-menu-section-{{ $menuSection->id }}-department-{{ $menuDepartment->id }}-collapse" >
                            {{ $menuDepartment->name }}
                            <span class="icon-[tabler--chevron-down] collapse-open:rotate-180 size-4"></span>
                            @if($currentDepartmentCount = $activeProposalsCount->where('department_id', $menuDepartment->id)->whereIn('centre_id', $menuDepartment->centres->pluck('id'))->sum('count'))
                                <div class="flex items-center">
                                    <span class="badge badge-outline badge-secondary font-bold border-neutral/15">
                                        {{ $currentDepartmentCount }}
                                    </span>
                                </div>
                            @endif
                        </a>
                        <ul id="sub-menu-section-{{ $menuSection->id }}-department-{{ $menuDepartment->id }}-collapse" class="{{ $selectedDepartment?->id === $menuDepartment?->id ? 'open' : 'hidden' }} collapse w-auto space-y-0.5 overflow-hidden transition-[height] duration-300" aria-labelledby="sub-menu-section-{{ $menuSection->id }}-department-{{ $menuDepartment->id }}">
                            @foreach($menuDepartment->centres as $menuCentre)
                                @continue(!$assigmentCentres->contains($menuCentre))
                                @continue(!in_array($menuCentre->id, $departmentCentreValidPairs[$menuDepartment->id] ?? [], false))
                                @php
                                    $isCurrentActive = ($menuSection?->id === $selectedSection?->id && $menuDepartment?->id === $selectedDepartment?->id && $menuCentre?->id === $selectedCentre?->id);
                                    $currentCentreCount = $activeProposalsCount->where('department_id', $menuDepartment->id)->where('centre_id', $menuCentre->id)->sum('count')
                                @endphp
                                <li @class(["border bg-gray-100 rounded" => $isCurrentActive])>
                                    <a href="{{ route('employee.remunerations.proposals.index.table', ['section' => $menuSection->id, 'department' => $menuDepartment->id, 'centre' => $menuCentre->id, 'validity' => $validity]) }}" class="block py-1 px-2 border border-transparent hover:!bg-base-200/80 hover:border-secondary/30 rounded">
                                        <div class="flex justify-between align-middle w-full">
                                            <div @class(['opacity-50' => (!$isCurrentActive && !$currentCentreCount)])>
                                                <div class="font-medium">{{ $menuCentre->code }}</div>
                                                <div class="text-xs text-gray-500">{{ $menuCentre->name }}</div>
                                            </div>
                                            @if($currentCentreCount)
                                                <div class="flex items-center">
                                                    <span class="badge badge-secondary font-bold border-neutral/15 {{ $isCurrentActive ? 'opacity-100' : 'bg-base-100 text-secondary' }}">
                                                        {{ $currentCentreCount }}
                                                    </span>
                                                </div>
                                            @else
                                                @if($isCurrentActive)
                                                    <div class="flex items-center">
                                                        <span class="badge badge-secondary opacity-100">
                                                            0
                                                        </span>
                                                    </div>
                                                @endif
                                            @endif
                                        </div>
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </li>
                @endforeach
            </ul>
        </li>
    @endforeach
</ul>
