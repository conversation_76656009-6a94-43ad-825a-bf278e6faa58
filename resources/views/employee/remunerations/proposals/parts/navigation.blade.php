<div class="sm:hidden" x-data="{ }">
    <label for="hs-card-nav" class="sr-only">Select a nav</label>
    <select x-on:change="window.location = $event.target.value" name="hs-card-nav" id="hs-card-nav" class="select rounded-b-none block w-full border-t-0 border-x-0 border-gray-300 rounded-t-xl focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none">
        @foreach($allowedTabs as $possibleTab)
            <option value="{{ route('employee.remunerations.proposals.index.list', ['tab' => $possibleTab]) }}" {{ $tab === $possibleTab ? 'selected' : '' }}>{{ __('employee.remunerations.proposals.list.show_only_'.$possibleTab) }}</option>
        @endforeach
    </select>
</div>
<!-- End Select (Mobile only) -->

<!-- Nav (Device only) -->
<div class="hidden sm:block">
    <nav class="relative z-0 flex border-b divide-x">
        @foreach($allowedTabs as $possibleTab)
            <a @class([
                'group',
                'relative',
                'min-w-0',
                'flex-1',
                'bg-base-100',
                'py-4',
                'px-4',
                'border-b-4' => ($tab === $possibleTab),
                'border-b-purple-600' => ($tab === $possibleTab),
                'text-gray-500' =>  ($tab !== $possibleTab),
                'text-base-content' => ($tab === $possibleTab),
                'hover:text-gray-700' => ($tab !== $possibleTab),
                'rounded-tl-xl' => ($loop->first),
                'rounded-tr-xl' => ($loop->last),
                'text-sm',
                'font-medium',
                'text-center',
                'overflow-hidden',
                'focus:outline-none',
                'focus:bg-purple-50',
                'focus:z-10',
            ]) href="{{ route('employee.remunerations.proposals.index.list', ['tab' => $possibleTab]) }}">{{ __('employee.remunerations.proposals.list.show_only_'.$possibleTab) }}</a>
        @endforeach
    </nav>
</div>
