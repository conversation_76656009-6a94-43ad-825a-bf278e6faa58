<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.overview.title')" :link="route('employee.remunerations.overview.index')"></x-breadcrumbs.current>
    </x-slot>

    <x-slot name="submenu">
        @include('employee.remunerations.navigation', ['activeMenuItem' => 'overview'])
    </x-slot>

    <div>
        <x-data-table.wrapper
            :headers="[
                    __('employee.remunerations.employee_list.name'),
                    __('employee.remunerations.employee_list.position'),
                ]"
            :pagination-links="$users->links()"
        >
            @forelse($users as $user)
                <x-data-table.row :columns="[$user->full_name]">
                    <x-data-table.cell id="custom1">
                        @foreach($user->validAssigments as $assigment)
                            @if($assigment->centre)
                                {{ $assigment->centre->code }} ({{ $assigment->centre->name }})
                            @endif
                            @if($assigment->centre && $assigment->position)
                                -
                            @endif
                            @if($assigment->position)
                                {{ $assigment->position->name }}
                            @endif
                        @endforeach
                    </x-data-table.cell>
                </x-data-table.row>
            @empty
                <x-data-table.empty-row empty-table-text="{{__('employee.remunerations.employee_list.no_employees')}}"></x-data-table.empty-row>
            @endforelse
        </x-data-table.wrapper>
    </div>
</x-employee-layout>
