<x-employee-layout>
    <x-slot name="title">
        {{ __('employee.remunerations.salaries.plans.title') }}
    </x-slot>

    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.salaries.title')" :link="route('employee.remunerations.salaries.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.salaries.plans.title')" :link="route('employee.remunerations.salaries.plans.index')"></x-breadcrumbs.current>
    </x-slot>

    <div class="mx-auto">
        <div class="bg-base-100 shadow-sm sm:rounded-lg p-3">
            <div class="text-base-content grid grid-cols-3 gap-3">
                <div class="col-span-3 lg:col-span-2">
                    <h2 class="text-base-content text-xl pb-3">
                        {{ __('employee.remunerations.salaries.plans.title') }}
                    </h2>

                    <p class="pb-6">{{ __('employee.remunerations.salaries.plans.info') }}</p>

                    <x-data-table.wrapper
                    :headers="[
                        __('employee.remunerations.salaries.plans.user'),
                        __('employee.remunerations.salaries.plans.validity'),
                        __('employee.remunerations.salaries.plans.salary'),
                        __('employee.remunerations.salaries.plans.note'),
                        __('employee.remunerations.salaries.plans.creator'),
                        __('employee.remunerations.salaries.plans.status'),
                    ]"
                    :pagination-links="$salaryPlans->links()"
                    :bordersRounded="''"
                    :customClassesForTh="''"
                    :showWithParentDivs="false"
                >
                    @forelse($salaryPlans as $salaryPlan)
                        <x-data-table.row :customClassesForTd="''" data-id="plan-{{ $salaryPlan->id }}" :columns="[
                            'name_assigments',
                            formatDate($salaryPlan->validity),
                            'salary',
                            'note',
                            $salaryPlan->creator->full_name,
                            $salaryPlan->status->translation(),
                       ]">
                            <x-slot name="name_assigments">
                                {{ $salaryPlan->user->full_name }}
                                <div class="text-gray-400 text-xs">
                                    <x-vermont.user-assigments :componentAssigments="$salaryPlan->user->validAssigments" :id="'user-'.$salaryPlan->user->id"></x-vermont.user-assigments>
                                </div>
                            </x-slot>
                            <x-slot name="salary">
                                <x-vermont.showhide :id="'plan-'.$salaryPlan->id">
                                    {{ formatMoney(($salaryPlan->base ?? 0) + ($salaryPlan->addition ?? 0)) }} <span class="text-gray-400 text-xs">{{ $allCurrencies[$salaryPlan->currency_id]->code }}</span> <br>
                                    <span class="text-gray-400 text-xs">({{ formatMoney($salaryPlan->base ?? 0) }} + {{ formatMoney($salaryPlan->addition ?? 0) }})</span>
                                </x-vermont.showhide>
                            </x-slot>
                            <x-slot name="note">
                                <x-vermont.showhide :id="'note-'.$salaryPlan->id">
                                    {!! nl2br($salaryPlan->note) !!}
                                </x-vermont.showhide>
                            </x-slot>
                        </x-data-table.row>
                    @empty
                        <x-data-table.empty-row empty-table-text="{{ __('employee.remunerations.salaries.plans.none') }}"></x-data-table.empty-row>
                    @endforelse
                </x-data-table.wrapper>
                </div>
                <div class="col-span-3 lg:col-span-1">
                    <h2 class="text-base-content text-xl pb-3">
                        {{ __('employee.remunerations.salaries.plans.create.title') }}
                    </h2>

                    <form action="{{ route('employee.remunerations.salaries.plans.store') }}" class="flex flex-col gap-4 p-1" method="POST">

                        @csrf

                        <div class="">
                            <label for="user_id" class="label-text">
                                {{ __('employee.remunerations.salaries.plans.create.user_id') }}
                            </label>
                            <universal-select2
                                search-property="full_name"
                                input-property="id"
                                name="user_id"
                                all-option
                                :options="{{json_encode($employees)}}"
                                custom-classes="@error('user_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror"
                                :translations="{{ json_encode(['all' => __('main.form_select')])}}"
                                :required="true"
                                :selected="{{ old('user_id') }}"
                            >
                            </universal-select2>
                            @error('user_id')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="">
                            <label for="validity" class="label-text">
                                {{ __('employee.remunerations.salaries.plans.create.validity') }}
                            </label>
                            <select id="validity" name="validity" class="input @error('validity') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                @for($i = 0; $i < 12; $i++)
                                    <option value="{{ $mothDate = date('Y-m-01', $mothDateTime = strtotime($validityMinDate.' + '.$i.' months')) }}" @selected(old('validity') === $mothDate)>{{ date('m / Y', $mothDateTime) }}</option>
                                @endfor
                            </select>
                            @error('validity')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="">
                            <label for="currency_id" class="label-text">
                                {{ __('employee.remunerations.salaries.plans.create.currency') }}
                            </label>
                            <select id="currency_id" name="currency_id" class="input @error('currency_id') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" required>
                                <option value="">{{ __('main.form_select') }}</option>
                                @foreach($allCurrencies->where('is_active', 1) as $currency)
                                    <option value="{{ $currency->id }}" @selected($currency->id === (int) old('currency_id'))>{{ $currency->code }}</option>
                                @endforeach
                            </select>
                            @error('status')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="">
                            <label for="base" class="label-text">
                                {{ __('employee.remunerations.salaries.plans.create.base') }}
                            </label>
                            <input type="number" min="0" step="1" id="base" name="base" class="input @error('base') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" value="{{ old('base') }}" required>
                            @error('base')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="">
                            <label for="addition" class="label-text">
                                {{ __('employee.remunerations.salaries.plans.create.addition') }}
                            </label>
                            <input type="number" min="0" step="1" id="addition" name="addition" class="input @error('addition') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror" value="{{ old('addition') }}" placeholder="">
                            @error('addition')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="">
                            <label for="addition" class="label-text">
                                {{ __('employee.remunerations.salaries.plans.create.note') }}
                            </label>
                            <textarea id="note" name="note" class="textarea @error('note') focus:border-red-500 focus:ring-red-500 !border-red-500 @enderror">{{ old('note') }}</textarea>
                            @error('note')
                            <p class="text-sm text-red-600 mt-2">
                                {{ $message }}
                            </p>
                            @enderror
                        </div>

                        <div class="flex justify-end">
                            <button class="btn btn-outline">{{ __('main.save') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-employee-layout>
