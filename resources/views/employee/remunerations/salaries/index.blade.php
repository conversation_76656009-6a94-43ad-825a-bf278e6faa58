<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.salaries.title')" :link="route('employee.remunerations.salaries.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.salaries.list.title')" :link="route('employee.remunerations.salaries.index')"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full min-h-[86px]">
        <universal-filter
            :filter-inputs="{{json_encode($filterInputs)}}"
            :filter-data="{{json_encode($filterData)}}"
        ></universal-filter>
    </div>

    <div>
        <div class="mx-auto">
            <div class="bg-base-100 shadow-sm sm:rounded-lg">
                <div class="p-3 text-base-content">
                    <x-data-table.wrapper
                        :headers="[
                            __('employee.remunerations.salaries.list.user'),
                            __('employee.remunerations.salaries.list.contract'),
                            __('employee.remunerations.salaries.list.assigments'),
                            __('employee.remunerations.salaries.list.salary'),
                            __('employee.remunerations.salaries.list.validity'),
                            '',
                        ]"
                        :pagination-links="$contracts->links()"
                        :bordersRounded="''"
                        :customClassesForTh="''"
                        :showWithParentDivs="false"
                    >
                        @forelse($contracts as $contract)
                            <x-data-table.row :customClassesForTd="''" data-id="contract-{{ $contract->id }}" :columns="[
                                $contract->user->full_name,
                                'contract',
                                'assigments',
                                'salary',
                                !empty($contract->currentSalary) ? formatDate($contract->currentSalary->validity) : '',
                                'actions',
                           ]">
                                <x-slot name="contract">
                                    {!! $contract->type->shortTranslation().' - '.$contract?->company?->code !!}
                                </x-slot>
                                <x-slot name="assigments">
                                    <x-vermont.user-assigments :componentAssigments="$contract->user->assigments" :id="'user-'.$contract->user->id"></x-vermont.user-assigments>
                                </x-slot>
                                <x-slot name="salary">
                                    @if(!empty($contract->currentSalary))
                                        <div class="hidden lg:block">
                                            @if(empty($showSalaries))
                                                <x-vermont.showhide :id="'salary-'.$contract->currentSalary->id">
                                                    {{ formatMoney(($contract->currentSalary->base ?? 0) + ($contract->currentSalary->addition ?? 0)) }} <span class="text-gray-400 text-xs">{{ $allCurrencies[$contract->currentSalary->currency_id]->code }}</span> <br>
                                                    <span class="text-gray-400 text-xs">({{ formatMoney($contract->currentSalary->base ?? 0) }} + {{ formatMoney($contract->currentSalary->addition ?? 0) }})</span>
                                                </x-vermont.showhide>
                                            @else
                                                {{ formatMoney(($contract->currentSalary->base ?? 0) + ($contract->currentSalary->addition ?? 0)) }} <span class="text-gray-400 text-xs">{{ $allCurrencies[$contract->currentSalary->currency_id]->code }}</span> <br>
                                                <span class="text-gray-400 text-xs">({{ formatMoney($contract->currentSalary->base ?? 0) }} + {{ formatMoney($contract->currentSalary->addition ?? 0) }})</span>
                                            @endif
                                        </div>
                                        <div class="lg:hidden">
                                            <span class="text-nowrap">{{ formatMoney(($contract->currentSalary->base ?? 0) + ($contract->currentSalary->addition ?? 0)) }}</span> <span class="text-gray-400 text-xs">{{ $allCurrencies[$contract->currentSalary->currency_id]->code }}</span>
                                        </div>
                                    @else
                                        <x-flyonui.toast class="p-2">
                                            {{ __('employee.remunerations.salaries.list.no_salary') }}
                                        </x-flyonui.toast>
                                    @endif
                                </x-slot>
                                <x-slot name="actions">
                                    <a href="{{ route('employee.remunerations.salaries.history', ['contract' => $contract->id]) }}" class="btn btn-outline">
                                        {{ __('employee.remunerations.salaries.list.history_button') }}
                                    </a>
                                </x-slot>
                            </x-data-table.row>
                        @empty
                            <x-data-table.empty-row empty-table-text="{{ __('employee.remunerations.salaries.list.no_contracts') }}"></x-data-table.empty-row>
                        @endforelse
                    </x-data-table.wrapper>
                </div>
            </div>
        </div>
    </div>
</x-employee-layout>
