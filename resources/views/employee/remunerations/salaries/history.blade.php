<x-employee-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('employee.remunerations.title')" :link="route('employee.remunerations.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('employee.remunerations.salaries.title')" :link="route('employee.remunerations.salaries.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('employee.remunerations.salaries.list.history.title', ['contract' => $contract->type->shortTranslation().' - '.$contract?->company?->code, 'employee' => $contract->user->full_name])" :link="route('employee.remunerations.salaries.history', ['contract' => $contract->id])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <x-data-table.wrapper
            :headers="[
                __('employee.remunerations.salaries.list.history.validity'),
                __('employee.remunerations.salaries.list.history.salary'),
                __('employee.remunerations.salaries.list.history.base'),
                __('employee.remunerations.salaries.list.history.addition'),
            ]"
            :pagination-links="$salaries->links()"
            :bordersRounded="''"
            :customClassesForTh="'!px-4 !py-4'"
        >
            @forelse($salaries as $salary)
                <x-data-table.row :customClassesForTd="'!px-4 !py-4'" data-id="contract-{{ $contract->id }}" :columns="[
                    formatDate($salary->validity),
                    'salary',
                    'base',
                    'addition',
               ]">
                    <x-slot name="salary">
                        @if($contract?->currentSalary?->id === $salary->id)
                            <i class="hrms hrms-checked mr-2 text-success"></i>
                        @elseif($salary->validity > date('Y-m-d'))
                            <i class="hrms hrms-calendar-outline mr-2 opacity-50"></i>
                        @else
                            <i class="hrms hrms-parcel-history mr-2 opacity-50"></i>
                        @endif
                        <span @class(['font-bold' => $contract?->currentSalary?->id === $salary->id])>{{ formatMoney(($salary->base ?? 0) + ($salary->addition ?? 0)) }} </span>
                        <span class="text-gray-400 text-xs">{{ $salary->currency->code }}</span>
                    </x-slot>
                    <x-slot name="base">
                        <div class="opacity-50">
                            {{ formatMoney($salary->base ?? 0) }}
                            <span class="text-gray-400 text-xs">{{ $salary->currency->code }}</span>
                        </div>
                    </x-slot>
                    <x-slot name="addition">
                        <div class="opacity-50">
                            {{ formatMoney($salary->addition ?? 0) }}
                            <span class="text-gray-400 text-xs">{{ $salary->currency->code }}</span>
                        </div>
                    </x-slot>
                </x-data-table.row>
            @empty
                <x-data-table.empty-row empty-table-text="{{ __('employee.remunerations.salaries.list.no_contracts') }}"></x-data-table.empty-row>
            @endforelse
        </x-data-table.wrapper>
    </div>
</x-employee-layout>
