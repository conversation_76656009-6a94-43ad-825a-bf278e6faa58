<x-it-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('it.title')" :link="route('it.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('it.persons.user.title')"
                              :link="route('it.persons.users.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent
            :title="__('it.persons.user.show.title', ['fullname' => $user->full_name, 'login' => $user->login])"
            :link="route('it.persons.users.show', $user->id)"></x-breadcrumbs.parent>
        <x-breadcrumbs.current
            :title="__('it.persons.user.show.details.title')"
            :link="route('it.persons.users.show', $user->id)"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="overflow-hidden shadow-sm sm:rounded-lg">
                <div class="">
                    <div class="flex flex-col bg-base-100 border shadow-sm rounded-xl">

                        @include('it.person.user.parts.navigation')

                        <div class="p-4 md:py-7 md:px-5">

                            <div class="flex flex-col">
                                <div class="-m-1.5 overflow-x-auto">
                                    <div class="p-1.5 min-w-full inline-block align-middle">
                                        <div class="overflow-hidden">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <tbody class="divide-y divide-gray-200">
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('it.persons.user.show.details.login') }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary">{{ $user->login }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('it.persons.user.show.details.first_name') }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary">{{ $user->first_name }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('it.persons.user.show.details.last_name') }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary">{{ $user->last_name }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('it.persons.user.show.details.email') }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary">{{ $user->email }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('it.persons.user.show.details.phone') }}</td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary">{{ $user->phone }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-it-layout>
