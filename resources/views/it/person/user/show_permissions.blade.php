<x-it-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('it.title')" :link="route('it.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent :title="__('it.persons.user.title')"
                              :link="route('it.persons.users.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.parent
            :title="__('it.persons.user.show.title', ['fullname' => $user->full_name, 'login' => $user->login])"
            :link="route('it.persons.users.show', $user->id)"></x-breadcrumbs.parent>
        <x-breadcrumbs.current
            :title="__('it.persons.user.show.permissions.title')"
            :link="route('it.persons.users.show', [$user->id, $tab])"></x-breadcrumbs.current>
    </x-slot>

    <div>
        <div class="mx-auto">
            <div class="overflow-hidden shadow-sm sm:rounded-lg">
                <div class="">
                    <div class="flex flex-col bg-base-100 border shadow-sm rounded-xl">

                        @include('it.person.user.parts.navigation')

                        <div class="p-4 md:py-7 md:px-5">

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <h3 class="text-xl">{{ __('it.persons.user.show.permissions.roles') }}</h3>

                                    <div class="overflow-hidden">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <tbody class="divide-y divide-gray-200">
                                                @forelse($user->roles as $role)
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ $role->name }}</td>
                                                        @can(\App\Models\RaP\Permission::IT_CAN_ASSIGN_ROLES)
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">
                                                                <form action="{{ route('it.persons.users.destroy_role', ['user' => $user->id, 'role' => $role->id]) }}" method="post">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="btn btn-soft btn-error">
                                                                        {{ __('it.persons.user.show.permissions.remove_role') }}
                                                                    </button>
                                                                </form>
                                                            </td>
                                                        @endcan
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="99" class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('it.persons.user.show.permissions.no_roles') }}</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>

                                    @can(\App\Models\RaP\Permission::IT_CAN_ASSIGN_ROLES)
                                        <div class="mt-4">
                                            <h3 class="text-left">{{ __('it.persons.user.show.permissions.add_role') }}</h3>

                                            <form method="post" action="{{ route('it.persons.users.assign_role', ['user' => $user->id]) }}" class="mt-6 space-y-6">
                                                @csrf

                                                <div>
                                                    <x-select id="role" name="role" class="mt-1 block w-full" :required="true" :options="$allRoles" />
                                                </div>

                                                <div class="flex items-center gap-4">
                                                    <x-primary-button>{{ __('it.persons.user.show.permissions.add_role') }}</x-primary-button>
                                                </div>
                                            </form>
                                        </div>
                                    @endif
                                </div>

                                <div>
                                    <h3 class="text-xl">{{ __('it.persons.user.show.permissions.permissions') }}</h3>

                                    <div class="overflow-hidden">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <tbody class="divide-y divide-gray-200">
                                            @forelse($user->permissions as $permission)
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ $permission->name }}</td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ $permission->guard_name }}</td>
                                                    @can(\App\Models\RaP\Permission::IT_CAN_ASSIGN_ROLES)
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary">
                                                            <form action="{{ route('it.persons.users.destroy_permission', ['user' => $user->id, 'permission' => $permission->id]) }}" method="post">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-soft btn-error">
                                                                    {{ __('it.persons.user.show.permissions.remove_permission') }}
                                                                </button>
                                                            </form>
                                                        </td>
                                                    @endcan
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="99" class="px-6 py-4 whitespace-nowrap text-sm text-secondary">{{ __('it.persons.user.show.permissions.no_permissions') }}</td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>

                                    @can(\App\Models\RaP\Permission::IT_CAN_ASSIGN_ROLES)
                                        <div class="mt-4">
                                            <h3 class="text-left">{{ __('it.persons.user.show.permissions.add_permission') }}</h3>

                                            <form method="post" action="{{ route('it.persons.users.assign_permission', ['user' => $user->id]) }}" class="mt-6 space-y-6">
                                                @csrf

                                                <div>
                                                    <x-select id="permission" name="permission" class="mt-1 block w-full" :required="true" :options="$allPermissions" />
                                                </div>

                                                <div class="flex items-center gap-4">
                                                    <x-primary-button>{{ __('it.persons.user.show.permissions.add_permission') }}</x-primary-button>
                                                </div>
                                            </form>
                                        </div>
                                    @endif
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-it-layout>
