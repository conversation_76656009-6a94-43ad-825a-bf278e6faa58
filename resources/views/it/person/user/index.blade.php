<x-it-layout>
    <x-slot name="breadcrumbs">
        <x-breadcrumbs.parent :title="__('it.title')" :link="route('it.index')"></x-breadcrumbs.parent>
        <x-breadcrumbs.current :title="__('it.persons.user.title')"
                               :link="route('it.persons.users.index')"></x-breadcrumbs.current>
    </x-slot>

    <div class="bg-base-100 p-3 mb-3 w-full">
        <universal-filter
                :filter-inputs="{{json_encode($filterInputs)}}"
                :filter-data="{{json_encode($filterData)}}"
        ></universal-filter>
    </div>

    <x-simple-data-table :data-for-table="$users->getCollection()" :col-options="$colOptions" :pagination-links="$users->links()" empty-table-text="{{__('it.persons.user.list.no_users')}}"
    ></x-simple-data-table>

{{--    <div class="py-6">--}}
{{--        <x-data-table.wrapper--}}
{{--                :headers="[ __('it.persons.user.list.full_name'), __('it.persons.user.list.login') , __('it.persons.user.list.is_active')]" :pagination-links="$users->links()">--}}
{{--            <x-slot name="headersSlot">--}}
{{--                <x-data-table.header-cell custom-classes="text-end rounded-tr-lg">{{ __('it.persons.user.list.actions')}}</x-data-table.header-cell>--}}
{{--            </x-slot>--}}
{{--            @foreach($users as $user)--}}
{{--                <x-data-table.row :columns="[$user->full_name, $user->login]">--}}
{{--                    <x-data-table.cell>--}}
{{--                        <span @class(['inline-flex', 'items-center', 'gap-x-1.5', 'py-1.5', 'px-3', 'rounded-full', 'text-xs', 'font-medium', 'border', 'border-teal-500' => $user->is_active, 'text-teal-500' => $user->is_active, 'border-red-500' => !$user->is_active, 'text-red-500' => !$user->is_active])>{{ __('main.'.($user->is_active ? 'yes' : 'no')) }}</span>--}}
{{--                    </x-data-table.cell>--}}
{{--                    <x-data-table.cell custom-classes="text-end">--}}
{{--                        <a href="{{ route('it.persons.users.show', $user->id) }}"--}}
{{--                           class="button-black-outline">--}}
{{--                            {{ __('it.persons.user.list.detail') }}--}}
{{--                        </a>--}}
{{--                    </x-data-table.cell>--}}
{{--                </x-data-table.row>--}}

{{--            @endforeach--}}
{{--        </x-data-table.wrapper>--}}
{{--    </div>--}}
</x-it-layout>
