<div class="sm:hidden" x-data="{ }">
    <label for="hs-card-nav" class="sr-only">Select a nav</label>
    <select x-on:change="window.location = $event.target.value" name="hs-card-nav" id="hs-card-nav" class="select rounded-b-none block w-full border-t-0 border-x-0 border-gray-300 rounded-t-xl focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none">
        @foreach(['details', 'settings', 'others', 'permissions'] as $possibleTab)
            <option value="{{ route('it.persons.users.show', ['user' => $user->id, 'tab' => $possibleTab]) }}" {{ $tab === $possibleTab ? 'selected' : '' }}>{{ __('it.persons.user.show.navigation.'.$possibleTab) }}</option>
        @endforeach
    </select>
</div>
<!-- End Select (Mobile only) -->

<!-- Nav (Device only) -->
<div class="hidden sm:block">
    <nav class="relative z-0 flex border-b divide-x">
        @foreach(['details', 'settings', 'others', 'permissions'] as $possibleTab)
            <a @class([
                'group',
                'relative',
                'min-w-0',
                'flex-1',
                'bg-base-100',
                'py-4',
                'px-4',
                'border-b-4' => ($tab === $possibleTab),
                'border-b-blue-400' => ($tab === $possibleTab),
                'text-gray-500' =>  ($tab !== $possibleTab),
                'text-base-content' => ($tab === $possibleTab),
                'hover:text-gray-700' => ($tab !== $possibleTab),
                'rounded-tl-xl' => ($loop->first),
                'rounded-tr-xl' => ($loop->last),
                'text-gray-200' => in_array($possibleTab, ['settings', 'others']),
                'text-sm',
                'font-medium',
                'text-center',
                'overflow-hidden',
                'hover:bg-base-100/50' => !in_array($possibleTab, ['settings', 'others']),
                'focus:outline-none',
                'focus:bg-base-100/50',
                'focus:z-10',
                'cursor-not-allowed' => in_array($possibleTab, ['settings', 'others']),
            ]) @disabled(in_array($possibleTab, ['settings', 'others'])) href="{{ route('it.persons.users.show', [$user->id, $possibleTab]) }}">{{ __('it.persons.user.show.navigation.'.$possibleTab) }}</a>
        @endforeach
    </nav>
</div>
