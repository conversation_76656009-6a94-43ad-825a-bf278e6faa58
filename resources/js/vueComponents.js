import {createApp, defineAsyncComponent} from 'vue';

import globalComponents from './vue/imports/global.js';
import clickOutside from './vue/imports/customDirectives.js';

/* Dynamic import by section   */
const section = document.body.dataset.section || 'default';
const loadSectionComponents = async () => {
    switch (section) {
        case 'it':
            return (await import('./vue/imports/it.js')).default;
        case 'hr':
            return (await import('./vue/imports/hr.js')).default;
        case 'employee':
            return (await import('./vue/imports/employee.js')).default;
        default:
            return [];
    }
};

/* Import section components and then initialize app */
loadSectionComponents().then((sectionComponents) => {
    const app = createApp({});

    /*  Track already registered components */
    const registeredComponents = new Set();
    const registerComponent = (component) => {
        if (!component.name) {
            console.warn(`⚠️ Komponent nemá názov a nebude zaregistrovaný!`);
            return;
        }
        if (!registeredComponents.has(component.name)) {
            app.component(component.name, component);
            registeredComponents.add(component.name);
        } else {
            console.warn(`⚠️ Vue komponent "${component.name}" už existuje.`);
        }
    };

    // Register components
    globalComponents.forEach(registerComponent);
    sectionComponents.forEach(registerComponent);
    app.component('api-test', defineAsyncComponent(() => import('./vue/components/ApiTest.vue')));


    // Directive registrations
    app.directive('click-outside', clickOutside);

    // Mount app after everything is loaded
    app.mount('#main', true);
    window.HSStaticMethods.autoInit();
});
