<template>
    <div class="group relative">
        <slot></slot>
        <div v-if="text"
             :class="tooltipContainerClasses">
            <div class="bg-gray-800 text-white px-5 py-3 rounded-lg text-sm font-medium shadow-lg min-w-[200px] max-w-[350px] whitespace-normal text-center leading-snug">
                {{ text }}
            </div>
            <div :class="arrowClasses" />
        </div>
    </div>
</template>

<script>
export default {
    name: "UniversalTooltip",
    props: {
        text: {
            type: String,
            default: ''
        },
        position: {
            type: String,
            default: 'top' // 'top', 'left', 'right', 'bottom'
        }
    },
    computed: {
        tooltipContainerClasses() {
            let positionClasses = '';
            switch (this.position) {
                case 'top':
                    positionClasses = 'bottom-full left-1/2 -translate-x-1/2 pb-2.5 translate-y-1 group-hover:translate-y-0';
                    break;
                case 'bottom':
                    positionClasses = 'top-full left-1/2 -translate-x-1/2 pt-2.5 -translate-y-1 group-hover:translate-y-0';
                    break;
                case 'left':
                    positionClasses = 'top-1/2 -translate-y-1/2 right-full pr-2.5 translate-x-1 group-hover:translate-x-0';
                    break;
                case 'right':
                    positionClasses = 'top-1/2 -translate-y-1/2 left-full pl-2.5 -translate-x-1 group-hover:translate-x-0';
                    break;
            }
            return `absolute z-50 opacity-0 group-hover:opacity-100 transition-all duration-200 ease-out pointer-events-none ${positionClasses}`;
        },
        arrowClasses() {
            let positionClasses = '';
            const commonClasses = 'absolute w-0 h-0 border-transparent';
            switch (this.position) {
                case 'top':
                    positionClasses = 'bottom-0 left-1/2 -translate-x-1/2 border-x-[10px] border-t-[10px] border-t-gray-800';
                    break;
                case 'bottom':
                    positionClasses = 'top-0 left-1/2 -translate-x-1/2 border-x-[10px] border-b-[10px] border-b-gray-800';
                    break;
                case 'left':
                    positionClasses = 'top-1/2 -translate-y-1/2 right-0 border-y-[10px] border-l-[10px] border-l-gray-800';
                    break;
                case 'right':
                    positionClasses = 'top-1/2 -translate-y-1/2 left-0 border-y-[10px] border-r-[10px] border-r-gray-800';
                    break;
            }
            return `${commonClasses} ${positionClasses}`;
        }
    }
};
</script> 