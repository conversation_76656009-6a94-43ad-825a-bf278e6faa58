<template>
    <div>
        <label :for="`${this.filterName}[${this.attribute}]`"
        ><i :class="`hrms ${this.iconClass} opacity-25 me-1`"></i
        >{{ this.label }}</label
        >
    </div>
    <div :id="this.id" class="flex">
        <div v-click-outside="close">
            <button
                :class="['relative btn btn-secondary px-3 text-nowrap rounded-e-none  border-neutral/25', {'btn-outline': !this.isOpen}]"
                :title="this.actionTitleTranslations && this.actionTitleTranslations.length!== 1 ? this.actionTitleTranslations[this.action] : ''"
                v-if="!this.disableLogic"
                @click.prevent="changeOpenState"
                type="button"
            >
                {{ this.action ? this.actions[this.action] : '~' }}
            </button>
            <transition
                enter-active-class="transition-opacity duration-100 ease-out"
                enter-from-class="opacity-0 scale-95"
                enter-to-class="opacity-100 scale-100"
                leave-active-class="transition-opacity duration-100 ease-in"
                leave-from-class="opacity-100 scale-100"
                leave-to-class="opacity-0 scale-95"
            >
                <ul v-if="isOpen" class="absolute mt-1 min-w-24 border rounded-md bg-base-100 z-50 overflow-hidden border-neutral/25">
                    <li
                        v-for="(action, key) in this.actions"
                        :title="this.actionTitleTranslations && this.actionTitleTranslations.length !== 1 ? this.actionTitleTranslations[key] : ''"
                        class="w-full"
                    >
                        <button
                            type="button"
                            class="text-sm w-full text-start p-1 hover:bg-accent hover:text-white hover:cursor-pointer"
                            @click.prevent="setAction(key)"
                        >
                            <span
                                :class="[
                                    'font-bold',
                                    { 'text-primary': this.action === key },
                                ]"
                            >
                                {{ action }}
                            </span>
                            <span class=""> — </span>
                            <span :class="[{ 'text-primary': this.action === key }]">
                        {{
                                    this.actionTitleTranslations && this.actionTitleTranslations.length !== 1 ? this.actionTitleTranslations[key] : ''
                                }}
                    </span>
                        </button>
                    </li>
                </ul>
            </transition>

        </div>

        <template v-if="this.action === 'in' || this.action === 'notIn'">
            <universal-filter-multiple-select
                :search-property="this.searchProperty ? this.searchProperty : null"
                :input-property="this.inputProperty ? this.inputProperty : null"
                :name="`${this.filterName}[${this.attribute}]`"
                :options="this.multipleData ? this.multipleData : []"
                :selected="this.filterValue ? this.filterValue : []"
                :disable-logic="this.disableLogic"
                :select-all-option="!this.disableAllOption"
                :translations="this.translations.actions"
            >
            </universal-filter-multiple-select>
        </template>
        <template
            v-else-if="this.action === 'isNull' || this.action === 'isNotNull'"
        >
            <input
                type="hidden"
                :name="`${this.filterName}[${this.attribute}]`"
                value="1"
            />
            <input type="text"
                   class="border w-full rounded-e-md  border-neutral/25 focus:border-neutral/40 focus:ring-0 disabled:bg-base-200 disabled:pointer-events-none"
                   disabled/>
        </template>
        <template v-else-if="this.options && this.initialize">
            <universal-filter-select
                :name="`${this.filterName}[${this.attribute}]`"
                :selected="this.filterValue"
                :search-property="this.searchProperty"
                :input-property="this.inputProperty"
                :options="this.options"
                :all-option="!this.disableAllOption"
                :disable-logic="this.disableLogic"
                :translations="this.translations.actions"
            >
            </universal-filter-select>
        </template>
        <template v-else>
            <input
                :class="['input border border-neutral/25  focus:border-neutral/25 focus:ring-0 disabled:bg-gray-200 disabled:pointer-events-none', {'rounded-s-none': !disableLogic}]"
                type="text"
                :name="`${this.filterName}[${this.attribute}]`"
                :id="`${this.filterName}[${this.attribute}]`"
                v-model="this.filterValue"
                :placeholder="this.placeholder"
            />
        </template>
    </div>
    <template v-if="this.action && !this.disableLogic">
        <div
            class="mt-2 flex justify-center border rounded bg-base-200 border-neutral/25"
        >
            <span class="text-primary me-1">
                {{ this.actions[this.action] }}
            </span>
            <span class="text-gray-400 me-1">—</span>
            <span class="text-gray-500">
                {{
                    this.actionTitleTranslations && this.actionTitleTranslations.length !== 1 ? this.actionTitleTranslations[this.action] : ''
                }}
            </span>
        </div>
    </template>

</template>

<script>
import UniversalSelect2 from "../inputs/UniversalSelect2.vue";
import UniversalFilterMultipleSelect from "../inputs/UniversalMultipleSelect2.vue";

export default {
    name: "UniversalFilterInput",
    components: {
        UniversalFilterSelect: UniversalSelect2,
        UniversalFilterMultipleSelect
    },
    props: {
        label: String,
        attribute: String,
        iconClass: String,
        options: null,
        searchProperty: null,
        inputProperty: "",
        filter: {
            type: Object,
            default: {}
        },
        customAction: {
            type: String,
            default: null
        },
        placeholder: {
            type: String,
            default: ""
        },
        translations: {
            type: Object,
            default: Object
        },
        disableLogic: {
            type: Boolean,
            default: false
        },
        allowedActions: {
            default: null
        },
        disableAllOption: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            initialize: false,
            // action: this.options ? 'equal' : 'like',
            action: null,
            filterName: "",
            filterValue: null,
            actionTitleTranslations: Object,
            actions: {
                like: "~",
                notLike: "!~",
                equal: "=",
                notEqual: "!=",
                lessThan: "<",
                greaterThan: ">",
                isNull: "x",
                isNotNull: "!x",
                in: "[.]",
                notIn: "![.]"
            },
            multipleData: this.options,
            id: "",
            isOpen: false
        };
    },
    beforeMount() {
        if (Object.values(this.translations) == 0) {
            axios.get('/global/filter/get-default-data/action_data').then(response => {
                this.actionTitleTranslations = response.data
            })
        } else {
            this.actionTitleTranslations = this.translations['filter_actions']
        }

    },
    mounted() {
        this.actions = this.allowedActions ? this.allowedActions : this.actions;
        let tempId = this.attribute.replace(".", "");
        let action = this.options ? "equal" : "like";
        this.id = `universal-filter-input-${tempId}`;
        this.filterName = `filter[${action}]`;
        if (this.customAction) {
            this.filterName = `filter[${this.customAction}][${action}]`;
        }
        if (this.options) {
            this.multipleData = [...this.options];
            delete this.actions["like"];
            delete this.actions["notLike"];
            // if (this.searchProperty) {
            //     let temp = {};
            //     temp[this.searchProperty] = "All";
            //     temp[this.inputProperty] = null;
            //     this.options.unshift(temp);
            // } else {
            //     this.options.unshift("");
            // }
        }
        this.init();
    },
    watch: {
        translations(value) {
            this.actionTitleTranslations = value.filter_actions

        }
    },
    methods: {
        setAction(action) {
            if (
                (this.action === "isNull" || this.action === "isNotNull") &&
                action !== "isNull" &&
                action !== "isNotNull"
            ) {
                this.filterValue = null;
            }
            if (
                (this.action === "in" || this.action === "notIn") &&
                action !== "in" &&
                action !== "notIn"
            ) {
                this.filterValue = null;
            }

            if (this.filterValue && !Array.isArray(this.filterValue) && !this.options) {
                if (action === "in" || action === "notIn") {
                    this.multipleData = [];
                    this.multipleData.push(this.filterValue);
                }
            }

            if (this.filterValue && !Array.isArray(this.filterValue) && (action === "in" || action === "notIn") && this.action !== "in" && this.action !== "notIn") {
                this.filterValue = [this.filterValue];
                if (this.options && this.inputProperty) {
                    this.filterValue = this.options.filter(option => {
                        return this.filterValue.includes(`${option[this.inputProperty]}`);
                    });
                }
            }

            this.action = action;

            this.filterName = `filter[${this.action}]`;
            if (this.customAction) {
                this.filterName = `filter[${this.customAction}][${this.action}]`;
            }
            this.close()
        },
        init() {
            Object.keys(this.actions).forEach((item) => {
                let filterValue = this.filter[item] ?? null;
                if (this.customAction) {
                    filterValue = this.filter[this.customAction]
                        ? this.filter[this.customAction][item] ?? null
                        : null;
                }
                filterValue = filterValue
                    ? filterValue[this.attribute] ?? null
                    : null;
                if (filterValue != null) {
                    this.action = item;
                    this.filterName = `filter[${this.action}]`;
                    if (this.customAction) {
                        this.filterName = `filter[${this.customAction}][${this.action}]`;
                    }
                    if (Array.isArray(filterValue) && this.options && this.inputProperty) {
                        this.filterValue = this.options.filter(option => {
                            return filterValue.includes(`${option[this.inputProperty]}`);
                        });
                    } else {
                        this.filterValue = filterValue;
                    }
                    if (Array.isArray(this.filterValue) && !this.options) {
                        this.multipleData = this.filterValue;
                    }
                    return true;
                }
            });
            this.initialize = true;
        },
        changeOpenState(newValue = null) {
            this.isOpen = newValue === null ? !this.isOpen : newValue
            if (this.isOpen) {
                document.addEventListener("keydown", this.handleEscape);
            } else {
                document.removeEventListener("keydown", this.handleEscape);
            }
        },
        handleEscape(event) {
            if (event.key === "Escape") {
                this.changeOpenState(false);
            }
        },
        close() {
            if (this.isOpen) {
                this.isOpen = false
            }
        }
    },
    computed: {}
};
</script>

<style scoped>

/*.hover:hover {
    background: #0d6efd;
    color: white;
    cursor: pointer;
}*/

</style>
