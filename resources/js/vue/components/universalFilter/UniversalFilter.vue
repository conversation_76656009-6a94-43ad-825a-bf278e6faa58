<template v-if="this.initialize">

    <!--  Filter <PERSON>er  -->
    <hr>
    <template v-if="this.localFilterTags.length > 0 || true">
        <div :class="['grid grid-cols-4 gap-2 my-3', {'print:hidden': !this.printTags}]">
            <div class="col-span-4 md:col-span-3">
                <div class="flex flex-wrap align-middle gap-1">
                    <span class="text-gray-500 whitespace-nowrap"><i class="hrms hrms-filter me-2"></i>
                        <strong class="text-lg">{{ this.translations?.base?.name }}:</strong>
                    </span>
                    <span class="badge badge-outline badge-accent"
                          v-if="this.identifiedActualPreset != null">
                        <i class="hrms hrms-bookmark2-add me-2 opacity-50"></i>
                        {{ this.translations?.bookmark?.name }}:
                        <span class="font-bold ms-1">
                            {{
                                this.identifiedActualPreset.preset_name
                            }}
                        </span> <a
                        :href="this.resetFilterRoute" type="reset"><i
                        class="hrms hrms-close"></i></a>
                    </span>
                    <template v-for="tag in this.filterTags" v-if="this.filterTags.length > 0">
                        <template v-if="Array.isArray(tag['value'])">
                          <span v-for="(value) in tag['value']"
                                class="badge badge-outline badge-accent">
                            <i v-if="tag.icon" :class="`hrms ${tag.icon} opacity-50 me-1`"></i>
                            <span>{{ value.value ? value.value : value }}</span>
                            <template v-if="!this.disableLogic && tag['type'] !== 'custom'">
                              <span class="font-normal opacity-75 mx-1">—</span>
                              <span
                                  class="font-normal opacity-75">{{
                                      this.translations.length !== 0 ? this.translations?.filter_actions[tag["type"]] : ""
                                  }} </span>
                            </template>
                            <a
                                :href="`${this.resetFilterAttributeRoute}/${tag['attribute']}/${value.originalFilterValue ? value.originalFilterValue : value}`">
                                <i class="hrms hrms-close opacity-50"></i>
                            </a>
                          </span>
                        </template>
                        <template v-else>
                            <span class="badge badge-outline badge-accent">
                                <i v-if="tag.icon" :class="`hrms ${tag.icon} me-1`"></i>
                                <span>{{ tag["value"] }}</span>
                                  <template v-if="!this.disableLogic && tag['type'] !== 'custom'">
                                    <span class="font-normal opacity-75 mx-1">—</span>
                                    <span
                                        class="font-normal opacity-75">{{
                                            this.translations.length !== 0 ? this.translations?.filter_actions[tag["type"]] : ""
                                        }}</span>
                                  </template>
                                  <a
                                      :href="`${this.resetFilterAttributeRoute}/${tag['attribute']}`">
                                      <i class="hrms hrms-close text-blue-600"></i>
                                  </a>
                            </span>
                        </template>

                    </template>
                </div>
            </div>
            <!--     Right action buttons       -->
            <div class="col-span-4 md:col-span-1 items-end print:hidden">
                <div class="flex justify-end">
                    <button class="btn btn-primary !rounded-r-none whitespace-nowrap cursor-pointer"
                            @click="toggleHideFilter">
                        <i :class="['hrms opacity-75', {'hrms-filter': !this.filtering}, {'hrms-filter-filled-active': this.filtering}]"></i>
                        {{ this.translations?.base?.search_filter }}
                    </button>

                    <div class="relative">
                        <button id="myBookmarks" type="button"
                                class="btn btn-primary !rounded-s-none h-full whitespace-nowrap cursor-pointer"
                                @click="changeBookmarkDropdownOpenState()">
                            <i :class="['hrms opacity-75', {'hrms-bookmark2-add-outline-1': this.identifiedActualPreset == null},  {'hrms-bookmark2-active': this.identifiedActualPreset != null}]"></i>
                            {{ this.translations?.bookmark?.my_bookmark }}
                            <svg class="opacity-75 size-5" viewBox="0 0 20 20" fill="currentColor"
                                 aria-hidden="true" data-slot="icon">
                                <path fill-rule="evenodd"
                                      d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                                      clip-rule="evenodd"/>
                            </svg>
                        </button>

                        <div
                            ref="dropdown"
                            :class="[
                            'z-50 border bg-base-100 shadow-sm absolute rounded-md right-0 mt-1', {'hidden': !showBookmarksDropdown},
                            ]">

                            <ul class="my-2" aria-labelledby="myBookmarks">
                                <li v-if="this.savedPresets.length == 0" class="p-2 whitespace-nowrap">
                                    {{ this.translations?.bookmark?.without_bookmarks }}
                                </li>
                                <li :class="{'border-l-4 py-1 border-indigo-400 leading-5 text-indigo-700  bg-indigo-50 focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition duration-150 ease-in-out': this.identifiedActualPreset?.id === savedPreset.id}"
                                    v-for="savedPreset in this.savedPresets" v-else>
                                    <a :class="['block text-start w-full p-2 whitespace-nowrap hover:bg-base-200']"
                                       :href="savedPreset.url">{{ savedPreset.preset_name }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <hr>
        <div @click="changeBookmarkDropdownOpenState(false)" v-if="showBookmarksDropdown"
             class="absolute opacity-80 top-0 start-0 w-full h-full z-40">

        </div>
    </template>

    <div id="filterCollapse"
         :class="['mt-1', {'hidden':  this.hideFilter === false}, {'print:hidden': !this.printFilter}]"
    >
        <div>
            <form class="" method="GET" :id="this.formId">
                <div class="grid grid-cols-6 gap-4">
                    <template v-for="filterInput in this.filterInputs">
                        <div v-if="(filterInput.show ?? true)"
                             :class="filterInput.filterClass ?? 'col-span-6 sm:col-span-3 md:col-span-2 2xl:col-span-1'">
                            <universal-filter-input
                                v-model:translations="this.translations"
                                :filter="this.filter"
                                :translations="this.translations"
                                :label="filterInput.label"
                                :icon-class="filterInput.icon"
                                :attribute="filterInput.attribute"
                                :placeholder="filterInput.placeholder ?? ''"
                                :options="filterInput.options ?? null"
                                :search-property="filterInput.searchProperty ?? null"
                                :input-property="filterInput.inputProperty ?? null"
                                :custom-action="filterInput.customAction ?? null"
                                :disable-logic="this.disableLogic || (filterInput.disableLogic ?? false)"
                                :allowed-actions="filterInput.allowedActions ?? null"
                                :disable-all-option="filterInput.disableAllOption"
                            >
                            </universal-filter-input>
                        </div>
                    </template>
                    <slot></slot>
                </div>
                <div class="grid grid-cols-6 mt-2 print:hidden">
                    <div class="col-span-6 sm:col-span-1">
                        <button
                            v-if="this.actualFilterUrl !== '' && !this.actualFilterUrl.includes('default-filter-data')"
                            type="button"
                            class="btn btn-primary btn-outline !gap-1" id="copyUrl"
                            @click.prevent="copyUrl()">
                            <i class="hrms hrms-share opacity-50 h-3.5"></i>
                            {{ this.translations?.actions?.share }}
                        </button>
                    </div>
                    <div class="col-span-6 sm:col-span-4 flex justify-center">
                        <a :href="this.resetFilterRoute" type="reset"
                           class="btn btn-primary btn-outline me-1 whitespace-nowrap !gap-1">
                            <i class="hrms hrms-reset h-3.5"></i>
                            <span class="">{{ this.translations?.actions?.reset }}</span>
                        </a>
                        <button class="btn btn-primary whitespace-nowrap !gap-1 cursor-pointer" type="submit">
                            {{ this.translations?.actions?.search }}
                            <i class="hrms hrms-search h-3.5"></i>
                        </button>

                    </div>
                    <div class="col-span-6 md:col-span-1 items-end" v-if="this.enableFilterSaving">
                        <div v-if="this.filtering === true" class="flex justify-end">
                            <!-- Save filter modal -->
                            <universal-modal
                                v-if="actualPresetExists !== true"
                                modal-window-classes="w-1/3"
                                button-classes="btn btn-primary btn-outline"
                            >
                                <template #button>
                                    <i class="hrms hrms-bookmark2-add h-3.5"></i>{{
                                        this.translations?.bookmark?.save_as_bookmark
                                    }}
                                </template>
                                <template #body>
                                    <div class="text-center mb-2">
                                        <p class="text-2xl mb-3" style="font-size: 70px !important;">
                                            <i class="hrms hrms-bookmark2-add text-blue-700"></i>
                                        </p>
                                        <p class="text-xl font-bold">
                                            {{ this.translations?.bookmark?.save_filter_as_bookmark }}
                                        </p>
                                        <div class="text-start">
                                            <label for="saveBookmarkName"
                                                   class="font-bold opacity-50">{{
                                                    this.translations?.bookmark?.bookmark_name
                                                }}</label>
                                            <input type="text" v-model="presetName" class="input-default"
                                                   id="saveBookmarkName">
                                        </div>

                                    </div>
                                    <div class="flex flex-wrap gap-1 mb-2">
                                        <template v-for="tag in this.filterTags" v-if="this.filterTags.length > 0">
                                            <template v-if="Array.isArray(tag['value'])">
                                            <span v-for="(value) in tag['value']"
                                                  class="badge badge-outline badge-accent">
                                                <i v-if="tag.icon" :class="`hrms ${tag.icon} opacity-50 me-1`"></i>
                                                <span>{{ value.value ? value.value : value }}</span>
                                                <template v-if="!this.disableLogic && tag['type'] !== 'custom'">
                                                      <span class="font-normal opacity-75 mx-1">—</span>
                                                      <span
                                                          class="font-normal opacity-75">{{
                                                              this.translations.length !== 0 ? this.translations?.filter_actions[tag["type"]] : ""
                                                          }} </span>
                                                </template>
                                                <a class="flex"
                                                   :href="`${this.resetFilterAttributeRoute}/${tag['attribute']}/${value.originalFilterValue ? value.originalFilterValue : value}`">
                                                    <i class="hrms hrms-close opacity-50"></i>
                                                </a>
                                            </span>
                                            </template>
                                            <template v-else>
                                            <span class="badge badge-outline badge-accent">
                                                <i v-if="tag.icon" :class="`hrms ${tag.icon}`"></i>
                                                <span>{{ tag["value"] }}</span>
                                                <template v-if="!this.disableLogic && tag['type'] !== 'custom'">
                                                    <span class="font-normal opacity-75 mx-1">—</span>
                                                    <span
                                                        class="font-normal opacity-75">{{
                                                            this.translations.length !== 0 ? this.translations?.filter_actions[tag["type"]] : ""
                                                        }}</span>
                                                </template>
                                                <a class="flex"
                                                   :href="`${this.resetFilterAttributeRoute}/${tag['attribute']}`">
                                                <i class="hrms hrms-close text-blue-600 h-3.5"></i>
                                                </a>
                                            </span>
                                            </template>

                                        </template>
                                    </div>
                                    <div class="flex justify-between">
                                        <button type="button" class="btn btn-secondary btn-outline" data-bs-dismiss="modal">
                                            {{ this.translations?.bookmark?.close }}
                                            <i
                                                class="hrms hrms-close h-3.5"></i>
                                        </button>
                                        <span v-if="this.presetName.trim() == ''"
                                              class="btn btn-primary btn-outline opacity-50">
                                    <i
                                        class="hrms hrms-bookmark2-add h-3.5"></i>{{
                                                this.translations?.bookmark?.save_as_bookmark
                                            }}
                                    </span>
                                        <a v-else
                                           :href="this.saveFilterBaseUrl+'/'+this.filterName+'/'+this.presetName.trim()+'/'+this.actualFilterInJson"
                                           :class="['btn btn-primary btn-outline']"><i
                                            class="hrms hrms-bookmark2-add h-3.5"></i>{{
                                                this.translations?.bookmark?.save_as_bookmark
                                            }}
                                        </a>
                                    </div>
                                </template>
                            </universal-modal>
                            <form
                                :action="this.identifiedActualPreset == null ? '' :this.identifiedActualPreset.deleteUrl"
                                method="POST"
                                v-else>
                                <input type="hidden" name="_method" value="DELETE">
                                <input type="hidden" name="_token" :value="localCsrf">
                                <button type="submit" class="btn btn-error btn-outline whitespace-nowrap border"
                                        :disabled="this.identifiedActualPreset == null" @click="areYouSure($event)">
                                    <i class="hrms hrms-trash-outline h4"></i>{{
                                        this.translations?.bookmark?.delete_bookmark
                                    }}
                                </button>
                            </form>
                        </div>


                        <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel"
                             aria-hidden="true">
                            <div class="modal-dialog">

                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <hr class="mt-3">

        </div>
    </div>
</template>

<script>

import UniversalFilterInput from "./UniversalFilterInput.vue";
import UniversalModal from "../UniversalModal.vue";
import Swal from "sweetalert2";

export default {
    name: "UniversalFilter",
    components: {
        UniversalFilterInput,
        Swal,
        UniversalModal
    },
    props: {
        formId: {
            type: String,
            default: "universal-filter-form-id"
        },
        filterInputs: Object,
        filterTags: {
            default: []
        },
        printTags: {
            type: Boolean,
            default: true
        },
        printFilter: {
            type: Boolean,
            default: false
        },
        disableLogic: {
            type: Boolean,
            default: false
        },
        filterData: {
            type: Object,
            default: Object
        },


    },
    data() {
        return {
            localFilterTags: this.filterTags,
            initialize: false,
            actions: ["like", "notLike", "equal", "notEqual", "lessThan", "greaterThan", "isNull", "isNotNull", "in", "notIn", "custom"],
            identifiedActualPreset: null,
            localCsrf: globalCsrf,
            showForm: false,
            presetName: "",
            filterName: this.filterData.filterNameForBookmarks,
            hideFilter: JSON.parse(localStorage.getItem(this.filterData.filterName)) ?? false,
            actualFilterUrl: this.filterData?.actualFilterUrl ?? '',
            actualFilterInJson: this.filterData?.filterInJson,
            saveFilterBaseUrl: this.filterData?.saveFilterBaseUrl ?? '',
            savedPresets: this.filterData?.userSavedPresets ?? Object,
            enableFilterSaving: this.filterData?.enableFilterSaving ?? false,
            translations: this.filterData?.translations ?? [],
            filter: this.filterData?.filter ?? [],
            resetFilterRoute: this.filterData?.resetFilterRoute ?? '',
            resetFilterAttributeRoute: this.filterData?.resetFilterAttributeRoute ?? '',
            filtering: this.filterData?.isFiltering ?? false,
            showBookmarksDropdown: false

        };
    },
    beforeMount() {
        if (this.filterData.translations == undefined) {
            this.getDefaultFilterData();
        } else {
            this.translations = this.filterData.translations
        }
    },
    mounted() {
        this.init();

    },
    methods: {
        init() {
            this.filterInputs.forEach((input) => {
                this.actions.forEach((item) => {
                    let filterValue = this.filter[item] ?? null;
                    if (input.customAction) {
                        filterValue = this.filter[input.customAction] ? this.filter[input.customAction][item] ?? null : null;
                    }
                    filterValue = filterValue ? filterValue[input.attribute] ?? null : null;
                    if (filterValue != null) {
                        if (["isNull", "isNotNull"].includes(String(item))) {
                            filterValue = input.label;
                        } else if (input.inputProperty) {
                            if (Array.isArray(filterValue)) {
                                let tempFilterValue = Object.values(input.options).filter(option => {
                                    return filterValue.includes(String(option[input.inputProperty]));
                                });
                                filterValue = [];
                                tempFilterValue.forEach((item) => {
                                    filterValue.push({
                                        value: item[input.searchProperty],
                                        originalFilterValue: item[input.inputProperty]
                                    });
                                });
                            } else {
                                filterValue = Object.values(input.options).find(option => {
                                    return option[input.inputProperty] == filterValue;
                                })[input.searchProperty];
                            }
                        }


                        let filterTag = {
                            value: filterValue,
                            type: item,
                            icon: input.icon ?? null,
                            attribute: input.attribute
                        };

                        this.localFilterTags.push(filterTag);

                        return true;
                    }
                });
            });
            this.initialize = true;
        },
        copyUrl() {
            const textarea = document.createElement("textarea");
            textarea.value = this.actualFilterUrl;
            textarea.style.position = "fixed";
            document.body.appendChild(textarea);
            textarea.focus();
            textarea.select();

            const successful = document.execCommand("copy");
            if (successful) {
                Swal.fire({
                    title: '<div class="custom-header flex items-start gap-1"><i class="hrms hrms-checked text-primary"></i>' + this.translations.bookmark?.url_copied + '</div>',
                    html: `<i class="hrms hrms-copyclipboardsend text-primary" style="font-size: 200px"></i> <p>
                    <h4 class="text-base-content font-bold text-xl mt-2">` + this.translations.bookmark?.you_can_paste_link + `</h4>
                    ` + this.translations.bookmark?.with_your_default_shortcut + `
                    </p>`,
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonColor: "#6c757d",
                    cancelButtonText: this.translations?.bookmark?.close,
                    showClass: {popup: ""},
                    hideClass: {popup: ""},
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        cancelButton: 'btn btn-secondary custom-cancel',
                        // actions: 'custom-actions',
                    },
                    buttonsStyling: false,
                })
            } else {
                alert("Failed.");
            }

            document.body.removeChild(textarea);
        },
        areYouSure(event) {
            event.preventDefault();
            Swal.fire({
                title: '<div class="custom-header flex items-start gap-1"><i class="hrms hrms-alert text-red-700"></i>' + this.translations?.bookmark?.are_you_sure + '</div>',
                html: ` <div style="text-align: center;">
                          <i class="hrms hrms-delete-bookmark text-red-700" style="font-size: 200px"></i>
                          <h2 class="text-xl font-bold">` + this.identifiedActualPreset.preset_name + `</h2>
                          <p class="text-gray-500">` + this.translations?.bookmark?.action_cant_be_undone + `</p>
                        </div>
                    `,
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#6c757d",
                confirmButtonText: this.translations?.bookmark?.yes_remove,
                cancelButtonText: this.translations?.bookmark?.no_cancel,
                showClass: {popup: ""},
                hideClass: {popup: ""},
                customClass: {
                    confirmButton: 'btn btn-error custom-confirm',
                    cancelButton: 'btn btn-error btn-outline custom-cancel',
                    actions: 'custom-actions',
                },
                buttonsStyling: false,
            }).then(async (result) => {
                if (result.isConfirmed) {
                    event.target.closest('form').submit();
                    Swal.fire({
                        title: '<div class="custom-header flex items-start gap-1"><i class="hrms hrms-checked text-red-700"></i>' + this.identifiedActualPreset.preset_name + '</div>',
                        html: ` <div style="text-align: center;">
                          <i class="hrms hrms-delete-bookmark-success text-danger" style="font-size: 200px"></i>
                          <h2>` + this.translations?.bookmark?.removed_success + `</h2>
                          <p class="text-muted"></p>
                        </div>
                    `,
                        showCancelButton: true,
                        showConfirmButton: false,
                        cancelButtonText: this.translations?.bookmark?.close
                    });
                }
            });
        },
        toggleHideFilter() {
            this.hideFilter = !this.hideFilter
            localStorage.setItem(this.filterName, this.hideFilter)

        },
        getDefaultFilterData() {
            let resultData = axios.get('/global/filter/get-default-data').then(result => {
                result = result.data
                this.filterName = result.filterName
                this.hideFilter = JSON.parse(localStorage.getItem(this.filterName)) ?? false
                this.actualFilterUrl = result.actualFilterUrl
                this.actualFilterInJson = result.filterInJson
                this.saveFilterBaseUrl = result.saveFilterBaseUrl
                this.savedPresets = result.userSavedPresets
                this.enableFilterSaving = result.enableFilterSaving
                this.translations = result.translations
            })
        },
        changeBookmarkDropdownOpenState(newValue = null) {
            this.showBookmarksDropdown = newValue === null ? !this.showBookmarksDropdown : newValue
            if (this.showBookmarksDropdown) {
                document.addEventListener("keydown", this.handleEscape);
            } else {
                document.removeEventListener("keydown", this.handleEscape);
            }
        },
        handleEscape(event) {
            if (event.key === "Escape") {
                this.changeBookmarkDropdownOpenState(false);
            }
        }
    },
    computed: {
        actualPresetExists() {
            let matchedPreset
            matchedPreset = this.savedPresets.filter(preset => {
                return preset.settings == this.actualFilterInJson
            })
            this.identifiedActualPreset = matchedPreset.length > 0 ? matchedPreset[0] : null
            return matchedPreset.length > 0
        },

    }
};
</script>


<style>

.custom-actions {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.custom-confirm, .custom-cancel {
    margin-left: 15px;
    margin-right: 15px;
    padding: 0.5rem 1rem;
    min-width: 100px;
}


.custom-actions button:not(:last-child) {
    margin-right: 20px;
}

.custom-header {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid lightgray;
    padding-bottom: 10px;
    margin-bottom: 20px;
}
</style>
