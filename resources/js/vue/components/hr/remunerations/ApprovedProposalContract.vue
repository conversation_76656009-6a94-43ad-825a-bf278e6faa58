<script>
export default {
    name: "ApprovedProposalContract",
    components: {},
    props: {
        proposal: Object,
    },
    data() {
        return {
            showProposalsList: false,
            showProposalsListLoader: false,
            contracts: [
                {id: 1, type: "tpp", company: {code: "VSK"}, payslip_code: "abc", start_date: "2025-02-01"},
                {id: 2, type: "dvp", company: {code: "VSER"}, payslip_code: "cba", start_date: "2025-03-01"}
            ],
            translations: {hr: {remunerations: {approved_proposals: {contract_start: "zac", contract_code: "kod"}}}}
        }
    },
    beforeMount() {
    },
    mounted() {
    },
    beforeUnmount() {
    },
    methods: {
        openCloseProposalsList() {
            if(this.showProposalsList){
                this.showProposalsList = false;
                return;
            }
            this.showProposalsListLoader = true;

            this.loadContracts();
        },
        loadContracts() {
            setTimeout(() => {
                this.showProposalsListLoader = false;
                this.showProposalsList = true;
            }, 500);
        },
        setContractIdForProposal(contractId) {
            this.showProposalsListLoader = true;
            this.showProposalsList = false;
            console.info("Nastavujem contract " + contractId + " pre proposal " + this.proposal.id);
            setTimeout(() => {
                this.showProposalsListLoader = false;
                location.reload();
            }, 500);
        }
    },
    computed: {
    }
};
</script>

<template>
    <div class="relative">
        <div class="flex justify-end">
            <a v-if="proposal.contract_id > 0" href="#" @click.prevent="openCloseProposalsList()" class="btn btn-soft btn-warning">Zmen</a>
            <a v-else href="#" @click.prevent="openCloseProposalsList()" class="btn btn-soft btn-error">Nastav</a>
        </div>
        <div v-if="showProposalsListLoader || showProposalsList" id="proposals_selector_{{ proposal.id }}" class="card absolute top-[100%] right-0 z-100 bg-base-100">
            <div class="card-body p-2">
                <div v-if="showProposalsListLoader">loadujem</div>
                <div v-if="showProposalsList">
                    <table class="table">
                        <tr v-for="contract in contracts" :key="contract.id">
                            <td>
                                {{ contract.type }} - {{ contract.company.code }}
                                <span class="text-gray-400 text-xs">({{ translations.hr.remunerations.approved_proposals.contract_start }} {{ contract.start_date}})</span>
                                <br>
                                <span class="text-gray-400 text-xs">{{ translations.hr.remunerations.approved_proposals.contract_code }}</span> {{ contract.payslip_code }}
                            </td>
                            <td>
                                <a href="#" @click.prevent="setContractIdForProposal(contract.id)" class="btn btn-soft btn-success">Nastav tento PP</a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>

</style>
