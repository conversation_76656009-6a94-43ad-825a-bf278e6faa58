<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="absolute mt-2 z-10 rounded-md shadow-lg bg-base-100 focus:outline-none"
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="menu-button"
      tabindex="-1"
    >
      <div class="p-0" role="none">
        <slot></slot>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'UniversalPopover',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s ease-out;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style> 