<template>
    <div :class="[{'mt-2 justify-center': !showForm}]"
         class="grid grid-flow-col auto-cols-[250px]">
        <transition name="step" mode="out-in">
            <!--      Select approver if not exists      -->
            <section id="zero_step"
                     class="col-span-2 flex justify-center mt-1"
                     v-if="!eventBus.defaultApprover">
                <div class="flex flex-col items-center p-5 w-3/6 max-h-fit bg-base-100 border rounded-xl space-y-3">
                    <div>
                        <img :src="this.supervisorIcon" alt="image"
                             style="height: 98px">
                    </div>
                    <div class="font-bold text-xl mb-1">{{ translations.approver }}</div>
                    <button class="btn btn-primary" @click="eventBus.openSelectApproverModal()">{{
                            translations.select
                        }} <i
                            class="hrms hrms-continue-form opacity-50"></i>
                    </button>
                </div>
            </section>

            <!--      Select new proposal form type      -->
            <section id="first_step"
                     class="col-span-2 flex justify-center mt-1"
                     v-else-if="eventBus.defaultApprover && !showForm">
                <div class="flex flex-col items-center p-5 w-4/6 max-h-fit bg-base-100 border rounded-xl">
                    <div>
                        <img :src="this.createFormIcon" alt="image"
                             style="height: 140px">

                    </div>
                    <div class="font-bold text-xl mb-1">{{ translations.add_next_proposal }}</div>
                    <div class="text-start w-full">
                        <label for="type" class="text-sm mb-3 block">{{ translations.which_type_add }}</label>
                        
                        <!-- Show all proposal types as input-like rows when not selected or when showing all -->
                        <transition
                            name="collapse"
                            enter-active-class="transition-all duration-400 ease-out"
                            enter-from-class="opacity-0 max-h-0"
                            enter-to-class="opacity-100 max-h-[500px]"
                            leave-active-class="transition-all duration-400 ease-in"
                            leave-from-class="opacity-100 max-h-[500px]"
                            leave-to-class="opacity-0 max-h-0">
                            <div v-if="!form.type || showAllProposalTypes"
                                 class="space-y-2 pb-2">
                                <div v-for="type in types" :key="type.value">
                                    <universal-tooltip :text="getIsTypeDisabled(type.value) ? translations?.create_disabled_by_validity : ''">
                                        <div
                                            @click="selectProposalType(type.value)"
                                            :class="[
                                             'flex items-center gap-2 transition-all duration-300 h-auto py-2 px-3 min-h-[3rem] rounded-lg border w-full',
                                             form.type == type.value ? 'border-primary bg-primary/10' : 'border-gray-200 border',
                                             {'bg-white': form.type != type.value},
                                             {'border-red-500 bg-red-50': firstStepError.input === 'type'},
                                             {'opacity-50 cursor-not-allowed': submitStarted},
                                             {'cursor-not-allowed': getIsTypeDisabled(type.value)},

                                             {'cursor-pointer hover:bg-base-200/80 hover:border-secondary/30': !getIsTypeDisabled(type.value)}
                                         ]"
                                            :style="submitStarted ? 'pointer-events: none;' : ''">
                                            <div v-if="form.type == type.value && showAllProposalTypes"
                                                 class="text-gray-400 flex-shrink-0">
                                                <i class="hrms hrms-status-pending"></i>
                                            </div>
                                            <div v-else-if="!form.type || form.type != type.value"
                                                 class="text-gray-300 flex-shrink-0">
                                                <i class="hrms"
                                                   :class="[{'hrms-status-pending': !getIsTypeDisabled(type.value)}, {'hrms-status-stop-outline': getIsTypeDisabled(type.value)}]"></i>
                                            </div>
                                            <div v-if="type.icon" class="text-xl flex-shrink-0"
                                                 :class="[
                                                {'text-gray-400': !getIsTypeDisabled(type.value)},
                                                {'text-gray-300': getIsTypeDisabled(type.value)},
                                                ]">
                                                <i :class="type.icon"></i>
                                            </div>

                                            <div class="flex-1 text-left">
                                                <div class="font-medium text-base"
                                                     :class="[
                                                {'text-base-content':!getIsTypeDisabled(type.value)},
                                                {'text-gray-300': getIsTypeDisabled(type.value)},
                                                ]">
                                                    {{ type.translation }}
                                                </div>
                                                <div v-if="type.description" class="text-sm mt-1"
                                                >
                                                    {{ type.description }}
                                                </div>

                                            </div>

                                        </div>
                                    </universal-tooltip>
                                </div>
                            </div>

                        </transition>

                        <!-- Show selected proposal type as a single input-like row when one is selected -->
                        <transition
                            name="slide-down"
                            enter-active-class="transition-all duration-400 ease-out"
                            enter-from-class="opacity-0 transform -translate-y-2"
                            enter-to-class="opacity-100 transform translate-y-0"
                            leave-active-class="transition-all duration-400 ease-in"
                            leave-from-class="opacity-100 transform translate-y-0"
                            leave-to-class="opacity-0 transform -translate-y-2">
                            <div v-if="form.type && !showAllProposalTypes" class="mb-3">
                                <div class="flex items-center gap-2 h-auto py-2 px-3 min-h-[3rem] rounded-lg border bg-base-100 border-neutral/15  hover:bg-base-200/80 hover:border-secondary/30">
                                    <div class="text-primary flex-shrink-0">
                                        <i class="hrms hrms-checked"></i>
                                    </div>
                                    <div v-if="selectedType?.icon" class="text-xl text-gray-400 flex-shrink-0">
                                        <i :class="selectedType.icon"></i>
                                    </div>
                                    <div class="flex-1 text-left">
                                        <div class="font-medium text-base">{{ selectedType?.translation }}</div>
                                        <div v-if="selectedType?.description" class="text-sm text-gray-500 mt-1">{{ selectedType.description }}</div>
                                    </div>
                                    <div @click.prevent="showAllTypes" class="text-gray-400 hover:text-primary cursor-pointer transition-colors flex-shrink-0 p-1 rounded hover:bg-gray-100">
                                        <i class="hrms hrms-swap text-lg"></i>
                                    </div>
                                </div>
                            </div>
                        </transition>

                        <div v-if="firstStepError.input === 'type'" class="text-red-700 text-sm mb-3">{{
                                firstStepError.message
                            }}</div>

                        <!-- Subtype selection - shows when a type is selected and has subcases -->
                        <transition
                            name="expand"
                            enter-active-class="transition-all duration-400 ease-out"
                            enter-from-class="opacity-0 max-h-0 overflow-hidden"
                            enter-to-class="opacity-100 max-h-32 overflow-visible"
                            leave-active-class="transition-all duration-400 ease-in"
                            leave-from-class="opacity-100 max-h-32 overflow-visible"
                            leave-to-class="opacity-0 max-h-0 overflow-hidden">
                            <div v-if="selectedType && selectedType.subcases" class="mb-3">
                                <label for="subtype" class="label-text">{{ translations.subtype }}</label>
                                <universal-select2
                                    :all-option="false"
                                    :disable-logic="true"
                                    search-property="translation"
                                    input-property="value"
                                    :select-first="false"
                                    :options="Object.values(selectedType.subcases)"
                                    v-model="form.subtype"
                                    :disabled="this.submitStarted"
                                >
                                </universal-select2>
                            </div>
                        </transition>
                        
                        <div v-if="firstStepError.input === 'subcases'" class="text-red-700 text-sm mb-3">{{
                                firstStepError.message
                            }}</div>
                        
                        <!-- Add button - only show when type is selected and subtype is selected (if required) -->
                        <transition
                            name="fade-up"
                            enter-active-class="transition-all duration-400 ease-out"
                            enter-from-class="opacity-0 transform translate-y-4"
                            enter-to-class="opacity-100 transform translate-y-0"
                            leave-active-class="transition-all duration-400 ease-in"
                            leave-from-class="opacity-100 transform translate-y-0"
                            leave-to-class="opacity-0 transform translate-y-4">
                            <div v-if="form.type && (!selectedType?.subcases || form.subtype)" class="text-center">
                                <button class="btn btn-primary" @click.prevent="handleShowForm">{{
                                        translations.add
                                    }} <i class="hrms hrms-continue-form opacity-50"></i>
                                </button>
                            </div>
                        </transition>
                    </div>


                </div>
            </section>

            <!--      Proposal form      -->
            <section id="second_step" class="col-span-2" v-else>
            <div class="bg-base-100 border border-t-0 py-4 px-6 h-full">
                <div class="inline-flex w-full mb-2 h-24 border-b items-start justify-between">
                    <div>

                        <span class="block font-bold text-2xl"><i
                            :class="[selectedType?.icon, 'me-1 text-gray-400']"></i>{{
                                selectedType?.translation
                            }}</span>
                        <span
                            class="text-gray-400">{{
                                selectedType?.subcases ? selectedType?.subcases[form.subtype]?.translation : ''
                            }}</span>
                    </div>
                    <button class="btn btn-secondary btn-outline border-0" @click="showForm = false"><i
                        class="hrms hrms-close"></i>
                    </button>
                </div>

                <form :action="createRoute" method="POST">
                    <input type="hidden" name="_token" :value="localCsrf" autocomplete="off">

                    <!--         Currency selector           -->
                    <label for="currency_id1" class="label-text mb-1 font-bold">{{ translations.currency }}:</label>
                    <div class="flex mb-3 gap-6">
                        <div class="flex items-center gap-1" v-for="currency in currencies" :key="currency.id">
                            <input type="radio" name="currency_id" class="radio radio-primary radio-inset radio-xs"
                                   :id="'currency_id'+currency.id" :value="currency.id" v-model="form.currency_id"
                                   :disabled="submitStarted"/>
                            <label class="label-text text-base" 
                                   :class="[form.currency_id == currency.id ? 'font-bold' : 'text-secondary']"
                                   :for="'currency_id'+currency.id"> {{
                                    currency.code
                                }} </label>
                        </div>
                    </div>

                    <!--         Base input           -->
                    <label for="base" class="label-text text-secondary mb-1">{{ translations.base }}</label>
                    <div
                        :class="['relative w-full', {'mb-3': !selectedType?.subcases || !selectedType?.subcases[form.subtype]?.budget}]">
                        <number-input
                            v-model:actual-value="form.base"
                            :disabled="this.submitStarted"
                            :classes="['input pr-12 block text-right', {'is-invalid' :Object.values(this.requestErrors).length > 0 && this.requestErrors.base }]"
                            name="base" id="base">
                        </number-input>
                        <span class="absolute inset-y-0 right-3 flex items-center text-gray-500 text-sm">
                            {{ currencies[form.currency_id]?.code }}
                        </span>
                    </div>
                    <span class="text-xs mb-3"
                          v-if="selectedType?.subcases && selectedType?.subcases[form.subtype]?.budget">
                        {{ translations.budget }}: <span class="font-bold">{{ this.eventBus.formatNumber(actualBudget) }}</span> / {{
                            this.eventBus.formatNumber(selectedType?.subcases[form.subtype]?.budget.budget)
                        }} {{ selectedType?.subcases[form.subtype]?.budget.currency.code }}
                    </span>

                    <!--         Addition input           -->
                    <div v-if="selectedType?.addition_enabled">
                        <label for="addition" class="label-text text-secondary mb-1">{{ translations.addition }}</label>
                        <div class="relative w-full mb-3">
                            <number-input
                                v-model:actual-value="form.addition"
                                :disabled="this.submitStarted"
                                :classes="['input pr-12 block text-right', {'is-invalid' :Object.values(this.requestErrors).length > 0 && this.requestErrors.addition }]"
                                name="addition" id="addition">
                            </number-input>
                            <span class="absolute inset-y-0 right-3 flex items-center text-gray-500 text-sm">
                            {{ currencies[form.currency_id]?.code }}
                        </span>
                        </div>
                    </div>

                    <!--         Validity input           -->
                    <label for="validity" v-if="!this.hiddenValidity" class="label-text text-secondary mb-1">{{
                            translations.validity
                        }}</label>
                    <input v-model="form.validity" :type="this.hiddenValidity ? 'hidden' : 'month'" id="validity"
                           name="validity"
                           class="input mb-3"
                           :disabled="this.submitStarted">

                    <!--         Reason input           -->
                    <label for="reason" class="label-text text-secondary mb-1">{{ translations.reason }}</label>
                    <textarea v-model="form.reason" aria-label="Textarea" id="reason" name="reason"
                              class="textarea mb-3"
                              :disabled="this.submitStarted" :required="reasonRequired">
                    </textarea>

                    <!--         Centre users selector           -->
                    <label for="" class="label-text flex justify-between text-secondary mb-1">{{ translations.centre_users }} 
                        <a @click.prevent="handleSmartSelection" 
                           :class="[
                               'flex items-center gap-1 transition-colors',
                               smartButtonState.disabled
                                   ? 'text-gray-400 cursor-not-allowed' 
                                   : 'hover:text-primary cursor-pointer'
                           ]"
                           :style="smartButtonState.disabled ? 'pointer-events: none;' : ''">
                            <i :class="smartButtonState.icon" class="text-sm"></i>
                            <span>{{ smartButtonState.text }}</span>
                        </a>
                    </label>
                    <div>
                        <div class="flex items-center gap-1" v-for="user in centreUsers" :key="user.id">
                            <input type="checkbox" class="checkbox checkbox-primary" :id="'add_propose_user_'+user.id"
                                   :value="user.id"
                                   v-model="form.users" checked
                                   :disabled="this.submitStarted || disabledUserCheckboxes.includes(user.id)"/>
                            <label
                                :class="[{'text-gray-400 cursor-not-allowed': this.submitStarted || disabledUserCheckboxes.includes(user.id)}]"
                                class="label label-text text-base font-medium" :for="'add_propose_user_'+user.id" :key="user.id">
                                {{
                                    user.full_name
                                }} </label>
                        </div>
                    </div>

                    <div class="text-center mb-3" v-if="!this.addMoreUsersShow">
                        <a href="" @click.prevent="openAddMoreUsers" class="link text-sm text-gray-400 no-underline p-3">
                            <i class="hrms hrms-asign-task-circle me-1"></i>{{ translations.add_more_people }}
                        </a>
                    </div>
                    <div class="my-3" v-else>
                        <label for="addMoreUsers" class="label-text">{{ translations.add_more_people }}</label>
                        <div v-if="loadingUsersForAddMoreUsers"
                             class="input cursor-not-allowed flex items-center justify-center">
                            <span class="loading loading-spinner loading-md"></span>
                        </div>
                        <universal-multiple-select2 v-else
                            :disable-logic="true"
                            search-property="full_name_with_centres"
                            input-property="id"
                            :options="addMoreUsers ? Object.values(addMoreUsers) : {}"
                            :disabled="this.submitStarted"
                                                    :default-open-to-above="true"
                            @changed="handleAddMoreUsers">
                        </universal-multiple-select2>
                    </div>

                    <input type="hidden" id="type" name="type" :value="form.type">
                    <input type="hidden" id="subtype" name="subtype" :value="form.subtype">
                    <input type="hidden" id="centre_id" name="centre_id" :value="centre_id">
                    <input type="hidden" id="department_id" name="department_id" :value="department_id">
                    <input type="hidden" id="proposer_id" name="proposer_id" :value="proposer?.id">
                    <input type="hidden" id="approver_id" name="approver_id" :value="proposer?.id">
                    <div class="flex justify-center">
                        <button type="submit" class="btn btn-primary my-3 w-full" @click="submit">
                            {{ translations.create }}
                            <i
                                class="hrms hrms-selectall"></i>
                        </button>
                    </div>
                </form>
                <div v-if="requestErrors">
                    <div v-for="(inputError, errorIndex) in requestErrors" :key="errorIndex">
                        <span v-for="(error, subErrorIndex) in inputError" :key="subErrorIndex" class="text-red-700"> {{ error }}</span>
                    </div>
                </div>
            </div>
        </section>
        </transition>
    </div>


</template>

<script>

/*  Space for import    */

import UniversalSelect2 from "../inputs/UniversalSelect2.vue";
import UniversalMultipleSelect2 from "../inputs/UniversalMultipleSelect2.vue";
import createFormIcon from '../../../../images/form/document-request.svg';
import supervisorIcon from '../../../../images/form/supervisor.svg';
import NumberInput from "../inputs/NumberInput.vue";
import UniversalTooltip from "../UniversalTooltip.vue";
export default {
    name: "CreateForm",
    components: {UniversalTooltip, NumberInput, UniversalMultipleSelect2, UniversalSelect2},
    emits: ['created', 'update:centreUsers', 'update:addMoreUsers'],
    inject: ['eventBus'],
    props: {
        addMoreUsers: Object,
        approvers: Object,
        centre_id: {
            type: Number,
            default: null
        },
        centreUsers: Object,
        createRoute: String,
        currencies: Object,
        createWithoutRefresh: {
            type: Boolean,
            default: true
        },
        department_id: Number,
        hiddenValidity: {
            type: Boolean,
            default: true
        },
        oneOfKindUserGroups: {
            type: Object,
            default: {}
        },
        mainDataLoaded: Boolean,
        proposer: Object,
        selectedCurrency: {
            type: Number,
            default: null
        },
        translations: {
            type: Object,
            default: {}
        },
        types: Object,
        validity: {
            type: String,
            default: null
        },
    },
    data() {
        return {
            actualAddMoreUsersSelect: [],
            addMoreUsersShow: false,
            createFormIcon,
            disabledUserCheckboxes: {},
            firstStepError: {input: '', message: ''},
            form: {
                type: null,
                subtype: null,
                base: null,
                addition: null,
                currency_id: this.selectedCurrency,
                reason: null,
                validity: this.validity,
                approver_id: this.proposer?.id,
                department_id: this.department_id,
                centre_id: this.centre_id,
                proposer_id: this.proposer?.id,
                users: []
            },
            loadingUsersForAddMoreUsers: false,
            localCsrf: globalCsrf,
            requestErrors: {},
            selectedType: null,
            showForm: false,
            submitStarted: false,
            supervisorIcon,
            showAllProposalTypes: false
        }
    },
    watch: {
        validity(newValue) {
            this.form.validity = newValue
        },

        oneOfKindUserGroups(newValue) {
            this.checkDisabledUserCheckboxes()
        },
        centreUsers: {
            handler(newValue) {
                this.addMoreUsersShow = false
            },
            deep: true
        },
    },

    beforeMount() {

    },
    mounted() {
    },
    beforeUnmount() {
    },
    methods: {
        selectProposalType(type) {
            if (this.getIsTypeDisabled(type)) {
                return
            }
            if (!this.submitStarted) {
                this.form.type = type
                this.selectedType = this.types[type]
                this.form.subtype = null
                this.showAllProposalTypes = false
            }
        },
        showAllTypes() {
            this.showAllProposalTypes = true
            this.form.type = null
            this.selectedType = null
            this.form.subtype = null
            this.firstStepError.input = ''
            this.firstStepError.message = ''
        },
        submit(event) {
            const form = event.target.closest("form");
            if (!form.checkValidity()) {
                form.reportValidity();
                event.preventDefault();
                return;
            }
            this.submitStarted = true
            if (this.createWithoutRefresh) {
                event.preventDefault();
                axios.post(this.createRoute, {
                    ...this.form
                }).then(response => {
                    this.$emit('created', {...this.form})
                    this.submitStarted = false

                    /*  Add newly added users to centreUsers list (in form + checkboxes) */
                    let centreUsersObjectValues = Object.values(this.centreUsers)
                    let tempCentreUsers = this.centreUsers
                    let tempAddMoreUsers = this.addMoreUsers
                    Object.values(this.addMoreUsers).forEach(user => {
                        if (this.form.users.includes(user.id)) {
                            const keys = Object.keys(this.centreUsers).map(Number);
                            const nextKey = keys.length ? Math.max(...keys) + 1 : 0;
                            if (!centreUsersObjectValues.some(centreUser => user.id === centreUser.id)) {
                                tempCentreUsers = {...tempCentreUsers, [nextKey]: user}
                            }
                            delete tempAddMoreUsers[Object.keys(tempAddMoreUsers).find(
                                key => tempAddMoreUsers[key].id === user.id
                            )]
                        }
                    })
                    this.$emit('update:centreUsers', tempCentreUsers)
                    this.$emit('update:addMoreUsers', tempAddMoreUsers)

                    this.resetForm()
                }).catch(errorResponse => {
                    // console.log(errorResponse)
                    if (errorResponse.response?.data?.errors) {
                        this.requestErrors = errorResponse.response.data.errors
                    }
                    if (errorResponse.response?.data?.message) {
                        this.requestErrors = [errorResponse.response.data.message]
                    }
                    this.submitStarted = false
                })
            }
        },
        resetForm(withSelectedTypeAndSubType = true) {
            if (withSelectedTypeAndSubType) {
                this.selectedType = null
                this.form.type = null
                this.form.subtype = null
                this.showAllProposalTypes = false
            }
            this.showForm = false
            this.form.base = null
            this.form.addition = null
            this.form.reason = null
            this.form.validity = this.validity
            this.form.department_id = this.department_id
            this.form.centre_id = this.centre_id
            this.form.proposer_id = null
            this.showAllCurrencies = false

            this.requestErrors = {}
        },
        handleAddMoreUsers(data) {
            let addIds = []
            let removeIds = []

            Object.values(data).forEach(selectData => {
                if (!this.actualAddMoreUsersSelect.includes(selectData.id) && !this.form.users.includes(selectData.id)) {
                    addIds.push(selectData.id)
                }
            })
            this.actualAddMoreUsersSelect.forEach(selectedId => {
                if (!Object.values(data).some(selectData => selectData.id == selectedId)) {
                    removeIds.push(selectedId)
                }
            })
            this.actualAddMoreUsersSelect = [...new Set([...this.actualAddMoreUsersSelect, ...addIds])]

            this.form.users = [...new Set([...this.form.users, ...addIds])]
            this.actualAddMoreUsersSelect = this.actualAddMoreUsersSelect.filter(
                id => !removeIds.includes(id)
            )
            this.form.users = this.form.users.filter(
                id => !removeIds.includes(id)
            )
        },
        handleShowForm() {
            if (!this.selectedType) {
                this.firstStepError.input = 'type'
                this.firstStepError.message = this.translations.subcases_error
                return
            }
            if (Object.values(this.selectedType.subcases ?? {}).length > 0 && !this.form.subtype) {
                this.firstStepError.input = 'subcases'
                this.firstStepError.message = this.translations.subcases_error
                return
            }

            this.firstStepError.input = ''
            this.firstStepError.message = ''
            this.requestErrors = {}
            this.resetForm(false)
            this.checkDisabledUserCheckboxes()
            this.showForm = true

        },
        checkDisabledUserCheckboxes() {
            this.disabledUserCheckboxes = []
            this.form.users = []
            
            Object.values(this.centreUsers).forEach(user => {
                if (this.form.subtype) {
                    Object.values(this.selectedType.subcases[this.form.subtype].one_of_kind_per_month_groups).forEach(oneOfKindGroup => {
                        const userHasThisGroup = Object.values(this.oneOfKindUserGroups[user.id] ?? []).includes(oneOfKindGroup)

                        if (userHasThisGroup) {
                            this.disabledUserCheckboxes.push(user.id)
                            return;
                        }
                    })
                }
                if (!this.disabledUserCheckboxes.includes(user.id)) {
                    this.form.users.push(user.id)
                }
            })
        },
        toggleAllUsers() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);

            // Don't do anything if there are no selectable users
            if (selectableUsers.length === 0) {
                return;
            }

            // Keep non-selectable users (disabled ones) in the selection
            const nonSelectableSelected = this.form.users.filter(userId =>
                !selectableUsers.includes(userId)
            );

            // Simple logic: if all selectable users are selected, deselect them
            // Otherwise, select all selectable users
            if (this.allUsersSelected) {
                // All are selected, so deselect all selectable users
                this.form.users = nonSelectableSelected;
            } else {
                // Not all are selected, so select all selectable users
                this.form.users = [...nonSelectableSelected, ...selectableUsers];
            }
        },
        selectAllUsers() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);

            if (selectableUsers.length === 0) {
                return;
            }

            // Keep non-selectable users (disabled ones) in the selection
            const nonSelectableSelected = this.form.users.filter(userId =>
                !selectableUsers.includes(userId)
            );

            // Select all selectable users
            this.form.users = [...nonSelectableSelected, ...selectableUsers];
        },
        deselectAllUsers() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);

            if (selectableUsers.length === 0) {
                return;
            }

            // Keep non-selectable users (disabled ones) in the selection
            const nonSelectableSelected = this.form.users.filter(userId =>
                !selectableUsers.includes(userId)
            );

            // Deselect all selectable users
            this.form.users = nonSelectableSelected;
        },
        openAddMoreUsers() {
            this.addMoreUsersShow = true

            if (Object.values(this.addMoreUsers).length === 0) {
                this.loadingUsersForAddMoreUsers = true
                axios.post('/api/v1/employee/remunerations/get-all-necessary-dynamic-data/1', {
                    section_id: this.eventBus.section.id,
                    department_id: this.eventBus.department.id,
                    centre_id: this.eventBus.centre?.id,
                    validity: this.eventBus.actualYear + '-' + this.eventBus.monthForCreateForm + '-01'
                }).then(response => {
                    let data = response.data
                    this.eventBus.usersForAddMoreCentreUsers = data.usersForAddMorePeople
                    this.loadingUsersForAddMoreUsers = false
                }).catch(errorResponse => {
                    // console.log(errorResponse)
                })
            }
        },
        getIsTypeDisabled(type) {
            return this.eventBus.allProposals[type] && this.eventBus.allProposals[type].disabled_by_validity
        },
        invertSelection() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);

            if (selectableUsers.length === 0) {
                return;
            }

            // Keep non-selectable users (disabled ones) in the selection
            const nonSelectableSelected = this.form.users.filter(userId =>
                !selectableUsers.includes(userId)
            );

            // Invert selection: currently selected selectable users become unselected,
            // currently unselected selectable users become selected
            const currentlySelectedSelectableUsers = selectableUsers.filter(userId => 
                this.form.users.includes(userId)
            );
            const currentlyUnselectedSelectableUsers = selectableUsers.filter(userId => 
                !this.form.users.includes(userId)
            );

            // New selection: keep non-selectable + add currently unselected (invert)
            this.form.users = [...nonSelectableSelected, ...currentlyUnselectedSelectableUsers];
        },
        handleSmartSelection() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);

            if (selectableUsers.length === 0) {
                return;
            }

            const selectedCount = selectableUsers.filter(userId => this.form.users.includes(userId)).length;
            const totalCount = selectableUsers.length;

            // Smart logic based on current selection state
            if (selectedCount === 0) {
                // No users selected -> Select All
                this.selectAllUsers();
            } else if (selectedCount === totalCount) {
                // All users selected -> Deselect All
                this.deselectAllUsers();
            } else {
                // Some users selected -> Select Opposite (invert)
                this.invertSelection();
            }
        },
    },
    computed: {
        allUsersSelected() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);
            
            return selectableUsers.length > 0 && selectableUsers.every(userId => this.form.users.includes(userId));
        },
        anyUsersSelected() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);
            
            return selectableUsers.some(userId => this.form.users.includes(userId));
        },
        selectableUsersCount() {
            return Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .length;
        },
        selectAllButtonState() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);
            
            if (selectableUsers.length === 0) {
                return {
                    action: 'none',
                    text: this.translations.no_users || 'No Users',
                    icon: 'hrms hrms-status-stop-outline'
                };
            }
            
            // Simple logic: show what action will be performed when clicked
            if (this.allUsersSelected) {
                // All are selected, clicking will deselect all
                return {
                    action: 'deselect',
                    text: this.translations.deselect_all || 'Deselect All',
                    icon: 'hrms hrms-deselectall'
                };
            } else {
                // Not all are selected, clicking will select all
                return {
                    action: 'select',
                    text: this.translations.select_all || 'Select All',
                    icon: 'hrms hrms-selectall'
                };
            }
        },
        actualBudget() {
            const matchedCentreUsers = Object.values(this.centreUsers).filter(centreUser => {
                return this.form.users.includes(centreUser.id)
            })
            const otherUsers = Object.values(this.addMoreUsers).filter(otherUser => {
                return this.form.users.includes(otherUser.id)
            })

            const validCentreUsersForCalculation = matchedCentreUsers.filter(centreUser => {
                const positions = Object.values(centreUser.valid_assigments).flatMap(assigment => assigment.position)
                const excludeBudgetCalculations = Object.values(positions).flatMap(position => position.excluded_for_budgets)
                const isExcludedFromBudgetCalculation = excludeBudgetCalculations.filter(excludeBudget => {
                    return excludeBudget.subtype == this.form.subtype
                })
                return isExcludedFromBudgetCalculation.length === 0
            })

            const validOtherUsersForCalculation = otherUsers.filter(otherUser => {
                const positions = Object.values(otherUser.valid_assigments).flatMap(assigment => assigment.position)
                const excludeBudgetCalculations = Object.values(positions).flatMap(position => position.excluded_for_budgets)
                const isExcludedFromBudgetCalculation = excludeBudgetCalculations.filter(excludeBudget => {
                    return excludeBudget.subtype == this.form.subtype
                })
                return isExcludedFromBudgetCalculation.length === 0
            })

            return Number(this.eventBus.actualBudgets[this.form.subtype] ?? 0) + ((Number(this.form.base) + Number(this.form.addition)) * (Object.values(validCentreUsersForCalculation).length + Object.values(validOtherUsersForCalculation).length))
        },
        reasonRequired() {
            return this.selectedType && this.selectedType.subcases && this.selectedType.subcases[this.form.subtype] ? this.selectedType.subcases[this.form.subtype].reason_required : false
        },
        smartButtonState() {
            const selectableUsers = Object.values(this.centreUsers)
                .filter(user => !this.disabledUserCheckboxes.includes(user.id))
                .map(user => user.id);
            
            if (selectableUsers.length === 0) {
                return {
                    disabled: true,
                    text: this.translations.no_users || 'No Users',
                    icon: 'hrms hrms-status-stop-outline'
                };
            }

            const selectedCount = selectableUsers.filter(userId => this.form.users.includes(userId)).length;
            const totalCount = selectableUsers.length;

            // Determine button state based on current selection
            if (selectedCount === 0) {
                // No users selected -> Show "Select All"
                return {
                    disabled: false,
                    text: this.translations.select_all || 'Select All',
                    icon: 'hrms hrms-selectall'
                };
            } else if (selectedCount === totalCount) {
                // All users selected -> Show "Deselect All"
                return {
                    disabled: false,
                    text: this.translations.deselect_all || 'Deselect All',
                    icon: 'hrms hrms-deselectall'
                };
            } else {
                // Some users selected -> Show "Select Opposite"
                return {
                    disabled: false,
                    text: this.translations.select_opposite || 'Select Opposite',
                    icon: 'hrms hrms-swap'
                };
            }
        },
    }
};
</script>


<style scoped>
/* Main step transitions for section changes */
.step-enter-from {
    transform: translateX(-20px);
    opacity: 0;
}

.step-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

.step-enter-to,
.step-leave-from {
    transform: translateX(0);
    opacity: 1;
}

.step-enter-active {
    transition: all 0.3s ease-out;
}

.step-leave-active {
    transition: all 0.3s ease-in;
}
</style>