<template>
    <div class="w-full h-full border rounded-md overflow-hidden">
        <!--   Centre and year month detail     -->
        <section id="centre" class="text-center text-xl pt-3 pb-2 border-b bg-base-100">
            <!--            <div-->
            <!--                class="font-bold inline-flex justify-center items-center py-1 border rounded-md leading-none"-->
            <!--            >-->
            <!--                <button class="button border-0 hover:bg-base-100/50"><i-->
            <!--                    class="hrms hrms-calendar-outline text-xl text-gray-500"></i>-->
            <!--                </button>-->
            <!--                <button class="button border-0 py-3 hover:bg-base-100/50" @click="changeActualDate(-1)"><i-->
            <!--                    class="hrms hrms-chevron-big-left text-gray-300"></i>-->
            <!--                </button>-->


            <!--                <span class="leading-none"><span class="loading loading-spinner"-->
            <!--                                                 v-if="Object.values(this.translations?.months ?? []).length === 0"></span><span-->
            <!--                    v-else>{{ this.translations?.months[actualMonth] }}</span> {{ actualYear }}</span>-->
            <!--                <button class="button border-0 py-3 hover:bg-base-100/50" @click="changeActualDate(1)"><i-->
            <!--                    class="hrms hrms-chevron-big-right text-gray-300"></i>-->
            <!--                </button>-->
            <!--            </div>-->
            <div class="grid grid-cols-7 justify-center items-center">
                <div
                    class="col-span-1 text-start">
                    <div v-if="previousCentreData && previousCentreData.url?.length > 0" class="ps-3">
                        <a class="btn btn-secondary btn-outline gap-1 text-xl text-secondary border-neutral/15"
                           :href="previousCentreData?.url">

                            <span class="font-bold">{{ previousCentreData?.centre_code }}</span>
                            <i class="hrms hrms-arr-left ms-2 text-gray-300 text-xl"></i>
                        </a>
                    </div>

                </div>

                <div v-if="centre" class="col-span-5 text-3xl flex justify-center items-center gap-1">
                    <i class="hrms hrms-center-outline text-gray-300 me-1"></i><span class="font-bold"> {{
                        centre?.code
                    }} - </span>
                    {{ centre?.name }}

                    <button
                        :disabled="isAtRightEnd"
                        @click="scrollToEnd"
                        class="btn btn-primary btn-sm ms-2"
                    ><i class="hrms hrms-positive"></i>
                        {{ translations?.general?.add_proposal }}
                    </button>
                </div>
                <div v-else class="col-span-1">
                    <span class="font-bold"> {{ department?.name }} </span>
                </div>
                <div v-if="nextCentreData && nextCentreData.url?.length > 0"
                     class="col-span-1 text-end pe-3">
                    <a class="btn btn-secondary btn-outline gap-1 text-xl text-secondary items-center border-neutral/15"
                       :href="nextCentreData?.url">
                        <i class="hrms hrms-arr-right me-2 text-gray-300 text-xl"></i>
                        <span class="font-bold">
                        {{ nextCentreData?.centre_code }}
                        </span>
                    </a>
                </div>

            </div>


        </section>
        <!--   Proposals     -->

        <section id="loadingProposals" ref="proposals" class="relative flex overflow-x-auto bg-base-200 h-full "
                 v-if="loadingData">
            <div class="border-r py-4 px-6 w-80 sticky left-0 z-10 bg-base-100 h-full flex flex-col gap-3">
                <div class="skeleton skeleton-animated h-8 w-full"></div>
                <div class="skeleton skeleton-animated h-4 w-3/4"></div>

                <div class="mt-3 flex flex-col gap-2" v-for="n in 6" :key="n">
                    <div class="skeleton skeleton-animated h-6 w-full"></div>
                    <div class="skeleton skeleton-animated h-4 w-3/4"></div>
                </div>
            </div>
        </section>

        <section id="proposals" ref="proposals"
                 class="relative flex overflow-x-auto min-h-[650px] custom-scroll"
                 @scroll="handleScroll"
                 v-if="!loadingData">

            <div class="absolute flex justify-center items-start inset-0 bg-base-100/50 opacity-70 z-50" v-if="loadingData">
                <span class="loading loading-spinner loading-lg mt-10"></span>
            </div>
            <div class="flex flex-col" ref="poposalsBody">

                <!--   Proposals header    -->
                <section id="header">
                    <div class="flex">
                        <div class="border-r border-b py-4 px-6 w-80 h-24 sticky left-0 z-10 bg-base-100">
                            <span class="font-bold text-2xl block">{{ translations?.general?.centre_users }}</span>
                            <!--              Default approver              -->
                            <div class="text-gray-500 mt-2" v-if="isFinalApprover">
                                {{ translations?.general?.final_approver }}
                            </div>
                            <div class="text-gray-500 mt-2" v-else>
                                {{ translations?.general?.approver }}:
                                <button @click="openSelectApproverModal"
                                        :class="[{'btn-error': !defaultApprover, 'btn-secondary btn-outline border-neutral/15': defaultApprover, 'pointer-events-none': Object.values(approvers).length <= 1}]"
                                        class="btn btn-xs">
                                    <span v-if="!defaultApprover">
                                    <i class="hrms hrms-alert me-1"></i>{{
                                            translations?.general?.not_assigned
                                        }}
                                </span>
                                    <span v-else> <i
                                        class="hrms hrms-share me-2 text-secondary"></i><span class="font-bold text-base-content">{{ defaultApprover?.full_name }}</span></span>
                                </button>
                            </div>
                        </div>
                        <transition-group name="scale-x" tag="div" class="flex">
                            <template v-for="proposal in uniqueProposals" :key="'header_'+proposal.id"
                            >
                                <div class="border-r border-b p-4 w-[412px] h-24 bg-base-100 sticky top-0 z-9"
                                     :id="'unique_header_'+proposal.id"
                                     :data-id="allProposals[proposal?.type]?.translation+' '+(allProposals[proposal?.type]?.subcases && allProposals[proposal?.type]?.subcases[proposal?.subtype] ? allProposals[proposal?.type]?.subcases[proposal?.subtype]?.translation : '') "
                                     v-if="(!isHiddenProposalColumnByUser(proposal.pairing_hash) || (!closableUniqueProposals[proposal.pairing_hash] || closableUniqueProposals[proposal.pairing_hash] && closableUniqueProposals[proposal.pairing_hash] === false)) && !tempLocalStorageHideColumn[proposal.pairing_hash]">
                                    <button class="absolute right-1 top-1 btn btn-primary btn-sm btn-circle"
                                            @click="hideProposalColumn(proposal.pairing_hash)"
                                            v-if="(closableUniqueProposals[proposal.pairing_hash] && closableUniqueProposals[proposal.pairing_hash] === true)">
                                        <i
                                            class="hrms hrms-close text-xs"></i></button>
                                    <div class="flex justify-between items-center">
                                        <span class="font-bold text-2xl block">
                                            <i :class="[allProposals[proposal?.type]?.icon, 'me-1 text-gray-400']"></i>{{
                                                allProposals[proposal?.type]?.translation
                                            }}
                                        </span>
                                        <!-- Retail competition link -->
                                        <universal-tooltip :text="translations?.general?.show_competition_report || 'Show competition report'" position="left">
                                            <a :href="retailCompetitionLink"
                                               target="_blank"
                                               rel="noopener noreferrer"
                                               v-if="allProposals[proposal?.type]?.subcases && allProposals[proposal?.type]?.subcases[proposal?.subtype] && allProposals[proposal?.type]?.subcases[proposal?.subtype].show_retail_competition_link == true">
                                                <i class="hrms hrms-sutaz-chart-outline text-gray-400 text-2xl hover:text-primary transition-all duration-300"></i>
                                            </a>
                                        </universal-tooltip>
                                    </div>

                                    <!--                 Subtype                   -->
                                    <div class="inline-flex gap-1 text-sm"
                                         v-if="allProposals[proposal?.type]?.subcases && allProposals[proposal?.type]?.subcases[proposal?.subtype]">
                                        <span class="text-gray-400"
                                        >{{ translations?.general?.type }}: </span>
                                        {{ allProposals[proposal?.type]?.subcases[proposal?.subtype]?.translation }}


                                    </div>
                                    <div
                                        v-if="allProposals[proposal?.type]?.disabled_by_validity">
                                        <span class="badge badge-soft text h-full text-xs font-medium text-secondary"
                                        >{{ translations?.general?.update_disabled_by_validity }}</span>


                                    </div>
                                    <!--                 Budget                   -->
                                    <div
                                        class="text-gray-400 text-sm inline-flex items-center justify-between w-full"
                                        v-if="allProposals[proposal?.type]?.subcases && allProposals[proposal?.type]?.subcases[proposal?.subtype]&& allProposals[proposal?.type]?.subcases[proposal?.subtype]['budget']">
                                        <div class="w-full">
                                            {{ translations?.general?.budget }}:
                                            <span
                                                :class="[{'text-base-content': actualBudgets[proposal.subtype] <= allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget}, {'text-error font-bold': actualBudgets[proposal.subtype] > allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget}]">{{
                                                    formatNumber(actualBudgets[proposal.subtype])
                                                }}</span> / {{
                                                formatNumber(allProposals[proposal?.type]?.subcases[proposal?.subtype]['budget']?.budget)
                                            }}
                                            {{
                                                allProposals[proposal?.type]?.subcases[proposal?.subtype]['budget']['currency']?.code
                                            }}
                                        </div>
                                        <div class="relative justify-end w-32 flex-col gap-1">
                                            <template v-if="actualBudgets[proposal.subtype] <= allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget">
                                                <span
                                                    :class="[`ms-[calc(${calculatedWidthForProgressBar(actualBudgets[proposal.subtype], allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget)-10}%-1.25rem)]`]"
                                                    class="absolute top-[-25px] progress-label text-xs">
                                                    <span>
                                                        {{
                                                            calculatedWidthForProgressBar(actualBudgets[proposal.subtype], allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget)
                                                        }}%
                                                    </span>
                                                </span>
                                                <div class="progress bg-primary/10 h-2" role="progressbar"
                                                     :aria-label=" allProposals[proposal?.type]?.subcases[proposal.subtype]['translation']+' - progressbar'"
                                                     :aria-valuenow="actualBudgets[proposal.subtype]" aria-valuemin="0"
                                                     :aria-valuemax="allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget"
                                                     :title="calculatedWidthForProgressBar(actualBudgets[proposal.subtype], allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget)+'%'">
                                                    <div
                                                        :class="['progress-bar', 'w-['+calculatedWidthForProgressBar(actualBudgets[proposal.subtype], allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget)+'%]', {'progress-error': actualBudgets[proposal.subtype] > allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget}, {'progress-primary': actualBudgets[proposal.subtype] <= allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget}]"
                                                        ></div>
                                                </div>
                                            </template>
                                            <template v-else>
                                                <span
                                                    class="absolute top-[-25px] right-0 progress-label text-xs bg-error text-white text-nowrap font-bold">
                                                    <i class="hrms hrms-alert"></i> {{ translations.general?.exceeded }}
                                                </span>
                                            </template>
                                        </div>

                                    </div>

                                </div>
                            </template>
                        </transition-group>
                    </div>
                </section>

                <!--   Proposal rows    -->
                <section id="rows">

                    <transition-group name="scale-y">
                        <row v-for="(user, index) in centreUsers"
                             :user="user"
                             :proposals="uniqueProposals"
                             :get-specific-proposal-route="getSpecificProposalRoute"
                             :currencies="currencies"
                             :selected-currency="proposerDefaultCurrency"
                             :create-route="createNewProposalsRoute"
                             :translations="translations.general"
                             :proposer="proposer"
                             :validity="humanReadableValidity"
                             :is-last-row="index === (Object.values(centreUsers).length -1)"
                             ref="row"
                             :key="user.full_name"
                             @show-full-proposal-history-in-modal="handleShowFullHistory"
                        >
                        </row>
                        <section id="addMoreUsers" key="addMoreUsersRow">
                            <div class="flex">
                                <!--          Add more users from other Centre          -->
                                <div
                                    class="border-r py-4 px-6 w-80 h-36 sticky left-0 bg-base-100 z-50 flex flex-col justify-between text-center overflow-visible">
                                    <div class="overflow-visible">
                                        <transition name="fade-slide" mode="out-in">
                                            <div key="button" class="flex justify-center mb-2" v-if="!addMoreUsersShow">
                                                <button @click.prevent="showAddMoreUsers()"
                                                        class="btn btn-outline btn-sm btn-secondary h-auto p-2 flex items-center gap-2 border-neutral/15">
                                                    <i class="hrms hrms-asign-task-circle text-gray-400 text-lg"></i>
                                                    <span class="text-xs font-medium">{{
                                                        translations?.general?.add_more_people
                                                    }}</span>
                                                </button>
                                            </div>

                                                                                        <div key="form" class="w-full overflow-visible" v-else>
                                                <div class="flex items-center justify-between mb-2">
                                                    <div class="flex items-center gap-1">
                                                        <i class="hrms hrms-asign-task text-gray-400"></i>
                                                        <span class="text-sm font-medium">
                                                            {{
                                                                translations?.general?.add_more_people
                                                            }}
                                                        </span>
                                                    </div>
                                                    <button @click.prevent="addMoreUsersShow = false"
                                                            class="btn btn-secondary btn-outline border-0 btn-xs">
                                                        <i class="hrms hrms-close text-xs"></i>
                                                    </button>
                                                </div>
                                                <label for="addMoreUsers" class="label-text text-xs mb-1 block">{{
                                                        translations?.add_more_people
                                                    }}</label>
                                                <div v-if="loadingUsersForAddMoreUsers"
                                                     class="input cursor-not-allowed flex items-center justify-center mb-2">
                                                    <span class="loading loading-spinner loading-md"></span>
                                                </div>
                                                <universal-select2 v-else
                                                    :disable-logic="true"
                                                    search-property="full_name_with_centres"
                                                    input-property="id"
                                                    :options="usersForAddMoreCentreUsers ? Object.values(usersForAddMoreCentreUsers) : {}"
                                                    :select-first="false"
                                                    :default-open-to-above="true"
                                                    @newValue="handleAddNewCentreUser"
                                                >
                                                </universal-select2>
                                            </div>
                                        </transition>
                                    </div>
                                    <transition name="fade-slide" mode="out-in">
                                        <button type="button"
                                                key="change-all-button"
                                                class="btn btn-secondary btn-outline btn-xs p-2 text-xs w-full flex flex-col items-center gap-1 h-auto border-neutral/15"
                                                @click="openProposalModalWithRightData( 'all')"
                                                v-if="!addMoreUsersShow">
                                            <i class="hrms hrms-table-change text-gray-400 text-lg"></i>
                                            <span class="text-xs font-medium">{{
                                                    translations?.general?.change_all
                                            }}</span>
                                        </button>
                                    </transition>
                                </div>
                                <transition-group name="scale-x">
                                    <template v-for="proposal in uniqueProposals" :key="'proposal_button_'+proposal.id">
                                        <div
                                            class="relative flex flex-col justify-end border-r p-4 w-[412px] h-36 bg-base-100"
                                            v-if="(!isHiddenProposalColumnByUser(proposal.pairing_hash) || (!closableUniqueProposals[proposal.pairing_hash] || closableUniqueProposals[proposal.pairing_hash] && closableUniqueProposals[proposal.pairing_hash] === false)) && !tempLocalStorageHideColumn[proposal.pairing_hash]"

                                        >
                                            <button type="button"
                                                    class="btn btn-secondary btn-outline btn-xs px-1 text-xs w-full flex flex-col items-center gap-1 h-auto p-2 border-neutral/15"
                                                    @click="openProposalModalWithRightData( 'column', allProposals[proposal?.type]?.translation, {proposal_type: proposal?.type, proposal_subtype: proposal?.subtype, proposal_hash: proposal?.pairing_hash})">
                                                    <i class="hrms hrms-all-column text-gray-400 text-lg"></i>
                                              <span class="text-xs font-medium">{{
                                                      translations?.general?.both_column
                                                }}</span>
                                            </button>
                                        </div>
                                    </template>

                                </transition-group>
                            </div>

                        </section>

                    </transition-group>

                </section>
                <div class="w-full h-full bg-base-200 border-t"></div>

            </div>

            <!--   Create form    -->
            <aside id="createForm" class="bg-base-200"
                   v-if="Object.values(translations).length > 0">
                <create-form
                    v-model:add-more-users="usersForAddMoreCentreUsers"
                    :approvers="approvers"
                    :centre_id="centre?.id"
                    v-model:centre-users="centreUsers"
                    :create-route="createNewProposalsRoute"
                    :currencies="currencies"
                    :department_id="department.id"
                    v-model:main-data-loaded="mainDataReloaded"
                    v-model:one-of-kind-user-groups="oneOfKindUserGroups"
                    :proposer="proposer"
                    :selected-currency="proposerDefaultCurrency"
                    :translations="translations.general"
                    v-model:types="allProposals"
                    :validity="actualYear+'-'+monthForCreateForm+'-01'"
                    @created="handleCreatedNewProposals"
                ></create-form>
            </aside>
            <button
                class="btn btn-primary btn-circle p-0 absolute z-10 transition-all duration-200 ease-in-out h-14 w-14"
                :style="{ top: scrollButtonTop + 'px', left: scrollButtonLeft + 'px' }"
                :disabled="!showScrollButton || isAtRightEnd"
                @click="scrollToRight"
                v-if="showScrollButton"
            >
                <img :src="arrowAnimatedCirclePrimary" alt="image" class="h-full"
                >
            </button>
        </section>
    </div>

    <!--  Proposal status change modal  -->
    <div id="proposalModal" class="overlay modal modal-middle overlay-open:opacity-100 overlay-open:duration-300 hidden"
         role="dialog" tabindex="-1">
        <div class="modal-dialog modal-dialog-lg overlay-open:opacity-100 overlay-open:duration-300">
            <div class="modal-content">
                <div class="modal-header flex-col justify-center">
                    <h3 class="modal-title">{{ translations?.general?.change_all }} {{ proposalModal?.title }}</h3>
                    <button type="button"
                            class="btn btn-text btn-circle btn-sm absolute end-[-10px] top-[-10px] bg-primary"
                            @click="closeProposalModal">
                        <span class="hrms hrms-close text-white"></span>
                    </button>
                    <div v-if="proposalModal.tableData.user"
                         class="grid gap-x-2 [grid-template-columns:41px_repeat(1,1fr)] mt-2">
                        <div class="col-span-1 row-span-2">
                            <img :src="employeeFormIcon ?? ''" alt="image"
                                 class="w-full">
                        </div>
                        <span class="col-span-1 font-bold">{{ proposalModal?.tableData?.user?.full_name }}</span>
                        <span class="text-gray-400 text-sm col-span-1" :title="userPositionsForTitleForStatusChange">{{
                                userPositionsForStatusChange && userPositionsForStatusChange[0] ? userPositionsForStatusChange[0] : ''
                            }} {{ Object.values(userPositionsForStatusChange).length > 1 ? '... ' : '' }}<i
                                class="hrms hrms-info-outline"
                                v-if=" Object.values(userPositionsForStatusChange).length > 1"></i>
                                                                    </span>
                    </div>
                    <div v-else-if="proposalModal.tableData.proposalType"
                         class="grid gap-x-2 [grid-template-columns:70px_repeat(1,1fr)] mt-2">
                        <div class="col-span-1">
                            <img :src="column ?? ''" alt="image"
                                 class="w-full">
                        </div>
                        <div class="col-span-1 flex items-center">
                            <div>
                                <span class="font-bold block">{{ centre?.code }}</span>
                                <span class="text-sm">{{ centre?.name }}</span>
                            </div>

                        </div>

                    </div>
                    <div v-else-if="proposalModal.tableData.isAllDataTable"
                         class="grid gap-x-2 [grid-template-columns:70px_repeat(1,1fr)] mt-2">
                        <div class="col-span-1">
                            <img :src="table ?? ''" alt="image"
                                 class="w-full">
                        </div>
                        <div class="col-span-1 flex items-center">
                            <div>
                                <span class="font-bold block">{{ centre?.code }}</span>
                                <span class="text-sm">{{ centre?.name }}</span>
                            </div>

                        </div>

                    </div>

                </div>
                <div class="modal-body overflow-x-auto">
                    <table class="table">
                        <thead
                            v-if="proposalModal?.tableData?.proposalNameInRow || proposalModal?.tableData?.proposalType"
                               class="bg-base-200">
                        <tr>
                            <th>{{
                                    proposalModal?.tableData?.proposalNameInRow ? translations?.general?.proposal : translations?.general?.employee
                                }}
                            </th>
                            <th>{{ translations?.general?.value }}</th>
                            <th>{{ translations?.general?.note }}</th>
                        </tr>
                        </thead>
                        <thead v-else-if="proposalModal?.tableData?.isAllDataTable">
                        <tr>
                            <th>{{
                                    proposalModal?.tableData?.proposalNameInRow ? translations?.general?.proposal : translations?.general?.employee
                                }}
                            </th>
                            <th v-for="uniqueProposal in uniqueProposals" :key="uniqueProposal.pairing_hash"
                                class="overflow-hidden text-ellipsis max-w-72">
                                <span class="flex gap-1 normal-case"><i
                                    :class="['text-gray-400',  allProposals[uniqueProposal.type]?.icon] "></i>{{
                                        allProposals[uniqueProposal.type]?.translation
                                    }}</span>
                                <span
                                    class="text-gray-400 text-xs normal-case"
                                    :title="uniqueProposal.subtype ?  allProposals[uniqueProposal.type]?.subcases[uniqueProposal.subtype]?.translation : ''">{{
                                        uniqueProposal.subtype ? '(' + allProposals[uniqueProposal.type]?.subcases[uniqueProposal.subtype]?.translation + ')' : ' '
                                    }}</span>
                            </th>
                        </tr>
                        </thead>
                        <tbody v-if="proposalModal.tableData.proposalNameInRow">
                        <tr v-for="uniqueProposal in uniqueProposals" :key="uniqueProposal.pairing_hash">
                            <td class="flex gap-1"><i
                                :class="[allProposals[uniqueProposal.type].icon, 'text-gray-400']"></i>{{
                                    allProposals[uniqueProposal.type]?.translation
                                }}
                                <span class="whitespace-normal line-clamp-1 text-gray-400"
                                      :title="allProposals[uniqueProposal.type]['subcases'][uniqueProposal.subtype]?.translation"
                                      v-if="uniqueProposal.subtype">
                                      v-if="uniqueProposal.subtype">
                                    {{
                                        '(' + allProposals[uniqueProposal.type]['subcases'][uniqueProposal.subtype]?.translation + ')'
                                    }}
                                </span>

                            </td>
                            <td>
                                <span v-if="!proposalModal?.tableData?.data[uniqueProposal.pairing_hash]">-</span>
                                <span
                                    v-else-if="proposalModal?.tableData?.data[uniqueProposal.pairing_hash] && allProposals[uniqueProposal.type]?.addition_enabled">
                                    {{
                                        formatNumber(proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.base)
                                    }} + {{
                                        formatNumber(proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.addition ?? 0)
                                    }} {{
                                        proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.currency?.code
                                    }}
                                </span>
                                <span v-else>
                                  {{
                                        formatNumber(proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.base)
                                    }} {{ proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.currency?.code }}
                                </span>
                            </td>
                            <td>
                                <span class="whitespace-normal line-clamp-1"
                                      :title="proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.logs[0]?.note"
                                      v-if="proposalModal?.tableData?.data[uniqueProposal.pairing_hash] && proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.logs && proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.logs[0]">
                                    {{ proposalModal?.tableData?.data[uniqueProposal.pairing_hash]?.logs[0]?.note }}
                                </span>
                            </td>
                        </tr>
                        </tbody>
                        <tbody v-else-if="proposalModal?.tableData?.proposalType">
                        <tr v-for="(data, employeeName) in proposalModal?.tableData?.data" :key="employeeName">
                            <td>{{ employeeName }}</td>
                            <td>
                                <span v-if="!data">-</span>
                                <span
                                    v-else-if="proposalModal?.tableData?.proposalType?.addition_enabled">
                                {{
                                        formatNumber(data.base)
                                    }} + {{
                                        formatNumber(data.addition ?? 0)
                                    }} {{
                                        data.currency?.code
                                    }}
                            </span>
                                <span v-else>
                                                              {{
                                        formatNumber(data.base)
                                    }} {{ data.currency?.code }}
                            </span>
                            </td>
                            <td>
                                <span class="whitespace-normal line-clamp-1"
                                      :title="data.logs[0].note"
                                      v-if="data && data.logs && data.logs[0]">
                                    {{ data.logs[0]?.note }}
                                </span>
                            </td>
                        </tr>
                        </tbody>
                        <tbody v-else-if="proposalModal.tableData.isAllDataTable">
                        <tr v-for="(data, employeeName) in proposalModal.tableData.data" :key="employeeName">
                            <td class="">{{ employeeName }}</td>
                            <td v-for="uniqueProposal in uniqueProposals" :key="uniqueProposal.pairing_hash">
                                <span v-if="!data[uniqueProposal.pairing_hash]">-</span>
                                <span
                                    v-else-if="allProposals[uniqueProposal.type].addition_enabled">
                                {{
                                        formatNumber(data[uniqueProposal.pairing_hash]?.base)
                                    }} + {{
                                        formatNumber(data[uniqueProposal.pairing_hash]?.addition ?? 0)
                                    }} {{
                                        data[uniqueProposal.pairing_hash]?.currency?.code
                                    }}
                            </span>
                                <span v-else>
                                                              {{
                                        formatNumber(data[uniqueProposal.pairing_hash]?.base)
                                    }} {{ data[uniqueProposal.pairing_hash]?.currency?.code }}
                            </span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer flex flex-col border-t items-center space-y-0.5">
                    <label for="proposalChangeStatusNote"
                           class="label-text text-secondary mb-1">{{ translations?.general?.and_add_detailed_description }}</label>
                    <textarea name="proposalChangeStatusNote" id="proposalChangeStatusNote"
                              v-model="proposalModal.updateData.note"
                              class="textarea min-h-8 h-16"></textarea>
                    <button type="button" class="btn btn-secondary btn-outline border-neutral/15 font-medium"
                            @click="confirmProposalModal('rollback')">
                        <i class="hrms hrms-sent-circle opacity-50"></i>
                        {{ translations?.general?.send_back_to_repair }}
                    </button>
                    <div>
                        <span class="whitespace-normal line-clamp-1"
                              :title="proposalModal.tableData.uniqueProposersForTitle">
                            {{ translations?.general?.created_by }}:
                            <template v-for="(proposer, proposerIndex) in proposalModal.tableData.uniqueProposers" :key="proposerIndex">
                                <i class="hrms hrms-vermont-ucko text-gray-300"></i> {{ proposer }},
                            </template>
                        </span>
                    </div>
                    <div v-if="proposalModal.exceededBudgets && Object.values(proposalModal.exceededBudgets).length > 0"
                         class="bg-red-100 border border-error p-2 rounded-md text-error">
                        <span class="whitespace-normal line-clamp-1 flex align-middle gap-1"
                              :title="exceededBudgetsString">
                            <i class="hrms hrms-alert text-xl"></i> {{
                                translations?.general?.exceeded_error_text
                            }}: <span
                            class="font-bold">{{
                                exceededBudgetsString
                            }}</span></span>
                    </div>
                    <div class="flex justify-center space-x-2">
                        <button type="button" class="group btn btn-secondary btn-outline border-neutral/15 font-bold text-base-content hover:bg-error hover:text-white"
                                @click="confirmProposalModal('reject')">
                            <i
                                class="hrms hrms-not-acepted-circle text-error group-hover:text-white"></i>
                            {{ translations?.general?.reject }}
                        </button>

                        <button type="button" class="group btn btn-secondary btn-outline border-neutral/15 font-bold text-base-content hover:bg-success hover:text-white"
                                @click="confirmProposalModal('approve')">
                            {{ translations?.general?.approve }} <i
                            class="hrms hrms-acepted-circle text-success group-hover:text-white"></i>
                        </button>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!--  Proposal detail modal  -->
    <div id="proposalDetailModal"
         class="overlay modal modal-middle overlay-open:opacity-100 overlay-open:duration-300 hidden"
         role="dialog" tabindex="-1">
        <div class="modal-dialog modal-dialog-md overlay-open:opacity-100 overlay-open:duration-300">
            <div class="modal-content over">
                <div class="modal-header">
                    <h3 class="modal-title font-bold">{{ translations?.general?.detail }} - {{
                            proposalDetailModal?.proposalName
                        }}</h3>
                    <button type="button"
                            class="btn btn-text btn-circle btn-sm absolute end-[-10px] top-[-10px] bg-primary"
                            @click="closeProposalDetailModal">
                        <span class="hrms hrms-close text-white"></span>
                    </button>
                </div>
                <div class="modal-body">
                    <!--         User name and positions           -->
                    <div class="flex justify-center mb-3">
                        <div class="grid [grid-template-columns:48px_repeat(3,1fr)] items-center">
                            <div class="row-span-2 col-span-1 h-full me-2 inline-flex items-start justify-end">
                                <div class="avatar avatar-placeholder">
                                    <div class="bg-warning/10 text-warning w-10 rounded-full">
                                        <span class="text-md uppercase">{{
                                                proposalDetailModal?.cellData?.user?.last_name[0]
                                            }}{{ proposalDetailModal?.cellData?.user?.first_name[0] }}</span>
                                    </div>
                                </div>
                            </div>
                            <span class="font-bold col-span-3">{{
                                    proposalDetailModal?.cellData?.user?.full_name
                                }}</span>
                            <span class="text-gray-400 text-sm col-span-3" :title="userPositionsForTitle">{{
                                    userPositions && userPositions[0] ? userPositions[0] : ''
                                }} {{ Object.values(userPositions).length > 1 ? '... ' : '' }}<i
                                    class="hrms hrms-info-outline" v-if=" Object.values(userPositions).length > 1"></i>
                                                                    </span>

                        </div>
                    </div>

                    <!--         Currency           -->
                    <div class="flex mb-2 items-center gap-6">
                        <label for="currency_id1" class="label-text font-bold">{{
                                translations?.general?.currency
                            }}:</label>
                        <div class="flex items-center gap-1" v-for="currency in currencies" :key="currency.id">

                            <input type="radio" name="currency_id" class="radio radio-primary radio-inset radio-xs"
                                   :id="'currency_id'+currency.id" :value="currency.id"
                                   v-model="proposalDetailModal.cellData.currency_id"
                            />
                            <label class="label-text text-base"
                                   :class="[proposalDetailModal?.cellData?.currency_id == currency.id ? 'font-bold' : 'text-secondary']"
                                   :for="'currency_id'+currency.id"> {{
                                    currency.code
                                }} </label>
                        </div>
                    </div>
                    <!--          Base/Addition          -->
                    <div class="flex gap-1 mb-2">
                        <!--         Base           -->
                        <div class="w-full flex flex-col justify-end">
                            <label for="base"
                                   :class="[{'text-xs ': proposalDetailModal?.cellData?.type && allProposals[proposalDetailModal?.cellData?.type]?.addition_enabled}]"
                                   class="label-text text-secondary mb-1">{{ translations.general?.base }}</label>
                            <div
                                class="relative w-full">
                                <number-input
                                    :value="proposalDetailModal?.cellData?.base"
                                    :classes="['input pr-12 block', {'font-bold': isProposalModalDetailInputChanged['base']}, {'is-invalid' : proposalDetailModal?.errors && proposalDetailModal?.errors?.base }]"
                                    @input-value-changed="inputValue => updateModalDetailInput(inputValue)"
                                    id="base"
                                    name="base"
                                    :required="true"
                                >
                                </number-input>

                                <span class="absolute inset-y-0 right-3 flex items-center text-gray-500 text-sm">
                            {{ currencies[proposalDetailModal.cellData.currency_id]?.code }}
                        </span>
                            </div>
                        </div>

                        <!--         Addition           -->
                        <div class="flex flex-col justify-end"
                             v-if="proposalDetailModal?.cellData?.type && allProposals[proposalDetailModal.cellData?.type]?.addition_enabled">
                            <label for="addition" class="label-text text-xs text-secondary mb-1">{{
                                    translations?.general?.addition
                                }}</label>
                            <div class="relative w-full">
                                <number-input
                                    :value="proposalDetailModal?.cellData?.addition"
                                    :classes="['input pr-12 block', {'font-bold': isProposalModalDetailInputChanged['addition']}, {'is-invalid' : proposalDetailModal?.errors && proposalDetailModal?.errors?.addition }]"
                                    @input-value-changed="inputValue => updateModalDetailInput(inputValue, 'addition')"
                                    id="addition"
                                    name="addition"
                                    :required="true"
                                >
                                </number-input>
                                <span class="absolute inset-y-0 right-3 flex items-center text-gray-500 text-sm">
                            {{ currencies[proposalDetailModal.cellData?.currency_id]?.code }}
                        </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-1 items-center"
                         v-if="proposalDetailModal?.cellData && allProposals[proposalDetailModal.cellData?.type] && allProposals[proposalDetailModal.cellData?.type]?.enable_paid_via_another_way">
                        <input type="checkbox" class="checkbox checkbox-xs checkbox-primary"
                               :id="'pay_via_another_way_'+proposalDetailModal.cellData.id"
                               v-model="proposalDetailModal.cellData.is_paid_via_another_way"/>
                        <label
                            class="label-text text-base"
                            :for="'pay_via_another_way_'+proposalDetailModal?.cellData?.id"
                            @input="updateModalDetailInput($event.target.value, 'enable_paid_via_another_way', false)">
                            {{ translations?.general?.is_paid_via_another_way }} </label>
                    </div>
                    <ul v-if="proposalDetailModal?.errors?.base || proposalDetailModal?.errors?.addition"
                        class="text-error text-xs">
                        <li v-for="(error, errorIndex) in (proposalDetailModal?.errors?.base ?? proposalDetailModal?.errors?.addition)" :key="errorIndex">
                            {{ error }}
                        </li>
                    </ul>
                    <!--         Reason           -->
                    <div>
                        <label for="reason" class="label-text">{{ translations.general?.reason }}</label>
                        <textarea :value="proposalDetailModal?.cellData?.reason"
                                  @input="updateModalDetailInput($event.target.value, 'reason', false)"
                                  aria-label="Textarea"
                                  id="reason"
                                  name="reason"
                                  rows="2"
                                  :class="[{'font-bold': isProposalModalDetailInputChanged['reason']}, {'is-invalid' : proposalDetailModal?.errors && proposalDetailModal?.errors?.reason }]"
                                  class="textarea textarea-sm min-h-8 h-8 mb-2">
                        </textarea>
                        <ul v-if="proposalDetailModal?.errors?.reason"
                            class="text-error text-xs">
                            <li v-for="(error, errorIndex) in proposalDetailModal?.errors?.reason" :key="errorIndex">
                                {{ error }}
                            </li>
                        </ul>
                        <!--            Note            -->
                        <label for="proposalNote"
                               class="label-text">{{ translations?.general?.and_add_detailed_description }}</label>
                        <textarea name="proposalNote" id="proposalNote" v-model="proposalModal.updateData.note"
                                  class="textarea textarea-sm min-h-14"></textarea>
                    </div>

                    <div class="grid grid-cols-12">
                        <div class="col-span-1 row-span-2 flex flex-col items-center">
                            <i class="hrms hrms-note-last text-gray-400 text-xl"></i>
                        </div>
                        <div class="col-span-11">
                            <p class="text-base-content text-sm line-clamp-1"
                               :title="proposalDetailModal?.cellData?.first_note"
                               v-if="proposalDetailModal?.cellData?.first_note">
                                {{ proposalDetailModal?.cellData?.first_note }}
                            </p>
                            <p class="text-gray-400 text-sm mb-2 line-clamp-1 flex gap-1"
                               v-if="proposalDetailModal?.cellData?.logs && proposalDetailModal?.cellData?.logs[0]">
                                <i class="hrms hrms-vermont-ucko" v-if="proposalDetailModal?.cellData?.first_note"></i>
                                {{ proposalDetailModal?.cellData?.logs[0].user?.full_name }}
                            </p>
                        </div>

                    </div>
                </div>
                <div class="modal-footer flex flex-col items-center space-y-0.5">
                    <ul v-if="proposalDetailModal?.errors"
                        class="text-error text-xs">
                        <template v-for="(errors, index) in proposalDetailModal.errors" :key="index">
                            <template v-if="typeof errors === 'object' && index != 'base' && index != 'reason' && index != 'addition'">
                                <li v-for="(message, messageIndex) in errors" :key="`${index}-${messageIndex}`"> {{ message }}
                                </li>
                            </template>
                            <li v-else-if="typeof errors !== 'object'" :key="`${index}-single`">
                                {{ errors }}
                            </li>
                        </template>
                    </ul>
                    <!--                    <button type="button" class="btn btn-soft btn-secondary" @click="closeProposalDetailModal">-->
                    <!--                        {{ translations.general?.cancel }}-->
                    <!--                    </button>-->
                    <button type="button" class="btn btn-secondary btn-outline border-neutral/15 font-medium"
                            @click="confirmProposalModal('rollback')">
                        <i class="hrms hrms-sent-circle opacity-50"></i>
                        {{ translations?.general?.send_back_to_repair }}
                    </button>
                    <div>
                        {{ translations?.general?.created_by }}: <span class="opacity-50">- <i
                        class="hrms hrms-vermont-ucko"></i></span> {{
                            proposalDetailModal?.cellData?.proposer?.full_name
                        }}
                    </div>
                    <div class="flex justify-center gap-3">
                        <button type="button" class="group btn btn-secondary btn-outline border-neutral/15 font-bold text-base-content hover:bg-error hover:text-white"
                                @click="confirmProposalModal('reject')">
                            <i
                                class="hrms hrms-not-acepted-circle text-error group-hover:text-white"></i>
                            {{ translations?.general?.reject }}
                        </button>

                        <button type="button" class="group btn btn-secondary btn-outline border-neutral/15 font-bold text-base-content hover:bg-success hover:text-white"
                                @click="confirmProposalModal('approve')">
                            {{ translations?.general?.approve }} <i
                            class="hrms hrms-acepted-circle text-success group-hover:text-white"></i>
                        </button>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!--  Select approver modal  -->
    <div id="selectApprover"
         class="overlay modal modal-middle overlay-open:opacity-100 overlay-open:duration-300 hidden"
         role="dialog" tabindex="-1">
        <div
            class="modal-dialog modal-dialog-md overlay-open:opacity-100 overlay-open:duration-300 overflow-visible">
            <div class="modal-content overflow-visible">
                <div class="modal-header">
                    <h3 class="modal-title">{{ translations?.general?.select_approver }}</h3>
                    <button type="button"
                            class="btn btn-text btn-circle btn-sm absolute end-[-10px] top-[-10px] bg-primary"
                            @click="closeSelectApproverModal">
                        <span class="hrms hrms-close text-white"></span>
                    </button>
                </div>
                <div class="w-full flex flex-col items-center">
                    <img :src="supervisor ?? ''" alt="image"
                         class="w-[98px]">
                    <span>{{ translations?.general?.send_proposals_to }}</span>
                </div>

                <div class="modal-body overflow-visible">
                    <div class="flex flex-col w-full items-center mb-2"
                         v-for="approver in approvers" :key="approver.id">
                        <label class="custom-option flex sm:w-2/3 flex-row items-start gap-3"
                        >
                            <input type="radio" name="'select_approver" class="radio radio-primary mt-2"
                                   v-model="approverSelectorId" :value="approver.id"
                            />
                            <img :src="supervisor ?? ''" alt="image"
                                 class="w-[54px]">
                            <span class="label-text w-full text-start">
                              <span class="flex justify-between mb-1">
                                <span class="text-base">{{ approver.full_name }}</span>
                              </span>
                              <span class="line-clamp-1 text-gray-400 text-sm"
                                    :title="createUserPositionsForTitle(approver)">{{
                                      createUserPositionsForTitle(approver)
                                  }}</span>
                            </span>
                        </label>

                    </div>
                </div>
                <div class="modal-footer flex justify-between">
                    <button type="button" class="btn btn-soft btn-secondary" @click="closeSelectApproverModal">
                        {{ translations?.general?.cancel }}
                    </button>
                    <button type="button" class="btn btn-primary" @click="confirmSelectApproverModal">
                        {{ translations?.general?.confirm }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!--  Usurp modal  -->
    <div id="usurpModal"
         class="overlay modal modal-middle overlay-open:opacity-100 overlay-open:duration-300 hidden"
         role="dialog" tabindex="-1">
        <div
            class="modal-dialog modal-dialog-md overlay-open:opacity-100 overlay-open:duration-300 overflow-visible">
            <div class="modal-content overflow-visible">
                <div class="modal-header justify-start gap-2 items-center">
                    <img :src="supervisor ?? ''"
                         alt="image"
                         class="w-11 rounded-full"/>
                    <div class="flex flex-col">
                        <h3 class="modal-title">
                            <span>{{ translations?.general?.usurp_title }}</span>
                        </h3>
                        <span class="text-sm text-gray-600">{{ translations?.general?.usurp_description }}</span>
                    </div>

                    <button type="button"
                            class="btn btn-text btn-circle btn-sm absolute end-[-10px] top-[-10px] bg-primary"
                            @click="closeUsurpModal">
                        <span class="hrms hrms-close text-white"></span>
                    </button>
                </div>
                <div class="modal-body overflow-visible">
                    <div class="w-full flex flex-inline justify-center">
                        <img :src="appropriate_request ?? ''"
                             alt="image"
                             class="w-24 rounded-full"/>
                        <div class="flex flex-col">
                            <span class="text-lg font-bold">{{ usurpModal.type }}</span>
                            <span v-if="usurpModal.subtype" class="text-xs">
                                {{ translations?.general?.subtype }}:
                                <span class="font-bold">{{ usurpModal.subtype }}</span>
                            </span>
                            <span v-if="usurpModal.user_full_name" class="text-xs">
                                {{ translations.general?.user }}:
                                <span class="font-bold">{{ usurpModal.user_full_name }}</span>
                            </span>
                        </div>


                    </div>
                </div>
                <div class="modal-footer flex justify-between">
                    <button type="button" class="btn btn-soft btn-secondary" @click="closeUsurpModal">
                        {{ translations?.general?.cancel }}
                    </button>
                    <button type="button" class="btn btn-primary" @click="confirmUsurpModal">
                        {{ translations?.general?.confirm }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- History Modal -->
    <universal-modal ref="historyModal" :show-modal-button="false" modal-window-classes="w-3/5 h-3/4">
        <template #header>

            <h3 class="modal-title text-lg font-semibold flex gap-1">
                <img :src="historyIcon" alt="image"
                     style="height: 25px">{{
                    historyModalTitle
                }}</h3>
        </template>
        <template #body>
            <div class="p-4 max-h-full">
                <proposal-timeline :logs="historyLogs" :proposal="currentProposalForHistory"
                                   :logs-loaded="historyLogsLoaded" :history-errors="historyErrors"/>
            </div>
            <!--            <div class="modal-footer p-3 border-t flex justify-end">-->
            <!--                <button @click="() => { if (this.$refs.historyModal) this.$refs.historyModal.close(); }"-->
            <!--                        class="btn btn-secondary">{{ translations.general?.close }}-->
            <!--                </button>-->
            <!--            </div>-->
        </template>
    </universal-modal>
</template>

<script>

/*  Space for import    */

import Row from "./Row.vue";
import CreateForm from "./CreateForm.vue";
import UniversalMultipleSelect2 from "../inputs/UniversalMultipleSelect2.vue";
import Cell from "./Cell.vue";
import UniversalSelect2 from "../inputs/UniversalSelect2.vue";
import {HSOverlay} from "flyonui/flyonui.js";
import employeeFormIcon from '../../../../images/form/employee-accept.svg';
import column from '../../../../images/form/column.svg';
import table from '../../../../images/form/table.svg';
import supervisor from '../../../../images/form/supervisor.svg';
import appropriate_request from '../../../../images/form/appropriate-request.svg';
import NumberInput from "../inputs/NumberInput.vue";
import UniversalModal from '../UniversalModal.vue';
import UniversalTooltip from "../UniversalTooltip.vue";
import ProposalTimeline from './Timeline.vue';
import historyIcon from '../../../../images/form/history.svg';
import arrowAnimatedCirclePrimary from '../../../../images/animated/double_arrows_circle_primary.svg';
export default {
    name: "remunerationProposalTable",
    components: {
        NumberInput,
        UniversalSelect2,
        Cell,
        UniversalMultipleSelect2,
        CreateForm,
        Row: Row,
        UniversalModal,
        UniversalTooltip,
        ProposalTimeline
    },
    provide() {
        return {
            eventBus: this
        }
    },
    props: {
        centre: {
            type: Object,
            default: {}
        },
        department: Object,
        section: Object,
        validity: {
            type: String,
            default: null
        },
        approvers: Object,
        selectedApproverId: Number,
        isFinalApprover: Boolean,
        previousCentreData: {
            type: Object
        },
        nextCentreData: {
            type: Object
        }
    },
    data() {
        return {
            areFirstTimeColumnsClosed: false,
            arrowAnimatedCirclePrimary,
            baseRetailCompetitionLink: '',
            currentProposalForHistoryTitle: '',
            changeAllModalVisible: false,
            changeAllModalType: '',
            changeAllModalSubtype: '',
            changeAllModalData: null,
            ignoreRowId: null,
            usersWithProposals: [],
            headersObserver: null,
            headerProposalsWithUniqueProposals: [],
            historyErrors: [],
            historyIcon,
            historyLogs: [],
            historyLogsLoaded: false,
            historyLogsRoute: '',
            historyModalTitle: '',
            loading: false,
            reloadAllMainData: false,
            defaultNewProposals: [],
            isDataLoaded: false,
            currentEditingProposals: {},
            errors: {},
            actualBudgets: {},
            actualMonth: null,
            actualYear: null,
            addMoreUsersShow: false,
            allProposals: {},
            appropriate_request,
            approverSelectorId: null,
            autoHideAddMoreUsersTimer: null,
            centreUsers: {},
            column,
            closableUniqueProposals: {},
            createNewProposalsRoute: '',
            currencies: {},
            debounceTimer: null,
            defaultApprover: {},
            employeeFormIcon,
            getSpecificProposalRoute: '',
            isAtRightEnd: false,
            isProposalModalDetailInputChanged: {addition: false, base: false, reason: false},
            loadingData: false,
            loadingUsersForAddMoreUsers: false,
            localStorageCentreUsersName: '',
            mainDataReloaded: false,
            proposerDefaultCurrency: null,
            proposer: null,
            proposalDetailModal: {
                cellData: {currency_id: null, base: null, addition: null, reason: null}
            },
            proposalModal: {
                title: '',
                updateData: {
                    note: '',
                    approver_id: null,
                    pay_out_on_agreement: false,
                    without_informing_payroll: false
                },
                decisionData: {},
                tableData: {
                    proposalNameInRow: false,
                    data: {}
                },
                exceededBudgets: {}
            },
            scrollButtonTop: 0,
            scrollButtonLeft: 0,
            scrollButtonToPosition: 0,
            showScrollButton: false,
            statuses: {},
            supervisor,
            table,
            tempLocalStorageHideColumn: {},
            translations: {},
            userProposals: {},
            usersForAddMoreCentreUsers: {},
            usurpModal: {proposal_id: null, type: null, subtype: null, user_full_name: null},
            uniqueProposals: {},
            visibleObserverElements: new Set(),
            currentProposalForHistory: null,
        }
    },

    watch: {
        uniqueProposals(newValue) {
            setTimeout(() => {
                this.recalculateBudgets()
            }, 300);
        },
    },
    beforeMount() {
        if (this.actualYear === null || this.actualMonth === null) {
            this.getActualDate()
        }
        const locallyStoredApprover = sessionStorage.getItem('remunerations.proposals.list.approver')
        this.setDefaultApprover(locallyStoredApprover ?? this.selectedApproverId, false)
        let promises = []
        promises.push(axios.post('/api/v1/employee/remunerations/get-all-necessary-static-data', {
            section_id: this.section?.id,
            department_id: this.department?.id,
            centre_id: this.centre?.id,
            validity: this.actualYear + '-' + this.monthForCreateForm + '-01'
        }).then(response => {
            let data = response.data
            this.translations = data.translations
            this.currencies = data.currencies
            this.createNewProposalsRoute = data.createProposalsRoute
            this.getSpecificProposalRoute = data.getSpecificProposalRoute
            this.historyLogsRoute = data.getProposalLogsRoute
            this.proposer = data.proposer
            this.statuses = response.data.enums?.ProposalStatus?.cases
            this.allProposals = response.data.enums?.ProposalType?.cases
            this.baseRetailCompetitionLink = response.data.baseRetailCompetitionUrl
            if (this.isFinalApprover) {
                this.defaultApprover = this.proposer
            }
        }).catch(errorResponse => {
            this.loadingData = false
            // console.log(errorResponse)
        }))
        promises.push(this.getDataForForm())

        Promise.all(promises).then(() => {
            setTimeout(() => {
                this.loadingData = false
            }, 300)
        })
    },
    mounted() {
        if (this.addMoreUsersShow) {
            this.addMoreUsersShow = false
        }
        this.watchColumnVisibility()
        this.recalculateBudgets()
        window.addEventListener('resize', this.checkScrollable);
        this.updateButtonPosition();
        window.addEventListener('scroll', this.updateButtonPosition);
        this.$refs.proposals.addEventListener('scroll', this.updateButtonPosition);
    },
    beforeUnmount() {
        window.removeEventListener('resize', this.checkScrollable);
        window.removeEventListener('scroll', this.updateButtonPosition);
        this.$refs.proposals.removeEventListener('scroll', this.updateButtonPosition);

    },

    methods: {
        getActualDate() {
            const now = this.validity ? new Date(this.validity) : new Date();
            now.setDate(1)
            this.actualYear = now.getFullYear();
            this.actualMonth = now.getMonth();
        },
        changeActualDate(stepInMonths) {
            const date = this.validity ? new Date(this.validity) : new Date(this.actualYear, this.actualMonth, 1);
            date.setMonth(date.getMonth() + 1 + stepInMonths)
            let month = date.getMonth()
            let year = date.getFullYear()
            if (month === 0) {
                month = 12
                year = year - 1
            }

            if (month < 10) {
                month = `0${month}`
            }

            const params = new URLSearchParams(window.location.search);
            params.set('validity', `${year}-${month}-01`);
            window.location.href = `${window.location.pathname}?${params.toString()}`;
        },
        getDataForForm(withLoadingData = true, isInPromise = false) {
            this.loadingData = withLoadingData
            this.mainDataReloaded = false


            return axios.post('/api/v1/employee/remunerations/get-all-necessary-dynamic-data', {
                section_id: this.section?.id,
                department_id: this.department?.id,
                centre_id: this.centre?.id,
                validity: this.actualYear + '-' + this.monthForCreateForm + '-01'
            }).then(response => {
                let data = response.data
                this.centreUsers = data.usersForForm
                this.userProposals = data.proposals
                this.proposerDefaultCurrency = data.proposerDefaultCurrency
                this.tempLocalStorageHideColumn = {}
                this.uniqueProposals = this.getUniqueProposals()
                this.recalculateClosableProposalHeaders()

                this.localStorageCentreUsersName = 'remunerations_proposals_' + this.proposer?.id + '_' + this.centre?.id + '_' + this.actualYear + '_' + this.actualMonth

                /*  Add users from localStorage */
                const localUsersToAdd = Object.values(localStorage.getItem(this.localStorageCentreUsersName) ? JSON.parse(localStorage.getItem(this.localStorageCentreUsersName)) : [])
                if (localUsersToAdd.length > 0) {

                    if (Object.values(this.usersForAddMoreCentreUsers).length === 0) {
                        axios.post('/api/v1/employee/remunerations/get-all-necessary-dynamic-data/1', {
                            section_id: this.section?.id,
                            department_id: this.department?.id,
                            centre_id: this.centre?.id,
                            validity: this.actualYear + '-' + this.monthForCreateForm + '-01',
                            user_ids: localUsersToAdd,
                            get_only_specified_user_ids: true,
                        }).then(response => {
                            let data = response.data
                            this.usersForAddMoreCentreUsers = data.usersForAddMorePeople
                            localUsersToAdd.forEach(localUserId => {
                                let foundUser = Object.values(this.usersForAddMoreCentreUsers).find(user => user.id == localUserId)
                                if (foundUser) {
                                    this.handleAddNewCentreUser(foundUser, false)
                                }
                            })
                        }).catch(errorResponse => {
                            // console.log(errorResponse)
                        })
                    }
                    localUsersToAdd.forEach(localUserId => {
                        let foundUser = Object.values(this.usersForAddMoreCentreUsers).find(user => user.id == localUserId)
                        if (foundUser) {
                            this.handleAddNewCentreUser(foundUser)
                        }
                    })

                }
                if (!isInPromise) {
                    this.loadingData = false
                }
                this.mainDataReloaded = true
                setTimeout(() => {
                    this.checkScrollable()
                    this.updateButtonPosition()

                }, 700)
            }).catch(errorResponse => {
                this.mainDataReloaded = true
                this.loadingData = false
                // console.log(errorResponse)
            })
        },
        getUniqueProposals() {
            return this.userProposals && Object.values(this.userProposals)
                ? Object.values(this.userProposals).filter((obj, index, self) =>
                        index === self.findIndex((t) => (
                        t.type === obj.type && t.subtype === obj.subtype && t.pairing_hash === obj.pairing_hash
                        ))
                )
                : []
        },
        handleCreatedNewProposals(data) {
            const nextKey = Object.keys(this.uniqueProposals).length;
            this.getDataForForm(false)
            // this.uniqueProposals = {...this.uniqueProposals, [nextKey]: {...data}}
        },
        handleNewProposalsFoundEvent(data) {
            const nextKey = Object.keys(this.userProposals).length;
            this.userProposals = {...this.userProposals, [nextKey]: {...data}}

        },
        setDefaultApprover(newApproverId = null, updateSession = true) {
            if (newApproverId !== null && updateSession === true) {
                sessionStorage.setItem('remunerations.proposals.list.approver', newApproverId)
            }
            this.defaultApprover = Object.values(this.approvers).find(approver => {
                return approver.id == newApproverId
            })
            this.approverSelectorId = this.defaultApprover?.id
        },
        handleAddNewCentreUser(data, autoHideAddMoreUsers = true) {
            if (autoHideAddMoreUsers) {
                this.autoHideAddMoreUsers()
            }
            const centreUsersNextKey = Object.keys(this.centreUsers).length;
            let localCentreUsers = JSON.parse(localStorage.getItem(this.localStorageCentreUsersName))
            const localCentreUsersNextKey = Object.keys(localCentreUsers ?? []).length;

            localCentreUsers = !localCentreUsers ? [] : localCentreUsers
            if (!Object.values(localCentreUsers).includes(data.id)) {
                localCentreUsers[localCentreUsersNextKey] = data.id
            }
            localStorage.setItem(this.localStorageCentreUsersName, JSON.stringify(localCentreUsers))
            this.centreUsers[centreUsersNextKey] = data
            this.addMoreUsersShow = false

            this.usersForAddMoreCentreUsers = Object.values(this.usersForAddMoreCentreUsers).filter(user => {
                return data.id !== user.id
            })
            if (autoHideAddMoreUsers) {
                setTimeout(() => {
                    this.addMoreUsersShow = true
                }, 10);
            }

        },
        /*  if proposalModal.data object contains:
         user_id => model confirm change state for all user proposals (in row),
         proposal_type + proposal_subtype change state for all proposals in column with this type and subtype,
         proposal_id change state only for one cell with this specific proposal ID  */
        openProposalModalWithRightData(dataType, dataName, data, preventModalOpen = false) {
            this.prepareDataForStatusChangeModal(dataType, data)
            this.watchProposalModalClose()
            this.proposalModal.exceededBudgets = {}
            const name = dataType === 'column' ? (data.proposal_subtype ? dataName + ' (' + this.allProposals[data.proposal_type]?.subcases[data.proposal_subtype]?.translation + ')' : dataName) : ''
            const modal = new HSOverlay(document.querySelector('#proposalModal'))
            if (dataType === 'all') {
                this.proposalModal.title = ''
            } else if (dataType === 'row') {
                this.proposalModal.title = this.translations?.general?.for
            } else {
                this.proposalModal.title = `- ${name}`
            }
            this.proposalModal.decisionData = dataType === 'all' ? {all: true} : data
            this.proposalModal.updateData.note = ''
            this.proposalModal.updateData.pay_out_on_agreement = false
            this.proposalModal.updateData.without_informing_payroll = false
            if (['row', 'all'].includes(dataType)) {
                Object.entries(this.actualBudgets).forEach(actualBudget => {
                    // allProposals[proposal?.type].subcases[proposal.subtype]['budget'].budget
                    let rightProposalSubtype = null
                    const rightProposalType = Object.values(this.allProposals).find(proposal => {
                        if (!proposal.subcases) {
                            return false
                        }
                        rightProposalSubtype = Object.values(proposal.subcases).find(subcase => {
                            return subcase.value == actualBudget[0]
                        })
                        return rightProposalSubtype != null
                    })
                    if (rightProposalSubtype && rightProposalSubtype.budget?.budget < actualBudget[1]) {
                        this.proposalModal.exceededBudgets[actualBudget[0]] = rightProposalType.translation + ' - ' + rightProposalSubtype.translation
                    }
                })
            } else { // dataType === 'column'
                let rightProposalSubtype = null
                const rightProposalType = Object.values(this.allProposals).find(proposal => {
                    if (!proposal.subcases) {
                        return false
                    }
                    rightProposalSubtype = Object.values(proposal.subcases).find(subcase => {
                        return subcase.value == data.proposal_subtype
                    })
                    return rightProposalSubtype != null
                })
                if (rightProposalSubtype && rightProposalSubtype.budget?.budget < this.actualBudgets[data.proposal_subtype]) {
                    this.proposalModal.exceededBudgets[data.proposal_subtype] = rightProposalType.translation + ' - ' + rightProposalSubtype.translation
                }
            }

            if (!preventModalOpen) {
                modal.open()
            }
        },
        closeProposalModal() {
            const modal = new HSOverlay(document.querySelector('#proposalModal'))
            const modalDetail = new HSOverlay(document.querySelector('#proposalDetailModal'))
            modal.close()
            if (modalDetail) {
                modalDetail.close()
            }
        },
        confirmProposalModal(actionType = null) {
            if (actionType !== null) {
                this.proposalModal.updateData.approver_id = actionType == 'approve' ? this.defaultApprover?.id : this.proposer?.id
                this.proposalModal.updateData.update_type = actionType
            }

            if (this.$refs.row) {
                this.processRefs(this.$refs.row, row => {
                    if (row.$refs && row.$refs.cell) {
                        this.processRefs(row.$refs.cell, cell => {
                            if (typeof cell.handleUpdateRequest === 'function') {
                                cell.handleUpdateRequest(this.proposalModal);
                            }
                        });
                    }
                });
            }
            this.closeProposalModal()
        },
        processRefs(ref, callback) {
            if (!ref) return;

            // if it is array
            if (Array.isArray(ref)) {
                ref.forEach(item => this.processRefs(item, callback));
            }
            // if it is object with refs
            else if (ref.$refs) {
                callback(ref);
            }
            // if it is direct component
            else {
                callback(ref);
            }
        },
        openSelectApproverModal() {
            const modal = new HSOverlay(document.querySelector('#selectApprover'))
            modal.open()
        },
        closeSelectApproverModal() {
            const modal = new HSOverlay(document.querySelector('#selectApprover'))
            modal.close()
        },
        confirmSelectApproverModal() {
            this.setDefaultApprover(this.approverSelectorId)
            this.closeSelectApproverModal()
        },
        openUsurpModal() {
            const modal = new HSOverlay(document.querySelector('#usurpModal'))
            modal.open()
        },
        closeUsurpModal() {
            const modal = new HSOverlay(document.querySelector('#usurpModal'))
            modal.close()
        },
        confirmUsurpModal() {
            if (this.$refs.row) {
                this.processRefs(this.$refs.row, row => {
                    if (row.$refs && row.$refs.cell) {
                        this.processRefs(row.$refs.cell, cell => {
                            cell.handleUpdateRequest({
                                decisionData: {proposal_id: this.usurpModal.proposal_id},
                                updateData: {update_type: 'usurp'}
                            }, false, true)
                        });
                    }
                });
            }
            this.closeUsurpModal()
        },
        autoHideAddMoreUsers() {
            clearTimeout(this.autoHideAddMoreUsersTimer);
            this.autoHideAddMoreUsersTimer = setTimeout(() => {
                this.addMoreUsersShow = false
            }, 30000);
        },
        showAddMoreUsers() {
            const localUsersToExclude = Object.values(localStorage.getItem(this.localStorageCentreUsersName) ? JSON.parse(localStorage.getItem(this.localStorageCentreUsersName)) : [])

            this.addMoreUsersShow = true
            if (Object.values(this.usersForAddMoreCentreUsers).length === 0) {
                this.loadingUsersForAddMoreUsers = true
                axios.post('/api/v1/employee/remunerations/get-all-necessary-dynamic-data/1', {
                    section_id: this.section?.id,
                    department_id: this.department?.id,
                    centre_id: this.centre?.id,
                    validity: this.actualYear + '-' + this.monthForCreateForm + '-01',
                    user_ids: localUsersToExclude
                }).then(response => {
                    let data = response.data
                    this.usersForAddMoreCentreUsers = data.usersForAddMorePeople
                    this.loadingUsersForAddMoreUsers = false
                    this.autoHideAddMoreUsers()
                }).catch(errorResponse => {
                    // console.log(errorResponse)
                })

                return
            }
            this.autoHideAddMoreUsers()
        },
        recalculateBudgets(subtype = null) {
            Object.values(this.uniqueProposals).forEach(uniqueProposal => {
                if (uniqueProposal.subtype != null && (subtype === null || subtype === uniqueProposal.subtype)) {
                    this.actualBudgets[uniqueProposal.subtype] = 0
                    if (this.$refs.row) {
                        this.processRefs(this.$refs.row, row => {
                            if (row.$refs && row.$refs.cell) {
                                this.processRefs(row.$refs.cell, cell => {
                                    if (cell.localProposal.subtype === uniqueProposal.subtype && cell.localProposal.status !== 5) {
                                        const userPositions = cell.user.valid_assigments?.flatMap(assigment => assigment.position)
                                        const excludeBudgetCalculations = userPositions.flatMap(position => position.excluded_for_budgets)
                                        const isExcludedFromBudgetCalculation = excludeBudgetCalculations.filter(excludeBudget => {
                                            return excludeBudget.subtype == cell.localProposal.subtype
                                        })

                                        const addValue = isExcludedFromBudgetCalculation.length > 0 ? 0 : Number(cell.localProposal.base) + Number(cell.localProposal.addition)
                                        this.actualBudgets[uniqueProposal.subtype] = this.actualBudgets[uniqueProposal.subtype] + addValue
                                    }
                                });
                            }
                        });
                    }
                }
            })
        },
        formatNumber(value) {
            return new Intl.NumberFormat('sk-SK', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            }).format(value);
        },
        calculatedWidthForProgressBar(actualValue, maxValue) {
            let percentage = Math.round((actualValue / maxValue) * 100)

            return percentage > 100 ? 100 : percentage
        },
        openProposalDetailModal() {
            const modal = new HSOverlay(document.querySelector('#proposalDetailModal'))
            modal.open()
            this.watchProposalDetailModalClose()
        },
        closeProposalDetailModal() {
            const modal = new HSOverlay(document.querySelector('#proposalDetailModal'))
            modal.close()
        },
        watchProposalDetailModalClose() {
            const modalEl = document.querySelector('#proposalDetailModal');

            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class') {
                        const isHidden = modalEl.classList.contains('hidden');
                        if (isHidden) {
                            this.proposalDetailModal = {
                                cellData: {currency_id: null, base: null, addition: null, reason: null}
                            }
                            this.isProposalModalDetailInputChanged = {addition: false, base: false, reason: false}
                            observer.disconnect();
                        }
                    }
                });
            });

            observer.observe(modalEl, {attributes: true});
        },
        watchProposalModalClose() {
            const modalEl = document.querySelector('#proposalModal');

            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class') {
                        const isHidden = modalEl.classList.contains('hidden');
                        if (isHidden) {
                            this.proposalModal = {
                                title: '',
                                updateData: {
                                    note: '',
                                    approver_id: null,
                                    pay_out_on_agreement: false,
                                    without_informing_payroll: false
                                },
                                decisionData: {},
                                tableData: {
                                    proposalNameInRow: false,
                                    data: {}
                                }
                            }
                            observer.disconnect();
                        }
                    }
                });
            });

            observer.observe(modalEl, {attributes: true});
        },
        updateModalDetailInput(value, name = 'base', isNumberInput = true) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                this.proposalDetailModal.cellData[name] = isNumberInput ? Number(value) : value
                this.isProposalModalDetailInputChanged[name] = true;
                if (this.$refs.row) {
                    this.processRefs(this.$refs.row, row => {
                        if (row.$refs && row.$refs.cell) {
                            this.processRefs(row.$refs.cell, cell => {
                                if (typeof cell.updateProposalFromParent === 'function') {
                                    cell.updateProposalFromParent(this.proposalDetailModal?.cellData);
                                }
                            });
                        }
                    });
                }
            }, 700);
        },
        prepareDataForStatusChangeModal(dataType, decisionData) {
            this.proposalModal.tableData.proposalNameInRow = false
            this.proposalModal.tableData.isAllDataTable = false

            let data = {}
            let uniqueProposers = {}
            let uniqueProposersForTitle = {}
            if (dataType === 'row') {
                this.proposalModal.tableData.proposalNameInRow = true
                const rightUser = this.centreUsers.find(centreUser => {
                    return centreUser.id === decisionData.user_id
                })
                this.proposalModal.tableData.user = rightUser
                Object.values(this.uniqueProposals).forEach(uniqueProposal => {
                    data[uniqueProposal.pairing_hash] = Object.values(this.userProposals).find(user_proposal => {
                        const isValidProposal = user_proposal.user_id == rightUser.id && user_proposal.type == uniqueProposal?.type && user_proposal.subtype == uniqueProposal?.subtype && user_proposal.pairing_hash == uniqueProposal?.pairing_hash && user_proposal.status != 4 && user_proposal.status != 5 && user_proposal.approver_id == this.proposer?.id

                        if (isValidProposal) {
                            uniqueProposers[user_proposal.proposer.id] = user_proposal.proposer.full_name
                        }

                        return isValidProposal
                    })
                })
            }

            if (dataType === 'column') {
                Object.values(this.centreUsers).forEach(centreUser => {
                    if (!data[centreUser.full_name]) {
                        data[centreUser.full_name] = {}
                    }
                    this.proposalModal.tableData.proposalType = this.allProposals[decisionData.proposal_type]
                    this.proposalModal.tableData.proposalSubType = decisionData.proposal_subtype ? this.proposalModal?.tableData?.proposalType?.subcases[decisionData.proposal_subtype] : null

                    data[centreUser.full_name] = Object.values(this.userProposals).find(user_proposal => {

                        const isValidProposal = user_proposal.user_id == centreUser.id && user_proposal.type == decisionData.proposal_type && decisionData.proposal_subtype == user_proposal.subtype && user_proposal.pairing_hash == decisionData.proposal_hash && user_proposal.status != 4 && user_proposal.status != 5 && user_proposal.approver_id == this.proposer?.id

                        if (isValidProposal) {
                            uniqueProposers[user_proposal.proposer.id] = user_proposal.proposer.full_name
                        }

                        return isValidProposal
                    })
                })
            }

            if (dataType === 'all') {
                this.proposalModal.tableData.isAllDataTable = true

                Object.values(this.centreUsers).forEach(centreUser => {
                    if (!data[centreUser.full_name]) {
                        data[centreUser.full_name] = {}
                    }
                    Object.values(this.uniqueProposals).forEach(uniqueProposal => {
                        data[centreUser.full_name][uniqueProposal.pairing_hash] = Object.values(this.userProposals).find(user_proposal => {

                            const isValidProposal = user_proposal.user_id == centreUser.id && user_proposal.type == uniqueProposal.type && uniqueProposal.subtype == user_proposal.subtype && user_proposal.pairing_hash == uniqueProposal.pairing_hash && user_proposal.status != 4 && user_proposal.status != 5 && user_proposal.approver_id == this.proposer?.id

                            if (isValidProposal) {
                                uniqueProposers[user_proposal.proposer.id] = user_proposal.proposer.full_name
                            }

                            return isValidProposal
                        })
                    })
                })
            }

            this.proposalModal.tableData.data = data
            this.proposalModal.tableData.uniqueProposers = uniqueProposers
            Object.values(uniqueProposers).forEach(uniqueProposer => {
                this.proposalModal.tableData.uniqueProposersForTitle = (this.proposalModal?.tableData?.uniqueProposersForTitle ?? '') + uniqueProposer + ', '
            })
        },
        createUserPositions(user) {
            let positions = []
            if (!user) {
                return positions
            }
            Object.values(user.valid_assigments).forEach(assigment => {
                if (assigment.position && !positions.includes(assigment.position.name)) {
                    positions.push(assigment.position.name)
                }
            })
            return positions
        },
        createUserPositionsForTitle(user) {
            let tempString = ''
            this.createUserPositions(user).forEach(position => {
                tempString = tempString + position + ', '
            })

            return tempString
        },
        scrollToRight() {
            const el = this.$refs.proposals;
            if (el) {
                el.scrollTo({
                    left: this.scrollButtonToPosition > 0 ? this.scrollButtonToPosition : el.scrollWidth,
                    behavior: 'smooth'
                });
            }
        },
        scrollToEnd() {
            const el = this.$refs.proposals;
            if (el) {
                el.scrollTo({
                    left: el.scrollWidth,
                    behavior: 'smooth'
                });
            }
        },
        checkScrollable() {
            const el = this.$refs.proposals;
            if (el) {
                this.showScrollButton = el.scrollWidth > el.clientWidth;
            }

            this.updateRightEndStatus()
        },
        handleScroll() {
            this.updateRightEndStatus();
            this.updateButtonPosition();
        },
        updateRightEndStatus() {
            const el = this.$refs.proposals;
            if (el) {
                this.isAtRightEnd = Math.abs(el.scrollWidth - el.clientWidth - el.scrollLeft) < 5;
            }
        },
        recalculateClosableProposalHeaders(type = null, subtype = null) {
            Object.values(this.uniqueProposals).forEach(uniqueProposal => {
                if (type !== null && uniqueProposal.type !== type) {
                    return
                }
                if (type !== null && subtype !== null && subtype !== uniqueProposal.subtype) {
                    return
                }
                const validProposals = Object.values(this.userProposals).filter(userProposal => {
                    return userProposal.status !== 5 && uniqueProposal.pairing_hash === userProposal.pairing_hash
                })
                this.closableUniqueProposals[uniqueProposal.pairing_hash] = Object.values(validProposals).length === 0

                if (!this.closableUniqueProposals[uniqueProposal.pairing_hash]) {
                    localStorage.removeItem('hide_' + uniqueProposal.pairing_hash + '_' + this.validity)
                    this.tempLocalStorageHideColumn[uniqueProposal.pairing_hash] = false
                }
            })

            if (!this.areFirstTimeColumnsClosed) {
                Object.entries(this.closableUniqueProposals).forEach(closableUniqueProposal => {
                    if (closableUniqueProposal[1]) {
                        this.hideProposalColumn(closableUniqueProposal[0])
                        this.areFirstTimeColumnsClosed = true
                    }
                })
            }
        },
        hideProposalColumn(pairingHash) {
            this.tempLocalStorageHideColumn[pairingHash] = true

            localStorage.setItem('hide_' + pairingHash + '_' + this.validity, true)
            setTimeout(() => {
                this.checkScrollable()
                this.handleScroll()
            }, 600)
        },
        isHiddenProposalColumnByUser(pairingHash) {
            return localStorage.getItem('hide_' + pairingHash + '_' + this.validity) ?? false
        },
        handleShowFullHistory(eventData) {

            if (eventData && eventData.logs && eventData.proposal && eventData.title) {
                this.historyLogsLoaded = false
                this.historyLogs = []
                this.historyErrors = []
                axios.post(this.historyLogsRoute + '/' + eventData.proposal.id).then(response => {
                    this.historyLogs = response.data.logs;
                    this.historyLogsLoaded = true
                }).catch(errorResponse => {
                    this.historyLogsLoaded = true
                    if (errorResponse.response?.data?.errors) {
                        this.historyErrors = errorResponse.response.data.errors
                    } else if (errorResponse.response?.data?.error) {
                        this.historyErrors.push([errorResponse.response.data.error])
                    } else if (errorResponse.response?.data?.message) {
                        this.historyErrors.push([errorResponse.response.data.message])
                    } else {
                        this.historyErrors.push([errorResponse])
                    }
                    // console.log(errorResponse)
                })
                this.currentProposalForHistory = eventData.proposal;
                this.historyModalTitle = eventData.title;
                if (this.$refs.historyModal) {
                    this.$refs.historyModal.open();
                }
            }
        },
        updateButtonPosition() {
            const section = this.$refs.proposals;
            if (!section) return;

            // VERTICAL POS
            const rect = section.getBoundingClientRect();
            const scrollY = window.scrollY || window.pageYOffset;

            const sectionTopAbsolute = rect.top + scrollY;
            const sectionHeight = section.offsetHeight;
            const viewportHeight = window.innerHeight;

            const visibleTop = Math.max(sectionTopAbsolute, scrollY);
            const visibleBottom = Math.min(sectionTopAbsolute + sectionHeight, scrollY + viewportHeight);
            const visibleMiddle = (visibleTop + visibleBottom) / 2;

            this.scrollButtonTop = visibleMiddle - sectionTopAbsolute;

            // HORIZONTAL POS
            const scrollLeft = section.scrollLeft;
            const clientWidth = section.clientWidth;


            this.scrollButtonLeft = scrollLeft + clientWidth - 64; // 48 = cca button size + margin
            this.resetObserver()
        },
        watchColumnVisibility() {
            this.observer = new IntersectionObserver(
                (entries) => {
                    const grouped = {}
                    // Cells by pairing_hash
                    entries.forEach(entry => {
                        const hash = entry.target.dataset.hash

                        if (!grouped[hash]) {
                            grouped[hash] = []
                        }

                        grouped[hash].push(entry)
                    })
                    let previousColumnVisibility = false
                    let shouldBeButtonVisible = false
                    let nextProposalToScroll = null

                    // Check if exists next hidden proposal column
                    this.uniqueProposals.forEach(uniqueProposal => {
                        if (nextProposalToScroll) {
                            return;
                        }
                        const visibleCells = grouped[uniqueProposal.pairing_hash]?.filter(e => e.isIntersecting)
                        if (previousColumnVisibility === true && visibleCells && visibleCells.length === 0) {
                            shouldBeButtonVisible = true
                            nextProposalToScroll = uniqueProposal
                            return;
                        }
                        previousColumnVisibility = !visibleCells ? previousColumnVisibility : (visibleCells.length > 0 ? true : false)
                    })

                    // Calculate scroll to right distance if next hidden proposal exists
                    if (nextProposalToScroll) {
                        const proposalEl = document.getElementById('unique_header_' + nextProposalToScroll.id)
                        const containerEl = this.$refs.proposals

                        const proposalRect = proposalEl.getBoundingClientRect()
                        const containerRect = containerEl.getBoundingClientRect()

                        const currentScrollLeft = containerEl.scrollLeft

                        const offsetLeft = proposalRect.left - containerRect.left
                        const offsetRight = proposalRect.right - containerRect.right

                        let deltaScroll = 0

                        if (offsetLeft < 0) {
                            deltaScroll = offsetLeft
                        } else if (offsetRight > 0) {
                            deltaScroll = offsetRight
                        }
                        this.scrollButtonToPosition = currentScrollLeft + deltaScroll
                        this.showScrollButton = true
                    } else {
                        this.scrollButtonToPosition = 0
                        this.showScrollButton = false
                    }
                },
                {
                    root: this.$refs.poposals,
                    rootMargin: '0px 0px 0px -700px',
                    threshold: 0.8
                }
            )
        },
        setObserverRef(el) {
            if (el) {
                this.observer.observe(el)
                this.visibleObserverElements.add(el)
            }
        },
        resetObserver() {
            this.observer.disconnect()
            this.visibleObserverElements.forEach(el => this.observer.observe(el))
        }
    },


    computed: {
        monthForCreateForm() {
            return (this.actualMonth + 1).toString().padStart(2, '0')
        },
        humanReadableValidity() {
            const date = this.validity ? new Date(this.validity) : new Date(this.actualYear, this.actualMonth, 1);
            date.setMonth(date.getMonth() + 1)
            let month = date.getMonth()
            let year = date.getFullYear()
            if (month === 0) {
                month = 12
                year = year - 1
            }

            if (month < 10) {
                month = `0${month}`
            }

            return `${year}-${month}-01`
        },
        oneOfKindUserGroups() {
            let oneOfKindSubCasesGroups = {}
            let userOneOfKindSubCasesGroups = {}
            Object.values(this.allProposals).forEach(proposal => {
                if (proposal.subcases) {
                    Object.values(proposal.subcases).forEach(subCase => {
                        if (subCase.one_of_kind_per_month_groups.length > 0) {
                            oneOfKindSubCasesGroups[subCase.value] = subCase.one_of_kind_per_month_groups
                        }
                    })
                }
            })
            Object.values(this.userProposals).forEach(userProposal => {
                if (userProposal.subtype) {
                    if (Object.keys(oneOfKindSubCasesGroups).includes(String(userProposal.subtype)) && userProposal.status !== 5) {
                        Object.values(oneOfKindSubCasesGroups[userProposal.subtype]).forEach(group => {
                            if (!userOneOfKindSubCasesGroups[userProposal.user_id]) {
                                userOneOfKindSubCasesGroups[userProposal.user_id] = {}
                            }
                            userOneOfKindSubCasesGroups[userProposal.user_id][group] = group
                        })
                    }
                }
            })

            return userOneOfKindSubCasesGroups

        },

        userPositions() {
            let positions = []
            if (!this.proposalDetailModal.cellData?.user) {
                return positions
            }
            Object.values(this.proposalDetailModal.cellData?.user?.valid_assigments).forEach(assigment => {
                if (assigment.position && !positions.includes(assigment.position.name)) {
                    positions.push(assigment.position.name)
                }
            })
            return positions
        },
        userPositionsForTitle() {
            let tempString = ''
            this.userPositions.forEach(position => {
                tempString = tempString + position + ', '
            })

            return tempString
        },
        userPositionsForStatusChange() {
            let positions = []
            if (!this.proposalModal.tableData?.user) {
                return positions
            }
            Object.values(this.proposalModal.tableData?.user?.valid_assigments).forEach(assigment => {
                if (assigment.position && !positions.includes(assigment.position.name)) {
                    positions.push(assigment.position.name)
                }
            })
            return positions
        },
        userPositionsForTitleForStatusChange() {
            let tempString = ''
            this.userPositionsForStatusChange.forEach(position => {
                tempString = tempString + position + ', '
            })

            return tempString
        },
        exceededBudgetsString() {
            if (!this.proposalModal.exceededBudgets) {
                return ''
            }
            let string = '';

            Object.values(this.proposalModal.exceededBudgets).forEach(exceededBudget => {
                string = string == '' ? exceededBudget : ', ' + exceededBudget
            })

            return string
        },
        retailCompetitionLink() {
            const preparedFilterData = this.centre?.code + '/' + this.actualYear + '-' + this.monthForCreateForm
            return this.baseRetailCompetitionLink.endsWith('/') ? this.baseRetailCompetitionLink + preparedFilterData : this.baseRetailCompetitionLink + '/' + preparedFilterData
        }
    }
};
</script>


<style>
.fade-slide-enter-from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
}

.fade-slide-leave-to {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
}

.fade-slide-enter-active {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.fade-slide-leave-active {
    transition: all 0.25s cubic-bezier(0.5, 0, 0.75, 0);
}

.fade-slide-enter-to,
.fade-slide-leave-from {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.scale-y-enter-from,
.scale-y-leave-to {
    transform: scaleY(0.1);
    opacity: 0;
}

.scale-y-enter-to,
.scale-y-leave-from {
    transform: scaleY(1);
    opacity: 1;
}

.scale-y-enter-active,
.scale-y-leave-active {
    transition: all 0.5s ease;
    transform-origin: top;
}

/*
.cell-wrapper {
    overflow: hidden;
}
*/

.scale-x-enter-from {
    transform: scaleX(0.1);
    opacity: 0;
}

.scale-x-enter-to {
    transform: scaleX(1);
    opacity: 1;
}

.scale-x-enter-active {
    transition: all 0.5s ease;
    transform-origin: left;
    position: relative;
}

.scale-x-leave-from {
    width: 412px;
    opacity: 1;
}

.scale-x-leave-to {
    width: 0;
    opacity: 0;
}

.scale-x-leave-active {
    transition: all 0.3s ease;
}

.scale-x-move {
    transition: all 1s ease;
}
.scale-y-move {
    transition: all 0.3s ease;
}


.custom-scroll::-webkit-scrollbar {
    height: 8px;
}

.custom-scroll::-webkit-scrollbar-track {
    background-color: rgba(var(--color-primary-rgb), 0.15);
    border-radius: 4px;
}

.custom-scroll::-webkit-scrollbar-thumb {
    background-color: var(--color-primary);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-primary-hover, var(--color-primary));
}

/* Firefox podpora */
.custom-scroll {
    scrollbar-color: var(--color-primary) #f1f1f1;
    scrollbar-width: thin;
}
</style>
