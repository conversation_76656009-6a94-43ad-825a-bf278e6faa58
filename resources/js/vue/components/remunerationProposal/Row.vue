<template>
    <section :id="'user_'+user.id">
        <div class="flex">
            <div :class="[{'border-b': !isLastRow}]" class="border-r py-4 px-6 w-80 h-32 sticky left-0 bg-base-100 z-20">
                <div class="grid [grid-template-columns:48px_repeat(4,1fr)]  items-center">
                    <div class="row-span-3 col-span-1 h-full me-2 inline-flex items-start">
                        <div class="avatar avatar-placeholder">
                            <div class="bg-warning/10 text-warning w-10 rounded-full">
                                <span class="text-md uppercase">{{ user.last_name[0] }}{{ user.first_name[0] }}</span>
                            </div>
                        </div>
                    </div>
                    <span class="font-bold col-span-4">{{ user.full_name }}</span>
                    <span class="text-gray-400 text-sm col-span-4" :title="userPositionsForTitle">{{
                            userPositions && userPositions[0] ? userPositions[0] : ''
                        }} {{ Object.values(userPositions).length > 1 ? '... ' : '' }} <i class="hrms hrms-info-outline"
                                                                                          v-if=" Object.values(userPositions).length > 1"></i>
                    </span>
                    <div class="col-span-4 flex gap-2">
                        <button
                            class="btn btn-secondary btn-outline btn-xs text-xs h-auto max-w-56 px-2 py-1 mt-1 border border-neutral/15"
                            @click="eventBus.openProposalModalWithRightData( 'row', user.full_name, {user_id: user.id})">
                            {{ translations?.change_all_for_employee }} <i
                            class="hrms hrms-selectall text-gray-400"></i>
                        </button>

                    </div>
                </div>
            </div>
            <!--            <transition-group :name="isInitialized ? 'scale-x' : ''" tag="div" class="flex">-->
            <div class="flex">
                <transition-group :name="isInitialized ? 'scale-x' : ''" tag="div" class="flex" appear>
                    <template v-for="proposal in proposals" :key="'cell_wrapper_'+proposal.pairing_hash">
                        <div :ref="eventBus.setObserverRef"
                             :data-hash="proposal.pairing_hash"
                            class="cell-wrapper"
                            v-if="shouldShowProposal(proposal)"
                            :key="(rightProposal(proposal) ? rightProposal(proposal)?.id+':'+proposal.pairing_hash : user.full_name+':'+proposal.pairing_hash)"
                        >
                            <cell
                                :proposal="rightProposal(proposal)"
                                :headerProposal="proposal"
                                :get-specific-proposal-route="getSpecificProposalRoute"
                                :user="user"
                                :currencies="currencies"
                                :selected-currency="selectedCurrency"
                                :create-route="createRoute"
                                :proposer="proposer"
                                :is-last-row="isLastRow"
                                :is-row-initialized="isInitialized"
                                ref="cell"
                                :key="'cell_'+(rightProposal(proposal) ? rightProposal(proposal)?.id+':'+proposal.pairing_hash : user.full_name+':'+proposal.pairing_hash)"
                                @request-show-full-history="$emit('show-full-proposal-history-in-modal', $event)"
                            />
                        </div>
                    </template>
                </transition-group>

            </div>

            <!--            </transition-group>-->
        </div>

    </section>
</template>

<script>

/*  Space for import    */

import Cell from "./Cell.vue";

export default {
    name: "row",
    components: {Cell},
    emits: ['show-full-proposal-history-in-modal'],
    inject: ['eventBus'],
    props: {
        createRoute: String,
        currencies: Object,
        getSpecificProposalRoute: String,
        isLastRow: Boolean,
        user: Object,
        proposals: Object,
        proposer: Object,
        selectedCurrency: {
            type: Number,
            default: null
        },
        translations: Object,
        validity: String,
    },
    data() {
        return {
            focusGroup: false,
            isInitialized: false

        }
    },
    watch: {},
    beforeMount() {
    },
    mounted() {
        this.isInitialized = true
    },
    beforeUnmount() {
    },
    methods: {
        rightProposal(proposal) {
            return this.user.remuneration_proposals.find(user_proposal => {
                return user_proposal.type == proposal?.type && user_proposal.subtype == proposal?.subtype && user_proposal.pairing_hash == proposal?.pairing_hash
            })
        },
        shouldShowProposal(proposal) {
            return (
                (!this.eventBus.isHiddenProposalColumnByUser(proposal.pairing_hash) ||
                    !this.eventBus.closableUniqueProposals[proposal.pairing_hash] ||
                    (this.eventBus.closableUniqueProposals[proposal.pairing_hash] === false)) &&
                !this.eventBus.tempLocalStorageHideColumn[proposal.pairing_hash]
            );
        },
    },
    computed: {
        userPositions() {
            let positions = []
            Object.values(this.user?.valid_assigments).forEach(assigment => {
                if (assigment.position && !positions.includes(assigment.position.name)) {
                    positions.push(assigment.position.name)
                }
            })
            return positions
        },
        userPositionsForTitle() {
            let tempString = ''
            this.userPositions.forEach(position => {
                tempString = tempString + position + ', '
            })

            return tempString
        }
    }
};
</script>


<style scoped>
</style>
