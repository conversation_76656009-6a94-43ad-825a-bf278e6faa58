<template>
    <div v-if="!logsLoaded" class="flex justify-center w-full">
        <span class="loading loading-spinner loading-lg"></span>
    </div>
    <template v-for="(historyError, index) in historyErrors" v-if="historyErrors">
        <ul class="bg-error border border-red-400 text-white rounded-md p-2">
            <li v-if="typeof index !== 'number'"> {{ index }}</li>
            <li v-for="error in historyError">{{ error }}</li>
        </ul>
    </template>
    <div v-if="logsLoaded && (!logs || logs.length === 0) && !historyErrors">
        <div class="timeline-end w-full">
            <div
                class="border-base-content/25 flex w-full items-center justify-between gap-2 rounded-lg border p-3">
                <p>{{ eventBus.translations?.general?.no_recent_changes }}</p>
            </div>
        </div>
    </div>
    <ul class="bg-base-200 border border-neutral/20 p-4 rounded-md">

        <template v-if="logsLoaded && logs.length > 0" v-for="(log, idx) in narrativeLogs" :key="log.id">
            <li class="relative flex gap-4 items-start mb-3">

                <!-- top vertical line -->
                <div
                    class="absolute top-[-12px] left-4.5 h-2 w-1 bg-gray-400/15 rounded-b-md" v-if="idx !== 0"></div>

                <!-- Avatar -->
                <div :class="[{'justify-center': logs.length -1 > idx}, {'justify-start': logs.length -1 === idx}]"
                     class="flex flex-col min-h-full items-center justify-center">
                    <!-- Avatar -->
                    <div class="avatar avatar-placeholder">
                        <div class="bg-warning/10 text-warning w-10 h-10 rounded-full flex items-center justify-center">
                            <span class="text-md uppercase">{{ log.user_initials }}</span>
                        </div>
                    </div>

                    <!-- bottom vertical line -->
                    <div class=" w-1 mt-1 h-full bg-gray-400/15 mx-auto rounded-t-md" v-if="logs.length -1 > idx"></div>
                </div>

                <!-- content -->
                <div
                    class="bg-base-100 border-neutral/15 w-full rounded-lg border p-3">
                    <div class="text-base-content">
                        <div class="flex justify-between w-full mb-2">
                            <span class="text-base-content font-bold">
                                {{ log.user_full_name }}
                                <span v-if="log.status_text" class="ms-2 text-sm font-normal text-secondary"> <i
                                    :class="eventBus.statuses[log.status].icon + ' me-1'"
                                    v-if="eventBus.statuses[log.status].icon"></i>{{
                                        log.status_text
                                    }}</span>
                                </span>
                            <div class="text-base-content/50 text-sm">{{ log.created_at_formatted_time }}</div>
                        </div>

                        <span v-if="log.note"
                              class="border border-neutral/15 rounded-md w-full px-2 py-1 flex gap-2 items-center bg-base-200 text-xs text-base-content mb-2">
                            <i class="hrms hrms-note text-xl"></i>{{ log.note }}</span>
                        <ul class="list-inside list-disc ms-2 text-xs text-base-content/60">
                            <li v-for="(change,name) in log.changes_narrative" class="mb-1">
                                {{ name }} - <span class="font-bold  text-base-content">{{ change }}</span>
                            </li>
                        </ul>
                    </div>

                </div>
            </li>
        </template>
    </ul>
</template>

<script>

export default {
    name: 'ProposalTimeline',
    props: {
        historyErrors: {
            type: Array,
            default: () => [],
        },
        logs: {
            type: Array,
            default: () => [],
        },
        logsLoaded: {
            type: Boolean,
            default: false
        },
        proposal: {
            type: Object,
            default: null
        },
        // Add other props like translations if needed from eventBus
    },
    inject: ['eventBus'], // If you need translations from eventBus
    mounted() {
        console.log(this.logs)
    },
    computed: {
        narrativeLogs() {
            if (!this.logs) return [];
            return this.logs.map(log => {
                // User info
                const user_full_name = log.user?.full_name || 'Systém';
                const user_initials = log.user?.last_name[0] + '' + log.user.first_name[0];
                const user_avatar = log.user?.avatar_url || null;
                // Status badge
                let statusBadgeClass = 'badge-soft badge-secondary';
                let statusText = '';
                let statusEnum = null;
                if (log.status) {
                    statusEnum = this.eventBus.statuses[log.status] ?? null;
                    if (statusEnum) {
                        statusText = statusEnum.translation;
                        switch (statusEnum.value) {
                            case 4:
                                statusBadgeClass = 'badge-outline badge-success';
                                break; // APPROVED
                            case 5:
                                statusBadgeClass = 'badge-outline badge-error';
                                break;   // REJECTED
                            case 2:
                                statusBadgeClass = 'badge-outline badge-warning';
                                break; // PENDING
                            case 3:
                                statusBadgeClass = 'badge-outline badge-info';
                                break;    // FORWARDED
                            default:
                                statusBadgeClass = 'badge-outline badge-neutral';
                        }
                    }
                }

                // Changes narrative (e.g., "status from Ongoing to Finished")
                let changes_narrative = {};
                if (log.changes && typeof log.changes === 'object' && Object.keys(log.changes).length > 0) {
                    Object.entries(log.changes).forEach(([field, change]) => {
                        let fieldName = this.eventBus.translations?.general[field] ?? field;
                        changes_narrative[fieldName] = this.formatChangeValue(field, change);

                    });
                }
                // changes_narrative = '<ul class="list-inside list-disc">' + changes_narrative + '</ul>'
                // Time formatting
                let created_at_formatted_time = '';
                if (log.created_at) {
                    try {
                        const date = new Date(log.created_at);
                        const today = new Date();
                        if (date.toDateString() === today.toDateString()) {
                            created_at_formatted_time = date.toLocaleTimeString('sk-SK', {
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        } else {
                            created_at_formatted_time = date.toLocaleDateString('sk-SK', {
                                day: '2-digit',
                                month: '2-digit',
                                year: 'numeric'
                            });
                        }
                    } catch (e) {
                        created_at_formatted_time = log.created_at;
                    }
                }

                return {
                    ...log,
                    user_full_name,
                    user_initials,
                    user_avatar,
                    status_text: statusText,
                    status_badge_class: statusBadgeClass,
                    changes_narrative,
                    created_at_formatted_time,
                };
            });
        },
    },
    methods: {
        formatChangeValue(field, value) {
            if (value === null || value === undefined) return 'N/A';

            return value;
        }
    }
};
</script>

<style scoped>
.timeline {
    --timeline-color: var(--color-primary, #2563eb);
}
</style> 