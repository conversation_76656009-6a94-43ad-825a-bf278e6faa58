<template>
    <transition :name="isRowInitialized ? 'scale-cell-x' : ''" mode="out-in" appear>
        <!-- Non-editable view -->
        <div
            :class="[{'bg-base-100/50': localProposal.in_progress_for_this_user && ![4,5].includes(localProposal.status)}, {'bg-base-100': !localProposal.in_progress_for_this_user || [4,5].includes(this.localProposal.status)}, {'border-b': !isLastRow}]"
            class="border-r p-4 w-[412px] h-32 flex flex-col items-center justify-center"
            v-if="localProposal?.approver_id != proposer?.id || localProposal?.status === 4 || localProposal?.status === 5 "
            :data-id="localProposal?.id ?? ''">
            <div class="text-gray-400 flex items-center">
                <i :class="['hrms text-xl me-1', actualStatusClasses]"></i>
                <span
                    :class="[{'text-gray-400': localProposal?.in_progress_for_this_user && localProposal?.status !== 5}, {'text-base-content': !(localProposal?.in_progress_for_this_user && localProposal?.status !== 5)}]">
                    {{ eventBus.formatNumber(localProposal?.base) }} <span class="me-1"
                    v-if="eventBus.allProposals[localProposal?.type]?.addition_enabled">+ {{
                        eventBus.formatNumber(localProposal?.addition)
                    }}</span><span
                    class="text-gray-400">{{ localProposal?.currency?.code }}</span>
                </span>
                <button v-if="localProposal?.is_claimable"
                        class="btn btn-secondary btn-xs btn-soft ms-2 font-medium border-slate-200"
                        @click="claimProposalBack"
                        :disabled="eventBus.allProposals[headerProposal?.type]?.disabled_by_validity"><i
                    class="hrms hrms-edit-text-outline"></i> {{ eventBus.translations?.general?.change }}
                </button>
            </div>
            <div class="mt-2 text-sm flex items-center gap-1">
                <span
                    :class="[
                      {'text-gray-400': localProposal?.in_progress_for_this_user && localProposal?.status !== 5},
                      {'font-bold': localProposal?.status === 4 || localProposal?.status === 5}
                    ]"
                    class="me-2">{{ eventBus.statuses[localProposal?.status]?.translation ?? '' }}</span>
                <i class="hrms hrms-vermont-ucko text-gray-400 me-1" v-if="localProposal?.status !== 4"></i>
                <span v-if="localProposal?.status !== 4"
                      :class="[{'text-gray-400': localProposal?.in_progress_for_this_user && localProposal?.status !== 5}]">

                    {{
                        localProposal?.approver?.full_name
                    }}</span>
                <!-- Approved: show history icon after status/name, as clickable icon only -->
                <template v-if="canShowHistory">
                  <span class="relative inline-flex items-center ms-2">
                    <i class="hrms hrms-history-outline text-lg text-gray-400 hover:text-gray-600 cursor-pointer"
                       @click="toggleHistoryPopover($event, 'nonEditable')"
                       ref="historyButtonRef"
                    ></i>
                    <span class="text-gray-400 hover:text-gray-600 cursor-pointer ms-1 select-none"
                          @click="toggleHistoryPopover($event, 'nonEditable')">
                      {{ eventBus.translations?.general?.history }}
                    </span>

                    <universal-popover :visible="showHistoryPopover && activePopover === 'nonEditable'"
                                       :id="'nonEditablePopoverEl'+localProposal?.id"
                                       ref="nonEditablePopoverEl"
                                       :style="{
                                            transform: `translate(0px, ${popoverTranslationY}px)`
                                        }"
                                       class="popover-custom-width absolute left-0 mt-1">
                        <div :id="'nonEditablePopoverEl'+localProposal?.id" class=""></div>

                    </universal-popover>
                  </span>
                </template>
            </div>
            <button v-if="localProposal?.is_usurpable"
                    class="btn btn-secondary bg-base-100 btn-xs btn-outline ms-2 mt-2 gap-1 border border-neutral/15"
                    @click="prepareUsurpModal"
                    :disabled="eventBus.allProposals[headerProposal?.type]?.disabled_by_validity"><i
                class="hrms hrms-asign-task"></i> {{ eventBus.translations?.general?.usurp }}
            </button>

            <div class="mt-2 inline-flex gap-1" v-if="eventBus.isFinalApprover && localProposal?.status === 4">

                <div class="flex items-center gap-1"
                     v-if="eventBus.allProposals[localProposal?.type]?.enable_paid_via_agreement">
                    <input type="checkbox" class="checkbox checkbox-xs checkbox-primary"
                           :id="'pay_out_on_aggreement_'+localProposal?.id"
                           v-model="localProposal.is_paid_via_agreement"

                           :disabled="!localProposal?.is_claimable || eventBus.allProposals[headerProposal?.type]?.disabled_by_validity"/>
                    <label
                        :class="{'cursor-not-allowed': !localProposal?.is_claimable}"
                        class="label-text text-xs" :for="'pay_out_on_aggreement_'+localProposal?.id">
                        {{ eventBus.translations.general?.pay_out_on_aggreement }} </label>
                </div>
                <div class="flex items-center gap-1"
                     v-if="eventBus.allProposals[localProposal?.type]?.enable_without_notify">
                    <input type="checkbox" class="checkbox checkbox-xs checkbox-primary"
                           :id="'without_informing_payroll_'+localProposal?.id"
                           @change="changePaymentStatus"
                           :checked="localProposal?.payment_status === 2"
                           :disabled="!localProposal?.is_claimable || eventBus.allProposals[headerProposal?.type]?.disabled_by_validity"/>
                    <label :class="{'cursor-not-allowed': false}"
                           class="label-text text-xs " :for="'without_informing_payroll_'+localProposal?.id">
                        {{ eventBus.translations.general?.without_informing_payroll }} </label>
                </div>
                <div class="flex items-center gap-1"
                     v-if="eventBus.allProposals[localProposal?.type]?.enable_paid_via_another_way">
                    <input type="checkbox" class="checkbox checkbox-xs checkbox-primary"
                           :id="'pay_via_another_way_'+localProposal?.id"
                           v-model="localProposal.is_paid_via_another_way"
                           :disabled="!localProposal?.is_claimable || eventBus.allProposals[headerProposal?.type]?.disabled_by_validity"/>
                    <label
                        :class="{'cursor-not-allowed': !localProposal?.is_claimable}"
                        class="label-text text-xs" :for="'pay_via_another_way_'+localProposal?.id">
                        {{ eventBus.translations?.general?.is_paid_via_another_way }} </label>
                </div>
            </div>

        </div>

        <!-- Editable view -->
        <div :class="[{'border-b': !isLastRow}]" class="relative border-r p-4 w-[412px] h-32 bg-base-100"
             v-else :data-id="localProposal?.id ?? ''">
            <div class="absolute inset-0 flex justify-center bg-base-100/50 opacity-70 z-50" v-if="updateStarted">
                <span class="loading loading-spinner loading-lg mt-10"></span>
            </div>

            <!-- Input when proposal type has addition enabled -->
            <div class="inline-flex mb-1 w-full"
                 v-if="eventBus.allProposals[localProposal?.type]?.addition_enabled && eventBus.allProposals[localProposal?.type]?.addition_enabled === true ">
                <number-input
                    :value="localProposal?.base" @input-value-changed="inputValue => updateInput(inputValue)"
                    :disabled="disabled"
                    :classes="['input rounded-e-none w-4/7 text-right', {'font-bold': isChanged['base']}, {'is-invalid' :Object.values(formErrors).length > 0 && formErrors.base }]">
                </number-input>
                <div class="relative">
                    <number-input
                        :value="localProposal?.addition"
                        @input-value-changed="inputValue => updateInput(inputValue, 'addition')"
                        :disabled="disabled"
                        :classes="['input pr-17 block disabled:bg-gray-200 rounded-s-none text-right', {'font-bold': isChanged['addition']}, {'is-invalid' :Object.values(formErrors).length > 0 && formErrors.addition }]">
                    </number-input>
                    <div
                        class="absolute border-l-0 border-0 rounded-md inset-y-0.5 right-2 flex items-center text-gray-500 text-sm"
                        @mouseleave="showInputProposalButtons=false">
                        <select v-model="localProposal.currency_id"
                                :class="{'is-invalid !border-1' :Object.values(formErrors).length > 0 && formErrors.currency_id }"
                                class="select select-sm remove-arrow px-1 border-0 disabled:bg-gray-200 hover:bg-primary/10 hover:text-primary"
                                :disabled="disabled"
                                @change="saveProposal">
                            <option v-for="currency in currencies" :key="currency.id" :value="currency.id">{{ currency.code }}</option>
                        </select>
                        <button
                            class="btn hover:btn-primary px-1 btn-xs btn-outline h-full border-0" :disabled="disabled"
                            @click="changeProposalStatus">
                            <i :class="disabled ? 'hrms hrms-archive-outline' : 'hrms hrms-edit-text-outline'"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Input when proposal type has addition disabled -->
            <div class="relative mb-1"
                 v-else>
                <number-input
                    :value="localProposal?.base" @input-value-changed="inputValue => updateInput(inputValue)"
                    :disabled="disabled"
                    :classes="['input pr-17 block disabled:bg-gray-200 text-right', {'font-bold': isChanged['base']}, {'is-invalid' :Object.values(formErrors).length > 0 && formErrors.base }]">
                </number-input>
                <div
                    class="absolute border-l-0 border-0 rounded-md inset-y-0.5 right-2 flex items-center text-gray-500 text-sm"
                    @mouseleave="showInputProposalButtons=false">
                    <select v-model="localProposal.currency_id"
                            :class="{'is-invalid border' :Object.values(formErrors).length > 0 && formErrors.currency_id }"
                            class="select select-sm remove-arrow px-1 border-0 disabled:bg-gray-200 hover:bg-primary/10 hover:text-primary"
                            :disabled="disabled"
                            @change="saveProposal">
                        <option v-for="currency in currencies" :key="currency.id" :value="currency.id">{{ currency.code }}</option>
                    </select>
                    <button
                        class="btn hover:btn-primary px-1 btn-xs btn-outline h-full border-0" :disabled="disabled"
                        @click="changeProposalStatus">
                        <i :class="disabled ? 'hrms hrms-archive-outline' : 'hrms hrms-edit-text-outline'"></i>
                    </button>
                </div>
            </div>
            <div class="flex gap-1 relative">
                <!-- Error badge that overlays history section -->
                <div class="absolute left-0 right-0 top-0 z-10" v-if="Object.values(formErrors).length > 0">
                    <div class="badge badge-error badge-soft p-2 text-md w-full overflow-visible  h-full font-bold"
                        :title="Object.values(formErrors).join(', ')">
                        <div v-for="(formError, errorIndex) in formErrors" :key="errorIndex" class="text-sm break-words">
                            <div v-for="(error, subErrorIndex) in formError" :key="subErrorIndex" class="line-clamp-3">{{ error }}</div>
                        </div>
                    </div>
                </div>
                
                <!-- Timeline icons -->
                <div class="flex flex-col items-center">
                    <i class="hrms hrms-note-last text-gray-400 text-xl"></i>
                    <div class="w-px h-6 bg-gray-400 text-gray-400"></div>
                    <i class="hrms hrms-note-first text-gray-400 text-xl"></i>
                </div>
                <!-- Main content (notes, user, etc.) -->
                <div class="flex flex-col justify-between">
                    <div v-if="firstNote" class="text-base-content text-sm line-clamp-1" :title="firstNote">
                        {{ firstNote }}
                    </div>
                    <div v-if="localProposal?.logs && localProposal?.logs[0]"
                         class="text-gray-400 text-sm mb-2 line-clamp-1 flex gap-1">
                        {{ localProposal?.logs[0].user?.full_name }}
                    </div>
                    <div class="text-gray-400 flex items-end text-sm">
                        <span class="line-clamp-1">{{ localProposal?.reason }}</span>
                    </div>
                </div>
                <!-- History Button -->
                <div class="ml-auto flex flex-col items-start justify-end relative">
                    <button
                        v-if="canShowHistory"
                        @click="toggleHistoryPopover($event, 'editable')"
                        ref="historyButtonRefEditable"
                        class="mt-auto btn btn-outline btn-xs btn-secondary self-start border-neutral/20"
                        :title="eventBus.translations?.general?.history || 'História'"
                    >
                        <i class="hrms hrms-history-outline text-lg text-gray-400 hover:text-gray-600"></i>
                    </button>

                    <universal-popover
                        :visible="showHistoryPopover && activePopover === 'editable'"
                        :style="{
                            transform: `translate(0px, -${popoverTranslationY}px)`
                        }"
                        ref="editablePopoverEl"
                        class="popover-custom-width min-w-[300px] absolute right-0 top-full mt-1"
                    >
                        <div :id="'editablePopoverEl'+localProposal?.id"></div>
                    </universal-popover>
                </div>
            </div>
        </div>

    </transition>
    <teleport :to="teleportHistoryTo" v-if="teleportHistoryTo">
        <div class="p-3 bg-base-100 border rounded-lg shadow-xl" @click.stop ref="popoverBody">
            <div class="mb-3 pb-2 border-b">
                <h3 class="font-semibold text-sm text-gray-700">
                    {{ eventBus.translations?.general?.reason }}:
                </h3>
                <span class="line-clamp-3 text-xs"
                      :title="localProposal?.reason">{{ localProposal?.reason }}</span>
            </div>
            <div class="max-h-60 overflow-y-auto space-y-2 pr-1 text-xs">
                <div v-if="Object.values(popoverSummaryLogs).length === 0" class="text-gray-500 text-center py-3">
                    {{
                        eventBus.translations?.general?.no_recent_changes
                    }}
                </div>
                <div v-for="(log, index) in Object.values(popoverSummaryLogs)" :key="'popover-log-' + log.id"
                     class="flex gap-2 pb-1.5">
                    <div class="flex flex-col items-center relative w-6">
                        <i :class="['hrms text-gray-400 text-xl', {'hrms-changelog-solo': index !== 0 && index !== Object.values(popoverSummaryLogs).length -1}, {'hrms-changelog-last-popover': index === 0}, {'hrms-changelog-first-popover': index === Object.values(popoverSummaryLogs).length -1}]"></i>
                        <div v-if="index !== Object.values(popoverSummaryLogs).length-1"
                             class="absolute top-3.5 left-[45%]  bg-gray-400 bottom-0"
                             :style="{
                                                                                    width: '2px',height: 'calc(100% + 0.3rem)' }"></div>
                        <!--                                                                                <div-->
                        <!--                                            v-if="index !== popoverSummaryLogs.length-1"-->
                        <!--                                            class="w-0.5 h-6 bg-gray-400 text-gray-400"></div>-->

                    </div>
                    <div class="flex-1">
                        <div class="flex justify-between items-baseline">
                            <p class="text-gray-700"><span class="font-medium">{{
                                    log.user_full_name
                                }}</span>
                                <span v-if="log.status_text"
                                      :class="['badge badge-xs normal-case ms-1', log.status_badge_class]">{{
                                        log.status_text
                                    }}</span>
                            </p>
                            <span class="text-xxs text-gray-400 whitespace-nowrap">{{
                                    log.created_at_formatted_time
                                }}</span>
                        </div>
                        <p v-if="log.note"
                           class="text-gray-500 border border-neutral/20 p-1 rounded mt-0.5 line-clamp-2 italic text-xxs"
                           :title="log.note">
                            <i class="hrms hrms-chat-quote-line me-0.5 text-gray-400"></i>{{ log.note }}
                        </p>
                        <p v-if="log.changes_summary" class="text-xxs text-gray-400 mt-0.5 line-clamp-1"
                           :title="log.changes_summary">
                            {{ eventBus.translations?.general?.changes }}:
                            {{ log.changes_summary }}</p>
                    </div>
                </div>
            </div>
            <button @click="openFullHistoryModal" class="btn btn-primary btn-block btn-xs mt-2.5">
                <i class="hrms hrms-timeline-view-outline me-1"></i>{{
                    eventBus.translations?.general?.history_full || 'Zobraziť celú históriu'
                }}
            </button>
        </div>
    </teleport>

</template>

<script>

/*  Space for import    */

import NumberInput from "../inputs/NumberInput.vue";
import UniversalPopover from '../universalPopover.vue';
import ProposalTimeline from './Timeline.vue';

export default {
    name: "Cell",
    components: {NumberInput, UniversalPopover, ProposalTimeline},
    emits: ['request-show-full-history'],
    inject: ['eventBus'],
    props: {
        createRoute: String,
        currencies: Object,
        getSpecificProposalRoute: String,
        headerProposal: Object,
        isLastRow: Boolean,
        isRowInitialized: Boolean,
        proposal: Object,
        proposer: Object,
        selectedCurrency: {
            type: Number,
            default: null
        },
        user: Object,

    },
    data() {
        return {
            activePopover: null,
            componentKey: getCurrentInstance().vnode.key,
            debounceTimer: null,
            formErrors: [],
            historyPopoverTarget: null,
            isChanged: {addition: false, base: false},
            localProposal: this.proposal ?? {
                type: this.headerProposal?.type,
                subtype: this.headerProposal?.subtype,
                base: null,
                addition: null,
                currency_id: this.selectedCurrency ?? this.currencies[0],
                reason: null,
                validity: this.headerProposal?.validity,
                approver_id: this.proposer?.id,
                department_id: this.headerProposal?.department_id,
                centre_id: this.headerProposal?.centre_id,
                proposer_id: null,
                users: [this.user?.id],
                status: 0,
                is_paid_via_agreement: false,
                pairing_hash: this.headerProposal?.pairing_hash,
            },
            popoverTranslationY: 0,
            showHistoryPopover: false,
            showInputProposalButtons: false,
            teleportHistoryTo: '',
            updateStarted: false,
            userId: this.user?.id,
        }
    },
    watch: {
        'localProposal.status'(newValue, oldValue) {
            setTimeout(() => {
                // this.eventBus.uniqueProposals = this.eventBus.getUniqueProposals()
                this.eventBus.recalculateClosableProposalHeaders(this.localProposal?.type, this.localProposal?.subtype)
            }, 1000)
        },

        localProposal: {
            handler(newValue) {
                if (newValue.approver_id === this.proposer?.id && newValue.approver_id === newValue.proposer_id && newValue.status === 5) {
                    setTimeout(() => {
                        this.localProposal = {
                            type: this.headerProposal?.type,
                            subtype: this.headerProposal?.subtype,
                            base: null,
                            addition: null,
                            currency_id: this.selectedCurrency ?? this.currencies[0],
                            reason: null,
                            validity: this.headerProposal?.validity,
                            approver_id: this.proposer?.id,
                            department_id: this.headerProposal?.department_id,
                            centre_id: this.headerProposal?.centre_id,
                            proposer_id: null,
                            users: [this.user?.id],
                            status: 0,
                        }
                    }, 2000)

                }
                this.updateUserProposal()
            },
            deep: true
        },
        "localProposal.is_paid_via_agreement"(newValue, oldValue) {
            if (newValue != oldValue && newValue != null) {
                this.localProposal.update_type = 'paid_via_agreement'
                this.saveProposal()
                this.localProposal.update_type = undefined

            }
        },
        "localProposal.is_paid_via_another_way"(newValue, oldValue) {
            if (newValue != oldValue && newValue != null && oldValue != undefined) {

                this.localProposal.update_type = 'is_paid_via_other_way'
                this.saveProposal()

                setTimeout(() => {
                    this.reloadProposal()
                }, 1000)
                this.localProposal.update_type = undefined

            }
        }
    },
    beforeMount() {
        document.addEventListener('click', this.handleClickOutsidePopover);
    },
    mounted() {
        // if (!this.proposal) {
        //     this.reloadProposal()
        // }

        this.checkCheckboxInputValues()
    },
    beforeUnmount() {
        document.removeEventListener('click', this.handleClickOutsidePopover);
    },
    methods: {
        reloadProposal(showSpinner = true, fromOpenedArticleDetailModal = false) {
            this.formErrors = []
            this.updateStarted = showSpinner
            let attributes = this.localProposal.id ? {proposal_id: this.localProposal.id} : {
                user_id: this.userId,
                approver_id: this.headerProposal?.approver_id,
                type: this.headerProposal?.type,
                subtype: this.headerProposal?.subtype,
                validity: this.headerProposal?.validity,
                department_id: this.headerProposal?.department_id,
                centre_id: this.headerProposal?.centre_id,
                pairing_hash: this.headerProposal?.pairing_hash,
            }
            axios.post(this.getSpecificProposalRoute, {
                ...attributes
            }).then(response => {
                if (!this.localProposal.id) {
                    this.eventBus.handleNewProposalsFoundEvent(response.data[0])
                    if (fromOpenedArticleDetailModal) {
                        this.eventBus.proposalDetailModal.cellData = response.data[0]
                    }
                }

                this.localProposal = response.data[0]
                this.updateStarted = false
                this.eventBus.recalculateBudgets(this.localProposal.subtype)
                this.checkCheckboxInputValues()
            })
                .catch(errorResponse => {
                    console.log()
                    if (errorResponse.response.status === 404) {
                        this.eventBus.getDataForForm()
                    }
                    this.updateStarted = false
                    // console.log(errorResponse.response.data)
                })
        },
        updateInput(value, name = 'base') {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                this.formErrors = []
                this.localProposal[name] = Number(value)
                this.isChanged[name] = true;
                this.saveProposal()
            }, 1000);

        },
        saveProposal(isSavedFromTableParent = false) {
            this.formErrors = []
            const route = this.localProposal.id ? this.createRoute + '/' + this.localProposal.id : this.createRoute
            if (this.localProposal?.user) {
                this.localProposal.user = undefined
            }
            let request = this.localProposal.id ? axios.patch(route, {
                ...this.localProposal
            }) : axios.post(route, {...this.localProposal})

            request.then(response => {
                if (!this.localProposal.id) {
                    this.reloadProposal( true, isSavedFromTableParent)
                    this.updateStarted = false
                    return
                }
                this.eventBus.recalculateBudgets(this.localProposal?.subtype)
                this.checkCheckboxInputValues()
                this.updateStarted = false
                this.updateProposalLogs()
            }).catch(errorResponse => {
                this.updateStarted = false
                if (errorResponse.response?.data?.errors) {
                    this.formErrors = errorResponse.response.data.errors
                } else if (errorResponse.response?.data?.error) {
                    this.formErrors.push([errorResponse.response.data.error])
                } else if (errorResponse.response?.data?.message) {
                    this.formErrors.push([errorResponse.response.data.message])
                } else {
                    this.formErrors.push([errorResponse])
                }
            }).finally(() => {
                if (isSavedFromTableParent) {
                    this.eventBus.proposalDetailModal.errors = this.formErrors
                }
            })

        },
        handleUpdateRequest(data, claimingBack = false, usurping = false) {
            this.formErrors = []
            let updateProposal = false
            if (!this.localProposal?.id || (this.disabled && !usurping)) {
                return
            }
            switch (true) {
                case data.decisionData.user_id !== undefined:
                    updateProposal = this.localProposal?.user_id == data.decisionData.user_id;
                    break;
                case data.decisionData.proposal_id !== undefined:
                    updateProposal = this.localProposal?.id == data.decisionData.proposal_id;
                    break;
                case data.decisionData.proposal_type !== undefined && data.decisionData.proposal_subtype !== undefined && data.decisionData.proposal_hash !== undefined:
                    updateProposal = this.localProposal?.type == data.decisionData.proposal_type && this.localProposal?.subtype == data.decisionData.proposal_subtype && this.localProposal?.pairing_hash == data.decisionData.proposal_hash;
                    break;
                case data.decisionData.all !== undefined:
                    updateProposal = data.decisionData.all;
                    break;
                case data.decisionData.component_key !== undefined:
                    updateProposal = data.decisionData.component_key == this.componentKey;
                    break;
                default:
                    updateProposal = false;
            }
            updateProposal = updateProposal && (![4, 5].includes(this.localProposal?.status) || claimingBack || usurping) // skip if proposal is approved or rejected

            if (updateProposal === false) {
                return;
            }
            this.updateStarted = true
            Object.entries(data.updateData).forEach(([key, value]) => {
                this.localProposal[key] = value
            })

            if (this.localProposal?.user) {
                this.localProposal.user = undefined
            }
            axios.patch(this.createRoute + '/' + this.localProposal?.id, {...this.localProposal}).then(response => {
                this.localProposal = response.data.data
                this.updateStarted = false

                this.eventBus.recalculateBudgets(this.localProposal.subtype)
                this.checkCheckboxInputValues()
                this.updateProposalLogs()
            }).catch(response => {
                this.reloadProposal()
                if (response.response?.data?.errors) {
                    this.formErrors = response.response.data.errors
                }
                if (response.response?.data?.error) {
                    this.formErrors.push([response.response.data.error])
                }
                if (response.response?.data?.message) {
                    this.formErrors.push([response.response.data.message])
                }

                this.updateStarted = false
            })
        },
        updateProposalLogs() {
            axios.post(this.eventBus.historyLogsRoute + '/' + this.localProposal?.id + "/3").then(response => {
                this.localProposal.logs = response.data.logs;
            })
        },
        changeProposalStatus() {
            const identifier = this.localProposal?.id ? {proposal_id: this.localProposal?.id} : {component_key: this.componentKey}
            this.formErrors = []
            const parentAllProposals = this.eventBus.allProposals
            const proposalName = parentAllProposals[this.localProposal?.type]?.translation
            const proposalSubName = parentAllProposals[this.localProposal?.type]?.subcases && parentAllProposals[this.localProposal?.type]?.subcases[this.localProposal?.subtype] ? parentAllProposals[this.localProposal?.type]?.subcases[this.localProposal?.subtype]?.translation : null
            const fullProposalName = proposalSubName ? `${proposalName} (${proposalSubName})` : proposalName
            this.localProposal.user = this.user
            this.eventBus.proposalDetailModal.cellData = this.localProposal
            this.eventBus.proposalDetailModal.proposalName = fullProposalName
            this.eventBus.proposalDetailModal.cellData.first_note = this.firstNote
            this.eventBus.proposalDetailModal.cellData.proposer = this.proposer
            if (!this.localProposal?.id) {
                this.eventBus.proposalDetailModal.cellData.component_key = this.componentKey
            }
            this.eventBus.openProposalDetailModal()

            this.eventBus.openProposalModalWithRightData('cell', `${this.user?.full_name}: ${fullProposalName}`, identifier, true)
        },
        claimProposalBack() {
            this.localProposal.approver_id = this.proposer?.id
            this.handleUpdateRequest({
                decisionData: {proposal_id: this.localProposal?.id},
                updateData: {update_type: 'withdraw'}
            }, true)
        },
        prepareUsurpModal() {
            const parentAllProposals = this.eventBus.allProposals
            this.eventBus.usurpModal.proposal_id = this.localProposal?.id
            this.eventBus.usurpModal.type = parentAllProposals[this.localProposal?.type].translation
            this.eventBus.usurpModal.subtype = parentAllProposals[this.localProposal?.type]?.subcases && parentAllProposals[this.localProposal?.type]?.subcases[this.localProposal?.subtype] ? parentAllProposals[this.localProposal?.type]?.subcases[this.localProposal?.subtype]?.translation : null
            this.eventBus.usurpModal.user_full_name = this.user?.full_name
            this.eventBus.openUsurpModal()
        },
        changePaymentStatus() {
            this.localProposal.payment_status = this.localProposal?.payment_status == 2 ? 1 : 2;
            this.localProposal.update_type = 'change_payment_status'
            this.saveProposal()
            this.localProposal.update_type = undefined
        },
        updateUserProposal() {
            let matchedProposalKey = null

            Object.entries(this.eventBus.userProposals).forEach(userProposal => {
                if (this.localProposal?.id && userProposal[1].id === this.localProposal?.id) {
                    matchedProposalKey = userProposal[0]
                }
            })
            if (matchedProposalKey !== null) {
                this.eventBus.userProposals[matchedProposalKey] = this.localProposal
            }
        },
        updateProposalFromParent(proposal) {
            const oldIsPaidViaAnotherWay = this.localProposal.is_paid_via_another_way
            if (proposal.id && proposal.id == this.localProposal.id) {
                this.localProposal = proposal
                // if (oldIsPaidViaAnotherWay == proposal.is_paid_via_another_way) {
                //     return
                // }

                this.saveProposal(true)
            }
            if (!proposal.id && proposal.component_key && proposal.component_key == this.componentKey) {
                this.localProposal = proposal
                // if (oldIsPaidViaAnotherWay == proposal.is_paid_via_another_way) {
                //     return
                // }
                this.saveProposal(true)
            }
        },
        checkCheckboxInputValues() {
            if (typeof this.localProposal?.is_paid_via_agreement === 'number' && this.localProposal?.is_paid_via_agreement === 1) {
                this.localProposal.is_paid_via_agreement = true
            }
            if (typeof this.localProposal?.is_paid_via_agreement === 'number' && this.localProposal?.is_paid_via_agreement === 0) {
                this.localProposal.is_paid_via_agreement = false
            }
            if (typeof this.localProposal?.is_paid_via_another_way === 'number' && this.localProposal?.is_paid_via_another_way === 1) {
                this.localProposal.is_paid_via_another_way = true
            }
            if (typeof this.localProposal?.is_paid_via_another_way === 'number' && this.localProposal?.is_paid_via_another_way === 0) {
                this.localProposal.is_paid_via_another_way = false
            }
        },
        toggleHistoryPopover(event, popoverName) {
            event.stopPropagation();
            // console.log(`[Popover Toggle] Before - Active: ${this.activePopover}, Visible: ${this.showHistoryPopover}, Target Name: ${popoverName}`);
            if (this.activePopover === popoverName && this.showHistoryPopover) {
                this.teleportHistoryTo = ''

                // Closing the popover
                this.popoverTranslationY = 0
                this.showHistoryPopover = false;
                this.activePopover = null;
                this.historyPopoverTarget = null;
                // console.log(`[Popover Toggle] After Close - Active: ${this.activePopover}, Visible: ${this.showHistoryPopover}`);
            } else {
                // Opening or switching the popover
                this.historyPopoverTarget = event.currentTarget;
                this.activePopover = popoverName;
                this.showHistoryPopover = true;
                this.$nextTick(() => {
                    this.teleportHistoryTo = popoverName === 'nonEditable' ? '#nonEditablePopoverEl' + this.localProposal?.id : '#editablePopoverEl' + this.localProposal?.id
                    this.updatePopoverPosition()
                })

                // console.log(`[Popover Toggle] After Open/Switch - Active: ${this.activePopover}, Visible: ${this.showHistoryPopover}`);
            }
        },
        handleClickOutsidePopover(event) {
            // Only run the logic if a popover is supposed to be visible
            if (this.showHistoryPopover && this.historyPopoverTarget && this.activePopover) {
                // Get the reference to the currently active popover component
                let popoverElRef = this.activePopover === 'nonEditable' ? this.$refs.nonEditablePopoverEl : this.$refs.editablePopoverEl;

                // Check if the popover component reference AND its root DOM element ($el) exist
                if (popoverElRef && popoverElRef.$el) {
                    // Check if the click occurred OUTSIDE the popover's root element AND OUTSIDE the trigger button
                    if (!popoverElRef.$el.contains(event.target) &&
                        this.historyPopoverTarget && !this.historyPopoverTarget.contains(event.target)) {
                        // If click is outside both, hide the popover
                        this.teleportHistoryTo = ''
                        this.popoverTranslationY = 0
                        this.showHistoryPopover = false;
                        this.activePopover = null;
                    }
                } else {
                    // If the popover ref doesn't exist (likely hidden by v-if),
                    // still check if the click was outside the trigger button.
                    // This handles closing if the click was elsewhere on the page while the popover was hidden.
                    if (this.historyPopoverTarget && !this.historyPopoverTarget.contains(event.target)) {
                        this.showHistoryPopover = false;
                        this.activePopover = null;
                    }
                }
            }
        },
        openFullHistoryModal() {
            this.teleportHistoryTo = ''
            this.popoverTranslationY = 0
            this.showHistoryPopover = false;
            this.activePopover = null; // Ensure popover state is fully reset
            if (this.localProposal && this.localProposal?.logs) {
                const proposalName = this.eventBus.allProposals[this.localProposal.type]?.translation || 'Návrh';
                const userName = this.localProposal.user?.full_name || this.user?.full_name || '';
                const title = `${this.eventBus.translations?.general?.history}: ${userName} - ${proposalName}`.trim();

                this.$emit('request-show-full-history', {
                    logs: this.localProposal?.logs,
                    proposal: this.localProposal,
                    title: title
                });
            }
        },
        formatTimeOrDateForPopover(dateString) {
            if (!dateString) return '';
            try {
                const date = new Date(dateString);
                const today = new Date();
                if (date.toDateString() === today.toDateString()) {
                    return date.toLocaleTimeString('sk-SK', {hour: '2-digit', minute: '2-digit'});
                }
                return date.toLocaleDateString('sk-SK', {day: '2-digit', month: '2-digit', year: 'numeric'});
            } catch (e) {
                return dateString; // fallback
            }
        },
        getChangesSummaryForPopover(changes) {
            if (!changes || typeof changes !== 'object' || !changes?.changes) return '';
            let fields = [];
            if (Array.isArray(changes)) {
                fields = changes.changes.map(c => {
                    return this.eventBus.translations?.general[c.field] ?? c.field
                });
            } else {
                fields = Object.keys(changes).map(field => {
                    return this.eventBus.translations?.general[field] ?? field
                });
            }
            return fields.join(', ');
        },
        updatePopoverPosition() {
            this.$nextTick(() => {
                const section = this.eventBus.$refs.proposals;
                const popoverBody = this.$refs.popoverBody;

                if (!section) return;

                const rectSection = section.getBoundingClientRect();
                const popover = popoverBody.getBoundingClientRect();


                const isOutOfBottom = popover.bottom - rectSection.bottom
                const edgeOffset = 20
                if (isOutOfBottom > 0) {
                    this.popoverTranslationY = isOutOfBottom + edgeOffset
                } else if (popover.top < 0) {
                    this.popoverTranslationY = -popover.top + (edgeOffset / 2)
                } else {
                    this.popoverTranslationY = 0
                }

            })

        },

    },
    computed: {
        firstNote() {
            return this.localProposal?.logs && this.localProposal?.logs[0] ? this.localProposal?.logs[0].note : ''
        },
        disabled() {
            return this.localProposal?.approver_id != this.proposer?.id || this.eventBus.allProposals[this.headerProposal?.type]?.disabled_by_validity
        },
        actualStatusClasses() {
            let classes;

            if (this.localProposal?.in_progress_for_this_user && ![5, 4].includes(this.localProposal?.status)) {
                return 'hrms-edit-text-outline text-gray-400'
            }
            switch (this.localProposal?.status) {
                case 4:
                    classes = 'hrms-acepted-circle text-green-600'
                    break;
                case 5:
                    classes = 'hrms-not-acepted-circle text-red-600'
                    break;
                case 2:
                    classes = this.localProposal?.is_claimable ? 'hrms-waiting5-aproval-circle-outline text-gray-400' : 'hrms-waiting4-aproval-circle text-gray-400'
                    break;
                default:
                    classes = 'hrms-waiting4-aproval-circle text-gray-400';
            }
            return classes;
        },
        canShowHistory() {
            return this.localProposal && this.localProposal?.logs && this.localProposal?.logs?.length > 0;
        },
        popoverSummaryLogs() {
            if (!this.canShowHistory) return [];
            return this.localProposal.logs.map(log => {
                const statusValue = log.status;
                const statusEnum = this.eventBus.statuses[statusValue];
                let statusBadgeClass = 'badge-soft badge-secondary';
                if (statusEnum) {
                    switch (statusValue) {
                        case 4:
                            statusBadgeClass = 'badge-soft badge-success';
                            break; // APPROVED
                        case 5:
                            statusBadgeClass = 'badge-soft badge-error';
                            break;   // REJECTED
                        case 2:
                            statusBadgeClass = 'badge-soft badge-warning';
                            break; // PENDING
                        case 3:
                            statusBadgeClass = 'badge-soft badge-info';
                            break;    // FORWARDED
                        default:
                            statusBadgeClass = 'badge-soft badge-neutral';
                    }
                }

                return {
                    id: log.id,
                    user_full_name: log.user?.full_name || 'Systém',
                    user_initials: log.user?.full_name ? log.user.full_name.match(/\\b\\w/g)?.join('')?.substring(0, 2)?.toUpperCase() : 'SY',
                    status_text: statusEnum?.translation || '',
                    status_badge_class: statusBadgeClass,
                    created_at_formatted_time: this.formatTimeOrDateForPopover(log.created_at),
                    note: log.note,
                    changes_summary: this.getChangesSummaryForPopover(log.changes),
                };
            });
        }
    }
};
</script>


<style scoped>
.left-swipe-enter-from {
    opacity: 0;
    transform: translateX(30px);
}

.left-swipe-leave-to {
    opacity: 0;
    transform: translateX(-30px);
}

.left-swipe-enter-active, .left-swipe-leave-active {
    transition: all 0.1s ease;
}

.popover-custom-width {
    width: 380px; /* Or your desired width for the popover */
}

.text-xxs {
    font-size: 0.65rem; /* Adjust if needed */
    line-height: 0.85rem;
}

.scale-cell-x-enter-from,
.scale-cell-x-leave-to {
    transform: scaleX(0.1);
    opacity: 0;
}

.scale-cell-x-enter-to,
.scale-cell-x-leave-from {
    transform: scaleX(1);
    opacity: 1;
}

.scale-cell-x-enter-active {
    transition: all 0.5s ease;
    transform-origin: left;
}

.scale-cell-x-leave-active {
    transition: all 0.5s ease;
    transform-origin: left;
}
</style>
