
<template>
    <div>
        <!-- Open button -->
        <button v-if="showModalButton" :id="name+'-modal-button'" @click="open" :class="buttonClasses" @click.prevent>
            <slot name="button">Open Modal</slot>
        </button>

        <!-- Modal -->
        <Teleport to="body">
            <transition name="fade">
                <div
                    :id="name+'modal'"
                    v-if="isOpen"
                    :class="['fixed inset-0 flex bg-black/50 z-50', modalPositionClasses]"
                    @click.self="close"
                >
                    <div
                        :id="name+'-modal-window'"
                        class="bg-base-100 p-6 rounded-lg shadow-lg flex flex-col"
                        :class="['justify-start', modalStyles]"
                    >
                        <!-- Close button always right top -->
                        <div :id="name+'-modal-header'" class="relative flex justify-between items-center w-full mb-2">
                            <slot name="header">
                                <span></span>
                            </slot>

                            <button type="button"
                                    class="btn btn-text btn-circle btn-sm absolute end-[-10px] top-[-10px] bg-primary"
                                    @click="close">
                                <i class="hrms hrms-close text-white"></i>
                            </button>
                        </div>

                        <!-- Modal body - using flex-1 to take remaining space -->
                        <div :id="name+'-modal-body'" class="w-full overflow-auto flex-1">
                            <slot name="body"></slot>
                        </div>
                    </div>
                </div>
            </transition>
        </Teleport>
    </div>
</template>

<script>
export default {
    name: "UniversalModal",
    components: {},
    props: {
        name: {
            type: String,
            default: ''
        },
        buttonClasses: {
            type: String,
            default: "button-blue-outline",
        },
        modalWindowClasses: {
            type: String,
            default: "",
        },
        modalPositionClasses: {
            type: String,
            default: "items-center justify-center"
        },
        showModalButton: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            isOpen: false,
        }
    },
    watch: {},
    beforeMount() {
    },
    mounted() {
    },
    beforeUnmount() {
    },
    methods: {
        open() {
            this.isOpen = true;
            document.addEventListener("keydown", this.handleEscapeClose);
        },
        close() {
            this.isOpen = false;
            document.removeEventListener("keydown", this.handleEscapeClose);
        },
        handleEscapeClose(event) {
            // Close modal by any aria-label="close" button
            if (this.isOpen && event.key === 'Escape') {
                this.close();
            }
        },
    },
    computed: {
        modalStyles() {
            return this.modalWindowClasses || "w-1/2 max-h-2/3";
        },
    }
};
</script>

<style scoped>
/* Animation */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
}
</style>