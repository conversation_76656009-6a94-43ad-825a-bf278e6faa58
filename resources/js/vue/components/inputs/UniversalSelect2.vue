<template>
    <div class="relative w-full cursor-pointer" ref="selectContainer">
        <input
            :id="name"
            ref="visible_select"
            readonly
            @click.prevent="show()"
            @keydown="keyDown"
            @keypress.enter.prevent="show()"
            :class="['input cursor-pointer border border-neutral/25', {'custom-outline': showDropdown || focus,'border-neutral/40': disabled}, {'rounded-md': this.disableLogic}, {'rounded-s-none rounded-e-md': !this.disableLogic}, this.customClasses]"
            type="text"
            :value="this.searchProperty ? selectedValue[searchProperty] : selectedValue === '' ? this.localTranslations.all : selectedValue"
        >
        <svg class="absolute right-3 top-2 opacity-75 size-5" viewBox="0 0 20 20" fill="currentColor"
             @click.prevent="show()"
             aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd"
                  d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                  clip-rule="evenodd"/>
        </svg>

        <input
            ref="select"
            type="hidden"
            readonly
            :value="this.searchProperty ? selectedValue[searchProperty] : selectedValue === ''"
        >
        <div v-if="showDropdown"
             ref="dropdown"
             :class="[
        'z-50 w-full border border-neutral/25 bg-base-100 shadow-sm absolute rounded-md',
        isDropdownAbove ? 'bottom-full mb-1 rounded-t-md' : 'top-full mt-1 rounded-b-md'
      ]">
            <div class="m-2" v-if="!isDropdownAbove">
                <input ref="search" @keypress.enter.prevent="show()" @keydown="keyDown"
                       @keydown.tab="showDropdown=false" @input="filter()" v-model="search" id="search"
                       type="text"
                       placeholder=""
                       class="input"
                       autocomplete="off">
            </div>
            <div class="max-height overflow-auto" :key="refreshKey">
                <div
                    v-if="this.searchProperty"
                    :id="item.id"
                    :class="{'text-white bg-primary': item[searchProperty] === this.selectedValue[searchProperty] }"
                    @click="select(item)"
                    @keydown.enter.prevent="showDropdown=false"
                    class="text-sm px-2 py-1 border-b border-gray-50 w-full hover:text-white hover:bg-primary"
                    v-for="item in items"
                    :key="item[searchProperty]"
                >
                    {{ item[searchProperty] }}
                </div>
                <div
                    v-else
                    :id="item.id"
                    :class="{'selected-custom': item === this.selectedValue}"
                    @click="select(item)"
                    @keydown.enter.prevent="showDropdown=false"
                    class="text-sm px-2 w-full hover:text-white hover:bg-primary"
                    v-for="item in items"
                    :key="item"
                >
                    {{ item === "" ? this.localTranslations.all : item }}
                </div>
            </div>
            <div class="m-2" v-if="isDropdownAbove">
                <input ref="search" @keypress.enter.prevent="show()" @keydown="keyDown"
                       @keydown.tab="showDropdown=false" @input="filter()" v-model="search" id="search"
                       type="text"
                       placeholder=""
                       class="input"
                       autocomplete="off">
            </div>
        </div>
        <input type="text" :name="name" :value="this.inputProperty ? selectedValue[inputProperty] : selectedValue"
               class="hidden">
    </div>
    <div @click="showDropdown=false" v-if="showDropdown" class="absolute top-0 start-0 w-full h-full z-40">

    </div>


</template>

<script>
export default {
    name: "UniversalSelect2",
    emits: ["newValue", "update:modelValue"],
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        options: {
            default: []
        },
        selected: {
            default: null
        },
        searchProperty: {
            default: null
        },
        inputProperty: {
            default: null
        },
        name: {
            default: ""
        },
        allOption: {
            type: Boolean,
            default: false
        },
        disableLogic: {
            type: Boolean,
            default: true
        },
        customClasses: {
            type: String,
            default: ''
        },
        translations: {
            type: Object,
            default: {}
        },
        modelValue: {
            default: ''
        },
        selectFirst: {
            type: Boolean,
            default: true
        },
        defaultOpenToAbove: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            focus: false,
            selectedValue: "",
            search: "",
            showDropdown: false,
            items: this.options,
            selectedPosition: 0,
            localOptions: this.options,
            isDropdownAbove: false,
            localTranslations: this.translations,
            searchTimeout: null,
            refreshKey: 0
        };
    },
    methods: {
        show() {

            if (!this.showDropdown && !this.disabled) {
                this.showDropdown = true;
                setTimeout(() => {
                    this.$refs.search.focus();
                }, 200);
                document.addEventListener('keydown', this.handleEscapePress)
                document.addEventListener("click", this.handleOutsideClick);
            } else {
                this.showDropdown = false;
                document.removeEventListener("click", this.handleOutsideClick);
            }
        },
        filter() {
            const normalize = (str) =>
                str.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase();

            this.items = Object.values(this.localOptions).filter(item => {
                const searchNormalized = normalize(this.search || '');

                if (this.searchProperty) {
                    const valueNormalized = normalize(item[this.searchProperty] || '');
                    return valueNormalized.includes(searchNormalized);
                }

                return normalize(item || '').includes(searchNormalized);
            });
            this.refreshKey++;
        },
        select(item) {
            this.selectedValue = item;
            this.showDropdown = false;
            this.$emit("newValue", this.selectedValue);
            this.$emit('update:modelValue', item[this.inputProperty]);
        },
        keyDown(e) {
            if (e.keyCode == 38) {  ///UP
                this.selectUp();
            }
            if (e.keyCode == 40) {  ///DOWN
                this.selectDown();
            }

        },
        selectDown() {
            this.selectedPosition = this.items.map((e) => {
                return e.id;
            }).indexOf(this.selectedValue.id);
            if (!this.selectedPosition) {
                this.selectedPosition = 0;
            }
            if (this.selectedPosition < this.items.length - 1) {
                this.selectedPosition++;
            }
            this.selectedValue = this.items[this.selectedPosition];
            document.getElementById(this.selectedValue.id).scrollIntoView({block: "end"});
        },
        selectUp() {
            this.selectedPosition = this.items.map((e) => {
                return e.id;
            }).indexOf(this.selectedValue.id);
            if (!this.selectedPosition) {
                this.selectedPosition = 0;
            }
            if (this.selectedPosition > 0) {
                this.selectedPosition--;
            }

            this.selectedValue = this.items[this.selectedPosition];
            document.getElementById(this.selectedValue.id).scrollIntoView({block: "end"});
        },
        handleOutsideClick(event) {
            if (!this.$refs.selectContainer?.contains(event.target)) {
                this.showDropdown = false;
                document.removeEventListener("click", this.handleOutsideClick);
            }
        },
        handleEscapePress(event) {
            if (event.key === 'Escape') {
                this.showDropdown = false;
                document.removeEventListener("keydown", this.handleEscapePress);
                document.removeEventListener("click", this.handleOutsideClick);
            }
        },
        checkDropdownPosition() {
            this.$nextTick(() => {
                const dropdown = this.$refs.dropdown;
                const input = this.$refs.visible_select;
                if (!dropdown || !input) return;
                // const dropdownRect = dropdown.getBoundingClientRect();
                const inputRect = input.getBoundingClientRect();
                const spaceBelow = window.innerHeight - inputRect.bottom;
                const spaceAbove = inputRect.top;
                const actualHeight = dropdown.offsetHeight;

                if (this.defaultOpenToAbove) {
                    // invertovaná logika: ak je dosť miesta hore aj dole, preferuj hore
                    if (spaceAbove >= actualHeight && spaceBelow >= actualHeight) {
                        this.isDropdownAbove = true;
                    } else {
                        this.isDropdownAbove = spaceBelow < actualHeight && spaceAbove > spaceBelow;
                    }
                } else {
                    // pôvodná logika: otváraj hore len ak dolu nie je miesto
                    this.isDropdownAbove = spaceBelow < actualHeight && spaceAbove > spaceBelow;
                }
            })
        },
        init() {
            if (this.allOption && this.localOptions[0] && this.localOptions[0]['id'] !== null) {
                let temp = {};
                if (!this.searchProperty) {
                    temp = ""
                } else {
                    temp[this.searchProperty] = this.localTranslations['all'] ?? '';
                    temp[this.inputProperty] = null;
                }

                this.localOptions.unshift(temp);
            }
            if (this.selected) {
                this.selectedValue = Object.values(this.localOptions).find(option => {
                    if (this.inputProperty) {
                        return option[this.inputProperty] == this.selected;
                    }
                    return option == this.selected;
                });
            } else {
                if (this.selectFirst) {
                    this.selectedValue = this.items.at(0);
                }
                this.$emit('update:modelValue', this.selectedValue[this.inputProperty]);
            }
        },
    },

    beforeMount() {
        if (Object.values(this.localTranslations).length === 0) {
            axios.get('/global/filter/get-default-data/actions').then(result => {
                this.localTranslations = result.data
                this.init()
            })
        } else {
            this.init()
        }
    },
    mounted() {

    },
    watch: {
        showDropdown(newValue) {
            if (newValue) {
                this.checkDropdownPosition()
            }
        },
        options(newValue) {
            this.localOptions = newValue
            this.items = newValue
            this.init()
        }
    }

};
</script>

<style scoped>

.max-height {
    max-height: 15rem;
}

</style>
