<!--
Example 1 if working with objects you must add the input and search key:
    <universal-multiple-select2
        search-property="name"
        input-property="id"
        name="order_groups"
        :options="{{ $orderGroups }}"
        :selected="{{ $wsPartnerOrderFormSetting->orderGroups ?? json_encode([]) }}"
        :select-all-option=" {{json_encode(true) }} "
    />

Example 2 if you working with array:
    <universal-multiple-select2
        name="emails"
        :options="{{ json_encode($emails) }}"
        :selected="{{$wsPartnerOrderFormSetting->emails ?? json_encode([])}}"
        :select-all-option=" {{json_encode(true) }} "
    />

    select-all-option -> this allows to select all of options
-->


<template>
    <!--    <label v-if="label" @click.prevent="show()" :for="`${this.name}_universal_multiselect2`"-->
    <!--           class="form-label">{{ this.label }}</label>-->
    <div class="relative w-full" @keydown.esc.prevent="showDropdown=false">
        <div :class="getClass()" class="flex ">

            <ul v-if="Object.values(this.localSelected).length > 0 && this.showSelected" ref="visible_select"
                @click.prevent="show()"
                @keydown="keyDown"
                @keypress.enter.prevent="show()"
                :class="['input-filter-select m-0 p-1 pointer universal-select2 universal-select2-list-container flex flex-wrap items-center gap-1', {'custom-outline': showDropdown, 'rounded-s-md': disableLogic}, this.customClasses]"
            >
                <li v-for="(item,index) in this.localSelected"
                    class=" border bg-gray-200  universal-select2-list-item rounded-md flex items-center">
                      <span class="flex items-center px-2 gap-1 text-xs  h-auto">
                            <span class="text-lg text-gray-400 opacity-50"
                                  @click.stop="remove(item)">
                            <i class="hrms hrms-close"></i>
                          </span>
                        {{ this.searchProperty ? item[this.searchProperty] : item }}
                      </span>
                </li>
            </ul>
            <input
                ref="visible_select"
                v-else
                readonly
                @click.prevent="show()"
                @keydown="keyDown"
                @keypress.enter.prevent="show()"
                :class="['input rounded-e-none', {'custom-outline':showDropdown || focus,'bg-gray-200':disabled, 'rounded-s-none': !disableLogic}, this.customClasses]"
                type="text"
                :placeholder="this.placeholder"
                :id="this.name"
            >
            <span
                @click="show()"
                type="button"
                :class="[{'btn btn-primary rounded-s-none px-0 flex items-center relative border-gray-400': true, 'custom-outline':showDropdown, 'btn-outline':!showDropdown},this.customClasses]">
                    <svg class="opacity-75 size-5" viewBox="0 0 20 20" fill="currentColor"
                         aria-hidden="true" data-slot="icon">
                        <path fill-rule="evenodd"
                              d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z"
                              clip-rule="evenodd"/>
                    </svg>
            </span>
        </div>
        <div v-if="showDropdown" ref="dropdown"
             class="absolute z-50 w-full border bg-base-100 shadow-sm rounded-b-md"
             :class="[
        'z-50 w-full border border-gray-400 bg-base-100 shadow-sm absolute rounded-md',
        isDropdownAbove ? 'bottom-full mb-1 rounded-t-md' : 'top-full mt-1 rounded-b-md'
      ]">
            <div class="m-2" v-if="!isDropdownAbove">
                <input ref="search" @keypress.enter.prevent="selectSearch()" @keydown="keyDown"
                       @keydown.tab="showDropdown=false" @input="this.filter()" v-model="search"
                       :id="`${this.name}_universal_multiselect2_search`" type="text"
                       placeholder="" class="input">
            </div>
            <div class="max-height overflow-auto">
                <div
                    :id="`${this.name}_opt_${index}`"
                    @click="select(item)"
                    @keydown.enter.prevent="showDropdown=false"
                    :class="['hover px-2 text-sm w-full',{'font-medium': ((item[this.inputProperty] ?? '') === this.localTranslations.all || item === this.localTranslations.all) && this.selectAllOption}]"
                    v-for="(item,index) in this.sortedItems"
                >
                    {{ this.searchProperty ? item[searchProperty] : item }}
                </div>
            </div>
            <div class="m-2" v-if="isDropdownAbove">
                <input ref="search" @keypress.enter.prevent="selectSearch()" @keydown="keyDown"
                       @keydown.tab="showDropdown=false" @input="this.filter()" v-model="search"
                       :id="`${this.name}_universal_multiselect2_search`" type="text"
                       placeholder="" class="input">
            </div>
        </div>
        <input v-for="(item,index) in this.realSelected" type="hidden"
               :id="`${this.name}_universal_multiselect2_selected_opt_${index}`" :name="`${this.name}[]`"
               :value="this.inputProperty ? item[this.inputProperty] : item" class="visually-hidden">
    </div>
    <div @click="showDropdown=false" v-if="showDropdown" class="fixed top-0 start-0 w-full h-full z-40">

    </div>

</template>

<script>
export default {
    name: "UniversalMultipleSelect2",
    emits: ["newValue", "changed"],
    props: {
        label: null,
        disabled: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: []
        },
        selected: {
            type: Array,
            default: []
        },
        searchProperty: {
            default: null
        },
        inputProperty: {
            default: null
        },
        name: {
            default: ""
        },
        size: {
            type: String,
            default: ""
        },
        placeholder: {
            type: String,
            default: "Please select..."
        },
        selectAllOption: {
            type: Boolean,
            default: false
        },
        showSelected: {
            type: Boolean,
            default: true
        },
        disableLogic: {
            type: Boolean,
            default: true
        },
        customClasses: {
            type: String,
            default: ''
        },
        translations: {
            type: Object,
            default: {}
        },
        defaultOpenToAbove: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            focus: false,
            selectedValue: "",
            search: "",
            showDropdown: false,
            items: JSON.parse(JSON.stringify(this.options)),
            notFilteredItems: [],
            selectedPosition: 0,
            localSelected: JSON.parse(JSON.stringify(this.selected)),
            realSelected: JSON.parse(JSON.stringify(this.selected)),
            isAllSelected: false,
            allOption: "All",
            isDropdownAbove: false,
            localTranslations: this.translations
        };
    },
    beforeMount() {
        if (Object.values(this.localTranslations).length === 0) {
            axios.get('/global/filter/get-default-data/actions').then(result => {
                this.localTranslations = result.data
                this.allOption = this.localTranslations.all
                this.init()
            })
        } else {
            this.init()
        }
    },
    mounted() {

    },
    methods: {
        getClass() {
            if (this.size) {
                return "input-group-" + this.size;
            } else return "";
        },
        show() {
            if (!this.showDropdown && !this.disabled) {
                this.showDropdown = true;
                setTimeout(() => {
                    this.$refs.search.focus();
                }, 200);
            } else this.showDropdown = false;
        },
        filter() {
            const normalize = (str) =>
                (str || '').normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase();

            const searchNormalized = normalize(this.search);

            this.items = Object.values(this.notFilteredItems).filter(item => {
                if (!this.searchProperty) {
                    return normalize(item).includes(searchNormalized);
                }

                return normalize(item[this.searchProperty]).includes(searchNormalized);
            });
        },
        select(item) {
            if (((item[this.inputProperty] ?? "") === this.localTranslations.all || item === this.localTranslations.all) && this.selectAllOption) {
                this.selectAll(item);
                return 0;
            }

            if (this.isAllSelected) {
                this.resetData();
            }

            this.localSelected.unshift(item);
            this.realSelected.unshift(item);

            if (!this.inputProperty) {
                this.items = this.items.filter((e) => {
                    return e !== item;
                });
                this.notFilteredItems = this.notFilteredItems.filter((e) => {
                    return e !== item;
                });
            } else {
                this.items = this.items.filter((e) => {
                    return e[this.inputProperty] !== item[this.inputProperty];
                });
                this.notFilteredItems = this.notFilteredItems.filter((e) => {
                    return e[this.inputProperty] !== item[this.inputProperty];
                });
            }

            this.$emit("newValue", this.realSelected);
            this.$emit("changed", this.realSelected);
        },
        remove(item) {
            if (((item[this.inputProperty] ?? "") === this.localTranslations.all || item === this.localTranslations.all) && this.selectAllOption) {
                this.resetData();
            } else {
                this.items.push(item);
                this.notFilteredItems.push(item);
                this.localSelected = this.localSelected.filter((e) => {
                    if (!this.inputProperty) {
                        return e !== item;
                    }
                    return e[this.inputProperty] !== item[this.inputProperty];
                });
                this.realSelected = this.realSelected.filter((e) => {
                    if (!this.inputProperty) {
                        return e !== item;
                    }
                    return e[this.inputProperty] !== item[this.inputProperty];
                });
                this.$emit("newValue", this.realSelected);
                this.$emit("changed", this.realSelected);
            }
        },
        selectSearch() {
            if (!this.inputProperty) {
                this.localSelected.unshift(this.search);
                this.realSelected.unshift(this.search);
                this.search = "";
                this.filter();
            }
        },
        keyDown(e) {
            if (e.keyCode == 38) {  ///UP
                this.selectUp();
            }
            if (e.keyCode == 40) {  ///DOWN
                this.selectDown();
            }
        },
        selectDown() {
            this.selectedPosition = this.items.map((e) => {
                return e.id;
            }).indexOf(this.selectedValue.id);
            if (!this.selectedPosition) {
                this.selectedPosition = 0;
            }
            if (this.selectedPosition < this.items.length - 1) {
                this.selectedPosition++;
            }
        },
        selectUp() {
            this.selectedPosition = this.items.map((e) => {
                return e.id;
            }).indexOf(this.selectedValue.id);
            if (!this.selectedPosition) {
                this.selectedPosition = 0;
            }
            if (this.selectedPosition > 0) {
                this.selectedPosition--;
            }
        },
        selectAll(item) {
            this.localSelected = [];
            this.localSelected.push(item);
            this.isAllSelected = true;
            this.items = JSON.parse(JSON.stringify(this.options));
            this.notFilteredItems = JSON.parse(JSON.stringify(this.options));
            this.realSelected = JSON.parse(JSON.stringify(this.options));
            this.$emit("newValue", this.realSelected);
            this.$emit("changed", this.realSelected);
            this.filter();
        },
        resetData() {
            this.realSelected = [];
            this.localSelected = [];
            this.isAllSelected = false;
            this.items = JSON.parse(JSON.stringify(this.options));
            this.items.unshift(this.allOption);
            this.notFilteredItems = JSON.parse(JSON.stringify(this.items));
            this.$emit("newValue", this.realSelected);
            this.$emit("changed", this.realSelected);
            this.filter();
        },
        checkDropdownPosition() {
            this.$nextTick(() => {
                const dropdown = this.$refs.dropdown;
                const input = this.$refs.visible_select;
                if (!dropdown || !input) return;
                // const dropdownRect = dropdown.getBoundingClientRect();
                const inputRect = input.getBoundingClientRect();
                const spaceBelow = window.innerHeight - inputRect.bottom;
                const spaceAbove = inputRect.top;
                const actualHeight = dropdown.offsetHeight;

                this.isDropdownAbove = spaceBelow < actualHeight && spaceAbove > spaceBelow;

                if (this.defaultOpenToAbove) {
                    if (spaceAbove >= actualHeight && spaceBelow >= actualHeight) {
                        this.isDropdownAbove = true;
                    } else {
                        this.isDropdownAbove = spaceBelow < actualHeight && spaceAbove > spaceBelow;
                    }
                } else {
                    this.isDropdownAbove = spaceBelow < actualHeight && spaceAbove > spaceBelow;
                }
            })
        },
        init() {
            if (this.selected.length === this.options.length && this.selectAllOption) {
                this.isAllSelected = true;
            } else if (this.selected.length > 0) {
                this.items = this.items.filter((item) => {
                    if (!this.inputProperty) {
                        return !this.localSelected.includes(item);
                    }
                    return !this.localSelected.find((i) => {
                        return item[this.inputProperty] === i[this.inputProperty];
                    });
                });
            }

            if (this.selectAllOption) {
                if (this.inputProperty) {
                    let item = {};
                    item[this.inputProperty] = this.localTranslations.all;
                    item[this.searchProperty] = this.localTranslations.all;
                    this.allOption = item;
                }

                if (!this.isAllSelected) {
                    this.items.unshift(this.allOption);
                } else {
                    this.localSelected = [];
                    this.localSelected.push(this.allOption);
                    this.realSelected = this.options;
                }
            }

            this.notFilteredItems = JSON.parse(JSON.stringify(this.items));
        }
    },
    computed: {
        sortedItems() {
            return this.items.sort((a, b) => {
                if (!this.searchProperty) {
                    if ((a === this.localTranslations.all || b === this.localTranslations.all) && this.selectAllOption) return 0;
                    if (a < b) {
                        return -1;
                    }
                    if (a > b) {
                        return 1;
                    }
                    return 0;
                }
                if ((a[this.searchProperty] === this.localTranslations.all || b[this.searchProperty] === this.localTranslations.all) && this.selectAllOption) return 0;
                if (a[this.searchProperty] < b[this.searchProperty]) {
                    return -1;
                }
                if (a[this.searchProperty] > b[this.searchProperty]) {
                    return 1;
                }
                return 0;
            });
        }
    },
    watch: {
        showDropdown(newValue) {
            if (newValue) {
                this.checkDropdownPosition()
            }
        }
    }
};
</script>

<style scoped>
.universal-select2 {
    background-color: #ffffff;
}

.border-bottom-radius-0 {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

.custom-outline {
    outline-color: #c3dbff;
    outline-width: 4px;
    outline-style: solid;
}

.pointer {
    cursor: pointer;
}

.hover:hover {
    background: #0d6efd;
    color: white;
    cursor: pointer;
}


.z-index-custom {
    z-index: 1060;
}

.z-overlay {
    z-index: 1050;
}

.max-height {
    max-height: 20rem;
}

.rounded-bottom {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.bg-lighter {
    background-color: #e4e4e4 !important;
}

.universal-select2-list-container {
    overflow-y: auto;
}


.universal-select2-list-item {
    border: 1px;
}

.rounded-start-0 {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-outline-secondary {
    border-color: lightgray !important;
}

.btn-outline-secondary:hover {
    background-color: lightgray !important;
    border-color: lightgray !important;
}

/* .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}*/
</style>
