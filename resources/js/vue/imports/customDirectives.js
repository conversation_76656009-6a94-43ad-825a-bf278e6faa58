export default {
    beforeMount(el, binding, vnode) {
        el.clickOutsideEvent = (event) => {

            // check if click were not at our element or his child
            if (!(el === event.target || el.contains(event.target))) {
                // If not, make what is in binding value
                binding.value(event);
            }
        };
        // add event listener on documents
        document.addEventListener('click', el.clickOutsideEvent);
    },
    unmounted(el) {
        // Remove listener after disconnect
        document.removeEventListener('click', el.clickOutsideEvent);
    },
};
