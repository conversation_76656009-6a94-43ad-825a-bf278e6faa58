@import "tailwindcss";
@plugin "flyonui" {
    themes: light --default;
  }
@import "../../node_modules/flyonui/variants.css";
@plugin "@iconify/tailwind4";
@source "../../node_modules/flyonui/dist/index.js";

@custom-variant dark (&:where(.nechcemnikdydark, .nechcemnikdydark *));

@theme {
    --text-xxs: 0.625rem;
    --text-xxs--line-height: 0.75rem;
    --font-sans: 'GantModernV2-Regular', ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-hrms: 'hrms', sans-serif;
    --font-gant-modern-light: 'GantModernV2-Light', sans-serif;
    --font-gant-modern-light-italic: 'GantModernV2-LightItalic', sans-serif;
    --font-gant-modern-regular: 'GantModernV2-Regular', sans-serif;
    --font-gant-modern-regular-italic: 'GantModernV2-RegularItalic', sans-serif;
    --font-gant-modern-medium: 'GantModernV2-Medium', sans-serif;
    --font-gant-modern-medium-italic: 'GantModernV2-MediumItalic', sans-serif;
    --font-gant-modern-bold: 'GantModernV2-Bold', sans-serif;
    --font-gant-modern-bold-italic: 'GantModernV2-BoldItalic', sans-serif;
    --font-gant-serif-medium-condensed: 'GantSerif-MediumCondensed', serif;

    --font-weight-light:  'GantModernV2-Light', sans-serif;
    --font-weight-regular:'GantModernV2-Regular', sans-serif;
    --font-weight-medium: 'GantModernV2-Medium', sans-serif;
    --font-weight-bold: 'GantModernV2-Bold', sans-serif;

    /* Override FlyonUI disabled input colors */
    --input-disabled-bg: var(--color-base-100);
    --input-disabled-border: color-mix(in oklab, var(--color-neutral) 15%, transparent);
    --input-disabled-text: var(--color-secondary);
}

@layer base {
    *, ::before, ::after {
        @apply border-neutral/20;
    }

  /*Hide input number arrows */
  input[type="number"] {
    appearance: textfield;
  }

  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    appearance: none;
    margin: 0;
  }

  input[type="number"]::-moz-number-spin-box {
    display: none;
  }

  /* Force disabled input styles at base layer */
  html input:disabled,
  html select:disabled,
  html textarea:disabled,
  html .input:disabled,
  html .select:disabled,
  html .textarea:disabled {
    background-color: color-mix(
      in oklab, var(--color-base-200) 100%, transparent) !important;
    border-color: color-mix(
      in oklab, var(--color-neutral) 15%, transparent) !important;
    color: color-mix(
      in oklab, var(--color-secondary) 100%, transparent) !important;
    cursor: not-allowed !important;
    border-width: 1px !important;
  }

  @font-face {
    font-family: 'GantModernV2-Light';
    src: url('../fonts/GantModernV2-Light.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantModernV2-LightItalic';
    src: url('../fonts/GantModernV2-LightItalic.woff2') format('woff2');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantModernV2-Regular';
    src: url('../fonts/GantModernV2-Regular.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantModernV2-RegularItalic';
    src: url('../fonts/GantModernV2-RegularItalic.woff2') format('woff2');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantModernV2-Medium';
    src: url('../fonts/GantModernV2-Medium.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantModernV2-MediumItalic';
    src: url('../fonts/GantModernV2-MediumItalic.woff2') format('woff2');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantModernV2-Bold';
    src: url('../fonts/GantModernV2-Bold.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantModernV2-BoldItalic';
    src: url('../fonts/GantModernV2-BoldItalic.woff2') format('woff2');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
  }
  @font-face {
    font-family: 'GantSerif-MediumCondensed';
    src: url('../fonts/GantSerif-MediumCondensed.woff2') format('woff2');
    font-weight: normal; /* Assuming medium condensed is around 700, adjust if needed */
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'hrms'; /* Icon font */
    src: url('../fonts/hrms.woff2') format('woff2'),
         url('../fonts/hrms.woff') format('woff'); /* Fallback for woff */
    font-weight: normal; /* Typically normal for icon fonts */
    font-style: normal;
    font-display: block; /* or swap, block is often used for icons */
  }

    html {
        font-family: var(--font-sans);
    }
    .font-light, .font-weight-light {
        font-family: var(--font-gant-modern-light) !important;
        font-weight: var(--font-weight-light) !important;
    }
    .font-normal, .font-regular, .font-weight-normal, .font-weight-regular {
        font-family: var(--font-gant-modern-regular) !important;
        font-weight: var(--font-weight-regular) !important;
    }
    .font-medium, .font-weight-medium {
        font-family: var(--font-gant-modern-medium) !important;
        font-weight: var(--font-weight-medium) !important;
    }
    .font-bold, .font-weight-bold {
        font-family: var(--font-gant-modern-bold) !important;
        font-weight: var(--font-weight-bold) !important;
    }
    .font-light-italic {
        font-family: var(--font-gant-modern-light-italic) !important;
        font-weight: var(--font-weight-light) !important;
    }
    .font-regular-italic, .font-normal-italic {
        font-family: var(--font-gant-modern-regular-italic) !important;
        font-weight: var(--font-weight-regular) !important;
    }
    .font-medium-italic {
        font-family: var(--font-gant-modern-medium-italic) !important;
        font-weight: var(--font-weight-medium) !important;
    }
    .font-bold-italic {
        font-family: var(--font-gant-modern-bold-italic) !important;
        font-weight: var(--font-weight-bold) !important;
    }
}

@layer utilities {
  .remove-arrow {
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    background: none;
    background-image: none;
  }

  input:disabled,
  select:disabled,
  textarea:disabled,
  .input:disabled,
  .select:disabled,
  .textarea:disabled,
  input[disabled],
  select[disabled],
  textarea[disabled],
  .input[disabled],
  .select[disabled],
  .textarea[disabled] {
    background-color: color-mix(
      in oklab, var(--color-base-200) 75%, transparent) !important;
    border-color: color-mix(
      in oklab, var(--color-neutral) 15%, transparent) !important;
    color: color-mix(
      in oklab, var(--color-secondary) 100%, transparent) !important;
    cursor: not-allowed !important;
    border-width: 1px !important;
    opacity: 1 !important;
  }

  select.remove-arrow:disabled,
  select.remove-arrow[disabled],
  .select.remove-arrow:disabled,
  .select.remove-arrow[disabled] {
    background-color: transparent !important;
    border: none !important;
    border-width: 0 !important;
    box-shadow: none !important;
  }

  html body input:disabled,
  html body select:disabled,
  html body textarea:disabled,
  html body .input:disabled,
  html body .select:disabled,
  html body .textarea:disabled {
    background-color: color-mix(
      in oklab, var(--color-base-200) 75%, transparent) !important;
    border-color: color-mix(
      in oklab, var(--color-neutral) 15%, transparent) !important;
    color: color-mix(
      in oklab, var(--color-secondary) 100%, transparent) !important;
    cursor: not-allowed !important;
    border-width: 1px !important;
  }

  /* Maximum specificity for remove-arrow select */
  html body select.remove-arrow:disabled,
  html body select.remove-arrow[disabled],
  html body .select.remove-arrow:disabled,
  html body .select.remove-arrow[disabled] {
    background-color: transparent !important;
    border: none !important;
    border-width: 0 !important;
    box-shadow: none !important;
  }
}

@layer components {
  .input-filter-select {
    @apply border w-full border-gray-400 focus:border-gray-400 focus:ring-1 disabled:bg-gray-200 disabled:pointer-events-none cursor-pointer
  }
}


