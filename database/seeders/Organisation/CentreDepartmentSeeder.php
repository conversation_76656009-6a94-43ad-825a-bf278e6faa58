<?php

namespace Database\Seeders\Organisation;

use App\Models\Organisation\Centre;
use App\Models\Organisation\CentreDepartment;
use App\Models\Organisation\Department;
use App\Models\Organisation\Section;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CentreDepartmentSeeder extends Seeder
{
    public function run(): void
    {
        $officeSection = Section::where('code', 'OFC')->first();
        $departments = Department::select('id', 'code')->with('sections')->get()->keyBy('code');
        $centres = Centre::select('id', 'code')->get();
        $now = now()->toDateTimeString();

        $officeDepartments = $departments->filter(function ($department) use ($officeSection) {
            return $department->sections->contains($officeSection);
        });

        CentreDepartment::truncate();

        $inserArray = [];

        foreach ($centres as $centre) {
            $selectedDepartmentId = match (substr($centre->code, 0, 2)) {
                '2P', '2Z' => $departments['RET-CZ']->id,
                '3P', '3Z' => $departments['RET-SK']->id,
                '4P', '4Z' => $departments['RET-HU']->id,
                '3S' => $departments['LOG']->id,
                default => 'ofc',
            };

            if($selectedDepartmentId === 'ofc') {
                foreach($officeDepartments as $officeDepartment) {
                    if($centre->code === '0S00' && in_array($officeDepartment->code, ['OFFICE-CZ', 'OFFICE-HU'], false)) {
                        continue;
                    }
                    if($centre->code === '4S00' && in_array($officeDepartment->code, ['OFFICE-CZ', 'OFFICE-SK'], false)) {
                        continue;
                    }
                    if(in_array($centre->code, ['2S00', '2S00P', '2S00K', '2S00R'], false) && in_array($officeDepartment->code, ['OFFICE-HU', 'OFFICE-SK'], false)) {
                        continue;
                    }

                    $inserArray[] = [
                        'centre_id' => $centre->id,
                        'department_id' => $officeDepartment->id,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }

                continue;
            }

            $inserArray[] = [
                'centre_id' => $centre->id,
                'department_id' => $selectedDepartmentId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        CentreDepartment::insert($inserArray);
    }
}
