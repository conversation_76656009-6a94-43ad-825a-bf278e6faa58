<?php

namespace Database\Seeders\Organisation;

use App\Models\Organisation\Department;
use App\Models\Organisation\DepartmentSection;
use App\Models\Organisation\Section;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DepartmentSectionSeeder extends Seeder
{
    public function run(): void
    {
        $sections = Section::select('id', 'code')->get()->keyBy('code');
        $departments = Department::select('id', 'code')->get();
        $now = now()->toDateTimeString();

        DepartmentSection::truncate();

        $inserArray = [];

        foreach ($departments as $department) {
            $selectedSectionId = match ($department->code) {
                'BRIG-CZ', 'RET-CZ', 'RET-SK', 'RET-HU' => $sections['RET']->id,
                'LOG' => $sections['WAR']->id,
                default => $sections['OFC']->id,
            };

            $inserArray[] = [
                'department_id' => $department->id,
                'section_id' => $selectedSectionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DepartmentSection::insert($inserArray);
    }
}
