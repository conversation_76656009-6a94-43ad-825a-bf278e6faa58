<?php

namespace Database\Seeders\Organisation;

use App\Models\Organisation\Department;
use App\Models\Organisation\DepartmentPosition;
use App\Models\Organisation\Position;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PositionDepartmentSeeder extends Seeder
{
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();
        DepartmentPosition::truncate();
        Schema::enableForeignKeyConstraints();

        $positions = Position::pluck('id', 'code');
        $departments = Department::pluck('id', 'code');
        $now = now();
        $insertArray = [];

        foreach([
            ['code' => 'A', 'name' => 'Assistant', 'departments' => ['COO'],],
            ['code' => 'ABM', 'name' => 'Asistent Brand Managera', 'departments' => ['BM'],],
            ['code' => 'ACC', 'name' => 'Accountant', 'departments' => ['ACC'],],
            ['code' => 'ACCA', 'name' => 'Accounting Assistant', 'departments' => ['ACC'],],
            ['code' => 'Accountant', 'name' => 'Senior Accountant', 'departments' => ['ACC'],],
            ['code' => 'ACDM', 'name' => 'Accounting Department Manager', 'departments' => ['ACC'],],
            ['code' => 'Administratívny pracovník v sklade', 'name' => 'Administratívny pracovník v sklade', 'departments' => ['LOG'],],
            ['code' => 'AFO', 'name' => 'Asistentka finančního oddělení', 'departments' => ['FIN'],],
            ['code' => 'ARTD', 'name' => 'Senior art director', 'departments' => ['MKT'],],
            ['code' => 'AS', 'name' => 'Asistentka', 'departments' => ['LOG'],],
            ['code' => 'Asistent', 'name' => 'Asistent', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'ASM', 'name' => 'Apparel Sales Manager', 'departments' => ['BM'],],
            ['code' => 'AST', 'name' => 'Assets Specialist', 'departments' => ['ACC'],],
            ['code' => 'AVM', 'name' => 'Area Visual Merchandiser', 'departments' => ['VM'],],
            ['code' => 'BIDS', 'name' => 'Business Intelligence & Data Specialist', 'departments' => ['AD'],],
            ['code' => 'BRIG', 'name' => 'Brigádnik', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'BRM', 'name' => 'Brand Manager', 'departments' => ['BM'],],
            ['code' => 'BS', 'name' => 'Brand Specialist', 'departments' => ['BT'],],
            ['code' => 'CBO', 'name' => 'Chief brand officer', 'departments' => ['COO'],],
            ['code' => 'CCR', 'name' => 'Content Creator', 'departments' => ['MKT'],],
            ['code' => 'CCS', 'name' => 'Call Centrum Specialist', 'departments' => ['EC'],],
            ['code' => 'CD', 'name' => 'Creative Director', 'departments' => ['MKT'],],
            ['code' => 'CFO', 'name' => 'Chief FInancial Officer', 'departments' => ['FIN'],],
            ['code' => 'CLEAN', 'name' => 'Cleaner, Administrator of the KP', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'CLM', 'name' => 'Claims Manager', 'departments' => ['BT'],],
            ['code' => 'COO', 'name' => 'Chief Operations Officer', 'departments' => ['COO'],],
            ['code' => 'CPRM', 'name' => 'Content & PR Manager', 'departments' => ['MKT'],],
            ['code' => 'DMM', 'name' => 'Digital Marketing Manager', 'departments' => ['MKT'],],
            ['code' => 'DSM', 'name' => 'Deputy Store Manager', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'E-shop', 'name' => 'E-shop Manager', 'departments' => ['EC'],],
            ['code' => 'e-shop Admin', 'name' => 'E-shop Administrator', 'departments' => ['IT'],],
            ['code' => 'ECCon', 'name' => 'E-commerce Consultant', 'departments' => ['EC'],],
            ['code' => 'EMS', 'name' => 'E-mail Marketing Specialist', 'departments' => ['MKT'],],
            ['code' => 'Eshop Šp', 'name' => 'Eshop Špecialista', 'departments' => ['EC'],],
            ['code' => 'FED', 'name' => 'Frontend Developer', 'departments' => ['IT'],],
            ['code' => 'FM', 'name' => 'Facility Manager', 'departments' => ['SC'],],
            ['code' => 'FT', 'name' => 'Facility Technician', 'departments' => ['SC'],],
            ['code' => 'HCBD', 'name' => 'Head of Controlling / Business Development', 'departments' => ['AD'],],
            ['code' => 'Head of WD', 'name' => 'Head of Web Developement', 'departments' => ['IT'],],
            ['code' => 'HoBT', 'name' => 'Head of Business Team', 'departments' => ['BT'],],
            ['code' => 'HoCD', 'name' => 'Head of Communication Department', 'departments' => ['MKT'],],
            ['code' => 'HoVM', 'name' => 'Head of Visual Merchandising', 'departments' => ['VM'],],
            ['code' => 'HR assistant', 'name' => 'HR assistant', 'departments' => ['HR'],],
            ['code' => 'HR Director', 'name' => 'HR Director', 'departments' => ['HR'],],
            ['code' => 'HR Generalist', 'name' => 'HR Generalist', 'departments' => ['HR'],],
            ['code' => 'HRMNG', 'name' => 'HR Manager', 'departments' => ['HR'],],
            ['code' => 'HRS', 'name' => 'HR Specialist', 'departments' => ['HR'],],
            ['code' => 'HS', 'name' => 'Head of IT Support', 'departments' => ['IT'],],
            ['code' => 'IDSG', 'name' => 'Interier Designer', 'departments' => ['SC'],],
            ['code' => 'IT Director', 'name' => 'IT Director', 'departments' => ['IT'],],
            ['code' => 'IT Operations Man.', 'name' => 'IT Operations Manager', 'departments' => ['IT'],],
            ['code' => 'IT tech', 'name' => 'IT Technician', 'departments' => ['IT'],],
            ['code' => 'Junior Accountant', 'name' => 'Junior Accountant', 'departments' => ['ACC'],],
            ['code' => 'Junior Účtovník', 'name' => 'Junior Účtovník', 'departments' => ['ACC'],],
            ['code' => 'LSM', 'name' => 'Leasing Manager', 'departments' => ['FIN'],],
            ['code' => 'Manažér logistiky', 'name' => 'Manažér logistiky', 'departments' => ['LOG'],],
            ['code' => 'MCS', 'name' => 'Marketing Communication Specialist', 'departments' => ['MKT'],],
            ['code' => 'MDD', 'name' => 'Manager of Development Department', 'departments' => ['DD'],],
            ['code' => 'MKTD', 'name' => 'Marketing Director', 'departments' => ['MKT'],],
            ['code' => 'Mng', 'name' => 'Manager', 'departments' => ['COO'],],
            ['code' => 'MNTC SK/HU', 'name' => 'Maintaince SK / HU', 'departments' => ['SC'],],
            ['code' => 'MS', 'name' => 'Manažér skladu', 'departments' => ['LOG'],],
            ['code' => 'Mzdová účtovníčka', 'name' => 'Mzdová účtovníčka', 'departments' => ['HR'],],
            ['code' => 'Office', 'name' => 'Office', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'Office Asistentka', 'name' => 'Office Asistentka', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'Office assistant', 'name' => 'Office assistant', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'Office M.', 'name' => 'Office Manager', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'OS/SM', 'name' => 'Outlet Supervisor, Store Manager', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'PaWMSSp', 'name' => 'Procesný a WMS špecialista', 'departments' => ['LOG'],],
            ['code' => 'Payroll Acountant', 'name' => 'Payroll Accountant', 'departments' => ['HR'],],
            ['code' => 'PHP DEV', 'name' => 'PHP Developer', 'departments' => ['IT'],],
            ['code' => 'PPSJ', 'name' => 'Production and PR Specialist Junior', 'departments' => ['MKT'],],
            ['code' => 'Production / CRM Specialist', 'name' => 'Production / CRM Specialist', 'departments' => ['MKT'],],
            ['code' => 'Projektový Špec. Eshop', 'name' => 'Projektový Špecialista v Eshope', 'departments' => ['IT'],],
            ['code' => 'Recepční', 'name' => 'Recepční', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'Retail Director', 'name' => 'Retail Director', 'departments' => ['RET'],],
            ['code' => 'Retail Outlet Manager', 'name' => 'Retail Outlet Manager', 'departments' => ['RET'],],
            ['code' => 'Retail specialist', 'name' => 'Retail specialist', 'departments' => ['RET'],],
            ['code' => 'RL', 'name' => 'Riaditeľ logistiky', 'departments' => ['LOG'],],
            ['code' => 'RM', 'name' => 'Retail Manager', 'departments' => ['RET'],],
            ['code' => 'RPM', 'name' => 'Retail Project Manager', 'departments' => ['RET'],],
            ['code' => 'S/E', 'name' => 'Skladník/Expedient', 'departments' => ['LOG'],],
            ['code' => 'S/V', 'name' => 'Skladník/Vodič', 'departments' => ['LOG'],],
            ['code' => 'SA', 'name' => 'Store assistant', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'SA/IVM', 'name' => 'Store assistant - Instore visual merchandiser', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'SAPvS', 'name' => 'Seniorný administratívny pracovník v sklade', 'departments' => ['LOG'],],
            ['code' => 'SCD', 'name' => 'Store Concept Director', 'departments' => ['SC'],],
            ['code' => 'SCPS', 'name' => 'Senior Financial Controller and Project Specialist', 'departments' => ['FIN'],],
            ['code' => 'SCS', 'name' => 'Store Concept Specialist', 'departments' => ['SC'],],
            ['code' => 'Senior IT SYA & WD', 'name' => 'Senior IT System Administrator & Web Developer', 'departments' => ['IT'],],
            ['code' => 'ShSV', 'name' => 'Shift Supervisor', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'Skladník', 'name' => 'Skladník', 'departments' => ['LOG'],],
            ['code' => 'SM', 'name' => 'Store Manager', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'SMG', 'name' => 'Sales Manager', 'departments' => ['FIN'],],
            ['code' => 'SMSP', 'name' => 'Store Manager Senior Partner', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'Správca k.tovaru', 'name' => 'Správca katalógu tovaru', 'departments' => ['IT'],],
            ['code' => 'Správca majetku', 'name' => 'Správca majetku', 'departments' => ['SC'],],
            ['code' => 'SSPM', 'name' => 'Senior Software Project Manager', 'departments' => ['IT'],],
            ['code' => 'SSTS', 'name' => 'Senior Store Supervisor', 'departments' => ['RET'],],
            ['code' => 'SS', 'name' => 'Store Supervisor', 'departments' => ['RET'],],
            ['code' => 'SÚčt', 'name' => 'Samostatný účtovník', 'departments' => ['ACC'],],
            ['code' => 'SW PM', 'name' => 'Software Project Manager', 'departments' => ['IT'],],
            ['code' => 'T/Ss', 'name' => 'Technik/Správca skladu', 'departments' => ['LOG'],],
            ['code' => 'TRA', 'name' => 'Treasury Administrator', 'departments' => ['FIN'],],
            ['code' => 'Upratovačka', 'name' => 'Upratovačka', 'departments' => ['OFFICE-CZ', 'OFFICE-SK', 'OFFICE-HU'],],
            ['code' => 'UXD', 'name' => 'UX Designer', 'departments' => ['IT'],],
            ['code' => 'VCC', 'name' => 'Vedúci Call Centra', 'departments' => ['EC'],],
            ['code' => 'Ver Brig', 'name' => 'Vermont brigádník', 'departments' => ['RET-CZ', 'RET-SK', 'RET-HU'],],
            ['code' => 'VLM', 'name' => 'Vermont Lounge Manager', 'departments' => ['RET-CZ'],],
            ['code' => 'VM', 'name' => 'Visual Merchandiser', 'departments' => ['VM'],],
            ['code' => 'VS', 'name' => 'Vedúci skladu', 'departments' => ['LOG'],],
            ['code' => 'WFS', 'name' => 'Warehouse and Fleet Specialist', 'departments' => ['FIN'],],
            ['code' => 'Zástupca vedúceho skladu', 'name' => 'Zástupca vedúceho skladu', 'departments' => ['LOG'],],
        ] as $array) {
            foreach($array['departments'] as $department) {
                $insertArray[] = [
                    'position_id' => $positions[$array['code']],
                    'department_id' => $departments[$department],
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        DepartmentPosition::insert($insertArray);

        return;


        //add positions to HR department
        foreach([
            'HR Director',
        ] as $positionCode) {
            $insertArray[] = [
                'position_id' => $positions[$positionCode],
                'department_id' => $departments['HR'],
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        //add positions to RM Management department
        foreach([
            'Retail Director',
            'Retail Outlet Manager',
            'RM',
        ] as $positionCode) {
            $insertArray[] = [
                'position_id' => $positions[$positionCode],
                'department_id' => $departments['RET'],
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        //add positions to RET-* department
        foreach([
            'SM',
            'SMSP',
            'OS/SM',
            'DSM',
            'SA/IVM',
            'ShSV',
            'SA',
        ] as $positionCode) {
            foreach([
                'RET-CZ',
                'RET-SK',
                'RET-HU',
            ] as $departmentCode) {
                $insertArray[] = [
                    'position_id' => $positions[$positionCode],
                    'department_id' => $departments[$departmentCode],
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        DepartmentPosition::insert($insertArray);
    }
}
