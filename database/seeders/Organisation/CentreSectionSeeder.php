<?php

namespace Database\Seeders\Organisation;

use App\Models\Organisation\Centre;
use App\Models\Organisation\CentreSection;
use App\Models\Organisation\CentreType;
use App\Models\Organisation\Section;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CentreSectionSeeder extends Seeder
{
    public function run(): void
    {
        $sections = Section::select('id', 'code')->get()->keyBy('code');
        $centres = Centre::select('id', 'centre_type_id')->get();
        $now = now()->toDateTimeString();

        CentreSection::truncate();

        $inserArray = [];

        foreach ($centres as $centre) {
            $selectedSectionId = match ($centre->centre_type_id) {
                CentreType::ID_WAREHOUSE => $sections['WAR']->id,
                CentreType::ID_OFFICE => $sections['OFC']->id,
                default => $sections['RET']->id,
            };

            $inserArray[] = [
                'centre_id' => $centre->id,
                'section_id' => $selectedSectionId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        CentreSection::insert($inserArray);
    }
}
