<?php

namespace Database\Seeders\Organisation;

use App\Models\Organisation\Department;
use App\Models\Organisation\DepartmentPosition;
use App\Models\Organisation\Position;
use App\Models\Organisation\PositionSection;
use App\Models\Organisation\Section;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PositionSectionSeeder extends Seeder
{
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();
        PositionSection::truncate();
        Schema::enableForeignKeyConstraints();

        $positions = Position::pluck('id', 'code');
        $sections = Section::pluck('id', 'code');
        $now = now();
        $insertArray = [];

        foreach([
            'HR Director',
            'Retail Director',
            'Retail Outlet Manager',
            'RM',

            'ACC',
            'ACCA',
            'ACDM',
            'ASM',
            'AVM',
            'Asistent',
            'ABM',
            'AFO',
            'AST',
            'A',
            'BRM',
            'BS',
            'BIDS',
            'CCS',
            'CBO',
            'CFO',
            'COO',
            'CLM',
            'CLEAN',
            'CPRM',
            'CCR',
            'CD',
            'DMM',
            'ECCon',
            'EMS',
            'e-shop Admin',
            'E-shop',
            'Eshop Šp',
            'FM',
            'FT',
            'FED',
            'HoBT',
            'HoCD',
            'HCBD',
            'HS',
            'HoVM',
            'Head of WD',
            'HR assistant',
            'HR Generalist',
            'HRMNG',
            'HRS',
            'IDSG',
            'IT Director',
            'IT Operations Man.',
            'IT tech',
            'Junior Accountant',
            'Junior Účtovník',
            'LSM',
            'MNTC SK/HU',
            'Mng',
            'MDD',
            'MCS',
            'MKTD',
            'Mzdová účtovníčka',
            'Office',
            'Office Asistentka',
            'Office assistant',
            'Office M.',
            'Payroll Acountant',
            'PHP DEV',
            'Production / CRM Specialist',
            'PPSJ',
            'Projektový Špec. Eshop',
            'Recepční',
            'RPM',
            'Retail specialist',
            'SMG',
            'SÚčt',
            'Accountant',
            'ARTD',
            'SCPS',
            'Senior IT SYA & WD',
            'SSPM',
            'SSTS',
            'SW PM',
            'Správca k.tovaru',
            'Správca majetku',
            'SCD',
            'SCS',
            'SS',
            'TRA',
            'Upratovačka',
            'UXD',
            'VCC',
            'VM',
            'WFS',
        ] as $positionCode) {
            $insertArray[] = [
                'position_id' => $positions[$positionCode],
                'section_id' => $sections['OFC'],
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        foreach([
            'AS',
            'Administratívny pracovník v sklade',
            'Manažér logistiky',
            'MS',
            'PaWMSSp',
            'RL',
            'SAPvS',
            'Skladník',
            'S/E',
            'S/V',
            'T/Ss',
            'VS',
            'Zástupca vedúceho skladu',
        ] as $positionCode) {
            $insertArray[] = [
                'position_id' => $positions[$positionCode],
                'section_id' => $sections['WAR'],
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        foreach([
            'SM',
            'SMSP',
            'OS/SM',
            'DSM',
            'SA/IVM',
            'ShSV',
            'SA',
            'BRIG',
            'Ver Brig',
            'VLM',
        ] as $positionCode) {
            $insertArray[] = [
                'position_id' => $positions[$positionCode],
                'section_id' => $sections['RET'],
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        PositionSection::insert($insertArray);
    }
}
