<?php

namespace Database\Seeders\RaP;

use App\Models\RaP\Permission;
use App\Models\RaP\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RolePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach([
            Role::HR_EMPLOYEE => [
                Permission::HR_ADMINISTRATION_ACCESS,
            ],
            Role::IT_EMPLOYEE => [
                Permission::IT_ADMINISTRATION_ACCESS,
            ],
        ] as $role => $permissions)
        {
            Role::findByName($role)->givePermissionTo($permissions);
        }
    }
}
