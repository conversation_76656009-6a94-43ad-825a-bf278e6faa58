<?php

namespace Database\Seeders\RaP;

use App\Models\RaP\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach([
            Role::HR_EMPLOYEE,
            Role::IT_EMPLOYEE,
        ] as $role) {
            Role::updateOrCreate([
                'name' => $role,
                'guard_name' => 'web',
            ]);
        }
    }
}
