<?php

namespace Database\Seeders\Init;

use App\Enums\Person\Assigment\AssigmentTypeEnum;
use App\Models\Organisation\Centre;
use App\Models\Person\Assigment;
use App\Models\Person\User;
use Illuminate\Database\Seeder;

class UsersAssigmentsSeeder extends Seeder
{
    public function run(): void
    {
        $retailManagersArray = [
            'u2210' => [ //Prochazkova
                '2P01',
                '2P08',
                '2P07',
                '2P16',
                '2P21',
                '2P28',
                '2P33',
                '2P34',
                '2P53',
                '2P60',
                '2P62',
                '2P64',
                '2P65',
                '2P69',
                '2P71',
                '2P72',
            ],
            'u1517' => [ //Slavikova
                '2P02',
                '2P10',
                '2P09',
                '2P17',
                '2P24',
                '2P29',
                '2P30',
                '2P36',
                '2P38',
                '2P41',
                '2P47',
                '2P51',
                '2P59',
                '2P63',
                '2P73',
                '2P74',
                '2P75',
            ],
            'u2039' => [ //Buzinkay
                '2P13',
                '2P15',
                '2P23',
                '2P25',
                '2P31',
                '2P32',
                '2P44',
                '2P49',
                '2P54',
                '2P55',
                '2P56',
                '2P57',
                '2P58',
                '3P15',
            ],
            'u1258' => [ //Durovkinova
                '2P05',
                '2P12',
                '2P14',
                '2P20',
                '2P26',
                '2P37',
                '2P39',
                '2P40',
                '2P42',
                '2P61',
                '2P67',
                '2P68',
                '2P76',
                '3P09',
                '3P10',
                '3P11',
                '3P32',
            ],
            'u1130' => [ //Pudmarcikova
                '3P01',
                '3P02',
                '3P03',
                '3P04',
                '3P05',
                '3P06',
                '3P07',
                '3P08',
                '3P12',
                '3P13',
                '3P14',
                '3P16',
                '3P17',
                '3P18',
                '3P19',
                '3P20',
                '3P21',
                '3P22',
                '3P23',
                '3P24',
                '3P25',
                '3P26',
                '3P27',
                '3P28',
                '3P29',
                '3ZRUN',
                '3P30',
                '3P33',
            ],
            'u1384' => [ //Toth
                '4P01',
                '4P02',
                '4P03',
                '4P04',
                '4P06',
                '4P07',
                '4P08',
                '4P09',
                '4P10',
                '4P11',
                '4P12',
                '4P13',
                '4P14',
                '4P16',
                '4P17',
                '4P18',
                '4P19',
                '4P20',
                '4P21',
                '4P22',
                '4P23',
                '4P24',
                '4P25',
                '4P26',
            ],
        ];

        foreach ($retailManagersArray as $login => $storeCodes) {
            $user = User::where('login', $login)->first();

            if(!$user) {
                continue;
            }

            $userAssigment = Assigment::where('is_from_hrms1', 1)
                ->where('user_id', $user->id)
                ->where('type', AssigmentTypeEnum::EMPLOYEE)
                ->first();

            if(!$userAssigment) {
                continue;
            }

            $centres = Centre::whereIn('code', $storeCodes)->with('address:id,country_id')->get();

            foreach($centres as $centre) {
                Assigment::updateOrCreate([
                    'user_id' => $user->id,
                    'centre_id' => $centre->id,
                    'country_id' => $centre->address->country_id,
                    'position_id' => $userAssigment->position_id,
                    'type' => $userAssigment->type,
                ],
                [
                    'status' => $userAssigment->status,
                ]);
            }
        }
    }
}
