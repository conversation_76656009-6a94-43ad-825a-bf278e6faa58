<?php

namespace Database\Seeders\Init;

use App\Models\Person\User;
use App\Models\RaP\Permission;
use Illuminate\Database\Seeder;

class UsersPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        $hrDirector = User::where('login', 'u1392')->first();
        $remunerationsProposalFunalApproverPermissions = Permission::where('name', Permission::EMPLOYEE_REMUNERATIONS_PROPOSALS_FINAL_APPROVER)->get();

        if(!$hrDirector) {
            return;
        }

        $hrDirector->permissions()->sync($remunerationsProposalFunalApproverPermissions);
    }
}
