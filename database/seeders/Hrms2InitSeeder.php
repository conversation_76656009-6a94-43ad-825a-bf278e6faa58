<?php

namespace Database\Seeders;

use Database\Seeders\Init\UsersAssigmentsSeeder;
use Database\Seeders\Init\UsersPermissionsSeeder;
use Database\Seeders\Organisation\CentreDepartmentSeeder;
use Database\Seeders\Organisation\CentreSectionSeeder;
use Database\Seeders\Organisation\DepartmentSectionSeeder;
use Database\Seeders\Organisation\PositionDepartmentSeeder;
use Database\Seeders\Organisation\PositionHierarchySeeder;
use Database\Seeders\Organisation\PositionSectionSeeder;
use Database\Seeders\RaP\PermissionsSeeder;
use Database\Seeders\RaP\RolePermissionsSeeder;
use Database\Seeders\RaP\RolesSeeder;
use Illuminate\Database\Seeder;

class Hrms2InitSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            PermissionsSeeder::class,
            RolesSeeder::class,
            RolePermissionsSeeder::class,
            PositionDepartmentSeeder::class,
            PositionSectionSeeder::class,
            PositionHierarchySeeder::class,
            UsersPermissionsSeeder::class,
            UsersAssigmentsSeeder::class,
            DepartmentSectionSeeder::class,
            CentreSectionSeeder::class,
            CentreDepartmentSeeder::class,
        ]);
    }
}
