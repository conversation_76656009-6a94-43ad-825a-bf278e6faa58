<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('person_users', function (Blueprint $table) {
            $table->string('lang', 4)->after('phone')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('person_users', function (Blueprint $table) {
            $table->dropColumn('lang');
        });
    }
};
