<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('person_user_auth_codes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('person_users');
            $table->string('code', 8);
            $table->dateTime('expired_at');
            $table->dateTime('validated_at')->nullable();
            $table->string('session_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('person_user_auth_codes');
    }
};
