<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        \App\Models\Remuneration\ProposalSubTypeBudget::truncate();
        Schema::table('remuneration_proposal_subtype_budgets', function (Blueprint $table) {
            $table->foreignId('currency_id')->nullable()->after('budget')->constrained('base_currencies');
        });
    }

    public function down(): void
    {
        Schema::table('remuneration_proposal_subtype_budgets', function (Blueprint $table) {
            $table->dropForeign(['currency_id']);
        });
    }
};
