<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('person_user_filter_bookmarks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('person_users');
            $table->string('filter_name');
            $table->string('preset_name');
            $table->json('settings');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('person_user_filter_bookmarks');
    }
};
