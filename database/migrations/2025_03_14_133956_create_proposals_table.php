<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('remuneration_proposals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('person_users');
            $table->unsignedTinyInteger('status');
            $table->unsignedTinyInteger('payment_status')->nullable();
            $table->unsignedTinyInteger('type');
            $table->unsignedTinyInteger('subtype')->nullable();
            $table->text('base')->nullable();
            $table->text('addition')->nullable();
            $table->foreignId('currency_id')->nullable()->constrained('base_currencies');
            $table->longText('reason')->nullable();
            $table->date('validity')->nullable();
            $table->foreignId('proposer_id')->nullable()->constrained('person_users');
            $table->foreignId('approver_id')->nullable()->constrained('person_users');
            $table->foreignId('centre_id')->nullable()->constrained('organisation_centres');
            $table->foreignId('department_id')->nullable()->constrained('organisation_departments');
            $table->string('pairing_hash', 32)->nullable();
            $table->foreignId('contract_id')->nullable()->constrained('person_contracts');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('remuneration_proposals');
    }
};
