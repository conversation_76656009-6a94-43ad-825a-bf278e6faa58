<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('person_users', function (Blueprint $table) {
            $table->id();
            $table->string('login')->nullable()->unique();
            $table->string('password')->nullable();
            $table->boolean('is_active')->default(false);
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('system_password_reset_tokens', function (Blueprint $table) {
            $table->string('login')->primary();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('system_sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->constrained('person_users')->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('person_users');
        Schema::dropIfExists('system_password_reset_tokens');
        Schema::dropIfExists('system_sessions');
    }
};
