<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organisation_department_section', function (Blueprint $table) {
            $table->primary(['department_id','section_id']);
            $table->foreignId('department_id')->constrained('organisation_departments');
            $table->foreignId('section_id')->constrained('organisation_sections');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('organisation_department_section');
    }
};
