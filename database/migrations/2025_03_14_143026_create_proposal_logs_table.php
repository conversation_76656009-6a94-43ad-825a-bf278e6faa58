<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('remuneration_proposal_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('proposal_id')->constrained('remuneration_proposals');
            $table->foreignId('user_id')->constrained('person_users');
            $table->unsignedTinyInteger('status')->nullable();
            $table->json('changes')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('remuneration_proposal_logs');
    }
};
