<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organisation_position_hierarchy', function (Blueprint $table) {
            $table->id();
            $table->foreignId('position_id')->constrained('organisation_positions');
            $table->foreignId('parent_id')->constrained('organisation_positions');
            $table->boolean('is_pay_approver')->boolean(false);
            $table->boolean('is_centre_pay_approver')->boolean(true);
            $table->unsignedTinyInteger('hierarchy_order')->default(1);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('organisation_position_hierarchy');
    }
};
