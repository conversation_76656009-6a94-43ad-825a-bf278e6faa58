<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('base_countries', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('name');
            $table->string('name_cs')->nullable();
            $table->string('name_sk')->nullable();
            $table->string('name_hu')->nullable();
            $table->foreignId('currency_id')->nullable()->constrained('base_currencies');
            $table->unsignedInteger('cdb_id')->nullable()->index();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('base_countries');
    }
};
