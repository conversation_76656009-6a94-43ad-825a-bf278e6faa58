<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('organisation_companies', function (Blueprint $table) {
            $table->text('print_block')->nullable();
            $table->text('print_line')->nullable();
        });

        $this->seedDefaultData();
    }

    public function down(): void
    {
        Schema::table('organisation_companies', function (Blueprint $table) {
            $table->dropColumn('print_block');
            $table->dropColumn('print_line');
        });
    }

    private function seedDefaultData(): void
    {
        if(\App\Models\Organisation\Company::count() === 0) {
            return;
        }

        $companyPrintArray = [
            'VSK' => [
                'block' => '<strong>VERMONT Slovakia s. r. o.</strong><br>IČO: 48 317 942<br>so sídlom: Vlčie Hrdlo 53, 821 07 Bratislava<br>zapísaná v Obchodnom registri Okresného súdu Bratislava I, Oddiel: Sro, Vlož<PERSON> č.: 106686/B<br>zastúpená: Ing. <PERSON>, konateľ',
                'line' => '<strong>VERMONT Slovakia s. r. o.</strong>, IČO: 48 317 942, so sídlom: Vlčie Hrdlo 53, 821 07 Bratislava, zapísaná  v Obchodnom registri Okresného súdu Bratislava I, Oddiel: Sro, Vložka č.: 106686/B',
            ],
            'VSERSK' => [
                'block' => '<strong>VERMONT Services Slovakia s. r. o.</strong><br>IČO:48 321 583<br>so sídlom: Vlčie Hrdlo 53, 821 07 Bratislava<br>zapísaná v Obchodnom registri Okresného súdu Bratislava I, Oddiel: Sro, Vložka č.: 106688/B<br>zastúpená: Ing. Peter Totka, konateľ',
                'line' => '<strong>VERMONT Services Slovakia s. r. o.</strong>, IČO: 48 321 583, so sídlom: Vlčie Hrdlo 53, 821 07 Bratislava, zapísaná  v Obchodnom registri Okresného súdu Bratislava I, Oddiel: Sro, Vložka č.: 106688/B',
            ],
            'GCECZ' => [
                'block' => '<strong>GANT Central Europe s.r.o.</strong>, IČ : 26212137, se sídlem Pohořelec 114/22, 118 00 Praha 1, společnost zapsaná v obchodním rejstříku vedeném Městským soudem v Praze, oddíl C, vložka 80135',
                'line' => 'GANT Central Europe s.r.o.<br>IČ : 26212137<br>se sídlem Pohořelec 114/22, 118 00 Praha 1<br>společnost zapsaná v obchodním rejstříku vedeném Městským soudem v Praze, oddíl C, vložka 80135<br>jednající Ing. Petrou Naumann, jednatelkou',
            ],
            'VCE' => [
                'block' => 'VERMONT Central Europe s.r.o.<br>IČ: 22174567<br>se sídlem Pohořelec 114/22, 118 00 Praha 1<br>společnost zapsaná v obchodním rejstříku vedeném Městským soudem v Praze, oddíl C, vložka 412064<br>jednající Ing. Petrou Naumann, jednatelkou',
                'line' => '<strong>VERMONT Central Europe s.r.o.</strong>, IČ: 22174567, se sídlem Pohořelec 114/22, 118 00 Praha 1, společnost zapsaná v obchodním rejstříku vedeném Městským soudem v Praze, oddíl C, vložka 412064',
            ],
            'ASH' => [
                'block' => 'VERMONT Hungary Kft., Margit u.27., 1023 Budapest<br>Cégjegyzék szám: Cg. 01-09-683453<br>Adószám: 11923053-2-41',
                'line' => 'VERMONT Hungary Kft., Margit u.27., 1023 Budapest, Cégjegyzék szám: Cg. 01-09-683453, Adószám: 11923053-2-41',
            ],
        ];

        $keys = array_keys($companyPrintArray);
        foreach(\App\Models\Organisation\Company::all() as $company) {
            if(!in_array($company->code, $keys, false)) {
                continue;
            }

            $company->update([
                'print_block' => $companyPrintArray[$company->code]['block'],
                'print_line' => $companyPrintArray[$company->code]['line'],
            ]);
        }
    }
};
