<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('remuneration_proposal_subtype_budgets', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('subtype')->index();
            $table->foreignId('centre_id')->constrained('organisation_centres');
            $table->date('validity')->index();
            $table->text('budget')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('remuneration_proposal_subtype_budgets');
    }
};
