<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organisation_centres', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('name');
            $table->foreignId('parent_id')->nullable()->constrained('organisation_centres');
            $table->foreignId('centre_type_id')->constrained('organisation_centre_types');
            $table->foreignId('company_id')->nullable()->constrained('organisation_companies');
            $table->foreignId('address_id')->nullable()->constrained('base_addresses');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->boolean('is_active')->default(false);
            $table->unsignedInteger('cdb_id')->nullable()->index();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('organisation_centres');
    }
};
