<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('remuneration_proposals', function (Blueprint $table) {
            $table->boolean('is_paid_via_agreement')->default(false)->after('contract_id');
        });
    }

    public function down(): void
    {
        Schema::table('remuneration_proposals', function (Blueprint $table) {
            $table->dropColumn('is_paid_via_agreement');
        });
    }
};
