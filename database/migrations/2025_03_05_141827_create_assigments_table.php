<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('person_assigments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('person_users');
            $table->foreignId('country_id')->nullable()->constrained('base_countries');
            $table->foreignId('position_id')->nullable()->constrained('organisation_positions');
            $table->foreignId('centre_id')->nullable()->constrained('organisation_centres');
            $table->unsignedTinyInteger('type');
            $table->unsignedTinyInteger('status');
            $table->date('valid_from_date')->nullable();
            $table->date('valid_to_date')->nullable();
            $table->boolean('is_from_hrms1')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('person_assigments');
    }
};
