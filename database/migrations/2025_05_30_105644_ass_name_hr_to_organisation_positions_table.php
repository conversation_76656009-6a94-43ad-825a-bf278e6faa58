<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('organisation_positions', function (Blueprint $table) {
            $table->string('name_hr')->nullable()->after('name');
        });
    }

    public function down(): void
    {
        Schema::table('organisation_positions', function (Blueprint $table) {
            $table->dropColumn('name_hr');
        });
    }
};
