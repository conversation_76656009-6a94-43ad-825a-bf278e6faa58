<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('remuneration_salary_plans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('person_users');
            $table->date('validity')->index();
            $table->text('base')->nullable();
            $table->text('addition')->nullable();
            $table->foreignId('currency_id')->constrained('base_currencies');
            $table->longText('note')->nullable();
            $table->foreignId('creator_id')->constrained('person_users');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('remuneration_salary_plans');
    }
};
