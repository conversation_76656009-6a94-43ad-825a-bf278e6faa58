<?php

use App\Models\Organisation\Position;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('remuneration_proposal_subtype_budget_excluded_positions', function (Blueprint $table) {
            $table->id();
//            $table->unsignedBigInteger('position_id');
            $table->foreignId('position_id')->constrained('organisation_positions')->index('organisation_positions_position_id');
            $table->unsignedTinyInteger('subtype');
            $table->timestamps();
        });

        $this->seedDefaultData();
    }

    public function down(): void
    {
        Schema::dropIfExists('remuneration_proposal_subtype_budget_excluded_positions');
    }

    private function seedDefaultData() : void
    {
        if(Position::count() === 0) {
            return;
        }

        $positionSm = Position::where('code', 'SM')->first();
        \App\Models\Remuneration\ProposalSubTypeBudgetExcludedPosition::create([
            'position_id' => $positionSm->id,
            'subtype' => \App\Enums\Remuneration\ProposalSubType::F_ACTIVATION->value,
        ]);
        \App\Models\Remuneration\ProposalSubTypeBudgetExcludedPosition::create([
            'position_id' => $positionSm->id,
            'subtype' => \App\Enums\Remuneration\ProposalSubType::F_FULFILLMENT_OF_MONTH->value,
        ]);

        $positionSmsp = Position::where('code', 'SMSP')->first();
        \App\Models\Remuneration\ProposalSubTypeBudgetExcludedPosition::create([
            'position_id' => $positionSmsp->id,
            'subtype' => \App\Enums\Remuneration\ProposalSubType::F_ACTIVATION->value,
        ]);
        \App\Models\Remuneration\ProposalSubTypeBudgetExcludedPosition::create([
            'position_id' => $positionSmsp->id,
            'subtype' => \App\Enums\Remuneration\ProposalSubType::F_FULFILLMENT_OF_MONTH->value,
        ]);
    }
};
