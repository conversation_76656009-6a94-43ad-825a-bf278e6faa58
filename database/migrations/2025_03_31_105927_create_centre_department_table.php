<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organisation_centre_department', function (Blueprint $table) {
            $table->primary(['centre_id','department_id']);
            $table->foreignId('centre_id')->constrained('organisation_centres');
            $table->foreignId('department_id')->constrained('organisation_departments');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('organisation_centre_department');
    }
};
