<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('person_user_work_datas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('person_users');
            $table->string('email')->nullable()->index();
            $table->string('email_password')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->text('photo')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('person_user_work_datas');
    }
};
