<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('person_users', function (Blueprint $table) {
            $table->unsignedInteger('hrms_id')->nullable()->index()->after('phone');
        });
    }

    public function down(): void
    {
        Schema::table('person_users', function (Blueprint $table) {
            $table->dropColumn('hrms_id');
        });
    }
};
