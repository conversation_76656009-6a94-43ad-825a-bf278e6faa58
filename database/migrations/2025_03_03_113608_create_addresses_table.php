<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('base_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('country_id')->constrained('base_countries');
            $table->foreignId('city_id')->constrained('base_cities');
            $table->string('city_section')->nullable();
            $table->string('shopping_center')->nullable();
            $table->string('street')->nullable();
            $table->string('number')->nullable();
            $table->string('zip')->nullable();
            $table->unsignedInteger('cdb_id')->nullable()->index();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('base_addresses');
    }
};
