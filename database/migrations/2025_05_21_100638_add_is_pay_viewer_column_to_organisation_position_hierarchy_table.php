<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('organisation_position_hierarchy', function (Blueprint $table) {
            $table->boolean('is_pay_viewer')->default(false)->after('is_centre_pay_approver');
        });
    }

    public function down(): void
    {
        Schema::table('organisation_position_hierarchy', function (Blueprint $table) {
            $table->dropColumn('is_pay_viewer');
        });
    }
};
