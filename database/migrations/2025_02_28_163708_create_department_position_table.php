<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organisation_department_position', function (Blueprint $table) {
            $table->primary(['department_id','position_id']);
            $table->foreignId('department_id')->constrained('organisation_departments');
            $table->foreignId('position_id')->constrained('organisation_positions');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('organisation_department_position');
    }
};
