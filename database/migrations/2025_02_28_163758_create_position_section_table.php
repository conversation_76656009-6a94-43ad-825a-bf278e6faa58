<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organisation_position_section', function (Blueprint $table) {
            $table->primary(['position_id', 'section_id']);
            $table->foreignId('position_id')->constrained('organisation_positions');
            $table->foreignId('section_id')->constrained('organisation_sections');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('organisation_position_section');
    }
};
