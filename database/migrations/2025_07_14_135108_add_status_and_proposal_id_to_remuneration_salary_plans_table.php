<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('remuneration_salary_plans', function (Blueprint $table) {
            $table->tinyInteger('status')->after('id')->default(\App\Enums\Remuneration\SalaryPlanStatus::PLANNED->value);
            $table->foreignId('proposal_id')->nullable()->constrained('remuneration_proposals');
        });
    }

    public function down(): void
    {
        Schema::table('remuneration_salary_plans', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropForeign(['proposal_id']);
            $table->dropColumn('proposal_id');
        });
    }
};
