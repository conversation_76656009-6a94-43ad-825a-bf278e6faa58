<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('person_contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('person_users');
            $table->foreignId('company_id')->constrained('organisation_companies');
            $table->unsignedTinyInteger('status');
            $table->unsignedTinyInteger('type');
            $table->boolean('is_primary')->default(false);
            $table->decimal('percentage', 7, 4)->default(100);
            $table->date('sign_date')->nullable();
            $table->date('start_date')->nullable();
            $table->date('trial_date')->nullable();
            $table->date('definite_date')->nullable();
            $table->date('end_notice_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('payslip_code')->nullable();
            $table->boolean('is_discount')->default(false);
            $table->boolean('is_archived')->default(false);
            $table->unsignedInteger('hrms_id');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('person_contracts');
    }
};
