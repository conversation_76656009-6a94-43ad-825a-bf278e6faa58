<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organisation_centre_section', function (Blueprint $table) {
            $table->primary(['centre_id','section_id']);
            $table->foreignId('centre_id')->constrained('organisation_centres');
            $table->foreignId('section_id')->constrained('organisation_sections');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('centre_section');
    }
};
